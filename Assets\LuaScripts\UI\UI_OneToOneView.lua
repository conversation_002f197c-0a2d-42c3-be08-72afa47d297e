local UI_OneToOneView = Class(BaseView)
local SlideRect = require("UI.Common.SlideRect")
local ItemBase = require("UI.Common.BaseSlideItem")
local GoGiftItem = require("UI.GoGiftItem")
local RankItem = Class(ItemBase)

local rankIconPath = "Sprite/ui_activity_1V1duijue/duijue_win1_paihang_number%s.png"
local CanvasSorting  -- 画布层级排序
local CanvasTemp     -- 临时画布组件
local ShowListTemp   -- 临时显示排行榜奖励列表

local SingleRound = {
    Time = 1,           -- 单轮时间
    Cost = 2,           -- 单轮报名费用
    WinReward = 3,      -- 单轮胜利奖励
    SearchingTime = 4,  -- 单轮匹配时间
    WinChatBubble = 5,  -- 胜方聊天气泡内容
    FailChatBubble = 6, -- 败方聊天气泡内容
}
local RefreshUIType = {
    Searching = 1,    -- 搜索对手
    Playing = 2,      -- 比拼中
    ResultWin = 3,    -- 结算胜利
    ResultDraw = 4,   -- 结算平手
    ResultFail = 5,   -- 结算失败
    TopicGift = 6,    -- 主题礼包
    RobotScore = 7,   -- 机器人积分
    FlyScore = 8,     -- 飞积分
    RequestRank = 9,  -- 请求排行榜数据
    GetWinStreakReward = 10, -- 获取连胜奖励后返回
    OpenGoArraw = 11, -- 打开前往箭头
    AutoStart = 12,   -- 自动开始匹配
}
local ButtonGroup = {
    btnStart = 1,
    btnGo = 2,
    btnWin = 3,
    btnFail = 4
}

local countDownMin = 5        -- 倒计时随机范围 Min
local countDownMax = 10       -- 倒计时随机范围 Max
local countDownMaxShow = 30   -- 倒计时显示的最大值

function UI_OneToOneView:OnInit()

end

function UI_OneToOneView:OnCreate(ispush)
    -- 设置活动 id 和活动对象
    local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.OneToOne)
    self.activityItem = activityItem
    if not self.activityItem then
        SetActive(self.ui.m_btnStart, false)
        return
    end

    local headPos = Vector2.New(-0.3, -0.5)
    CreateCommonHeadAsync(self.ui.m_goMyHeadNode.transform, 0.9, headPos, function (go, trans)
        self.myHeadNode = go
        self:RefreshPlayerInfo() -- 刷新玩家信息
    end)
    CreateCommonHeadAsync(self.ui.m_goOtherHeadNode.transform, 0.9, headPos, function (go, trans)
        self.otherHeadNode = go
    end)

    self:InitButtonGroup()
    self.ui.m_togAutoStart.isOn = NetOneToOneData:GetDataByKey("autoStart")
    self.ui.m_togAutoStart.onValueChanged:AddListener(function (isOn)
        NetOneToOneData:SetDataByKey("autoStart", isOn)
        NetOneToOneData:TrackEventAutoStart("UI_OneToOneView_Toggle")
    end)
    --直购礼包入口
    local function callBack(obj)
        self.directBuyGo = obj
    end
    GoGiftItem:Create(self.ui.m_goGiftBorder,ActivityTotal.OneToOne,nil,callBack)
    -- 检查比拼是否结束
    NetOneToOneData:CheckCompetitionEnd()
    self.nowScore = 0                     -- 通行证当前分数
    self.isPush = ispush                  -- 是否被推送打开
    self.availableHead = {}               -- 可用的头像
    self.availableBorder = {}             -- 可用头像框
    self.clearScreenAnim = GetComponent(self.uiGameObject, UE.Animation)
    self.isPlayingClearScreen = false
    self.canRequestRankData = true
    self.canClose = true
    self.winningRewardItemList = {}
    NetOneToOneData:ComputeRobotScore()   -- 计算机器人积分
    self:InitRobotHead()                  -- 填充可用的头像
    self:InitPanel()                      -- 初始化界面
    local competitionResult = NetOneToOneData:GetDataByKey("competitionResult")
    if competitionResult > 0 then
        UI_UPDATE(UIDefine.UI_OneToOneView, competitionResult)
    end
    -- 新手引导
    local isGuide = NetGlobalData:GetActivityGuideCache(ActivityTotal.OneToOne)
    if not isGuide then
        NetGlobalData:SetActivityGuideCache(ActivityTotal.OneToOne)
        self:Guide()
    end
    self:SetIsUpdateTick(true)
    self:InitPointsContent()
    -- 播放开场动画
    PlayAnimStatusIndex(self.clearScreenAnim, "onetoone_clearscreen")
	--self:SetGiftRed()

    self:SetCustomCloseFun(true)

    self.ui.m_rtransArrowAct:DOAnchorPos(Vector2.New(24, 0), 0.5):SetLoops(-1, LoopType.Yoyo)

    -- 至少完成一次比拼
    local completeOnce = NetOneToOneData:GetDataByKey("completeOnce")
    if completeOnce then
        -- 初始请求一次
        self:RequestRankData()
    end

    -- 检查自动开始（通过提示弹窗触发）
    self:CheckAutoStartByTip()
end

function UI_OneToOneView:SetGiftRed()
	SetActive(self.ui.m_goGiftRed,not NetOneToOneData.data.getFreeGift)
end

function UI_OneToOneView:OnRefresh(type, time)
    -- 刷新搜索对手倒计时
    if type == RefreshUIType.Searching then
        local searchingTotalTime = NetOneToOneData:GetDataByKey("searchingTotalTime")
        local elapsedTime = searchingTotalTime - time
        local showTime = countDownMaxShow - elapsedTime
        self.ui.m_txtSearching.text = TimeMgr:ConverSecondToString(showTime)
        if time <= 0 then
            SetActive(self.ui.m_goSearching, false)
            self:StopSearching()
            TimeMgr:DestroyTimer(UIDefine.UI_OneToOneView, self.headTimer)
        end
    -- 刷新比拼倒计时
    elseif type == RefreshUIType.Playing then
        self.ui.m_txtGameCountDown.text = TimeMgr:ConverSecondToString(time)
        if time <= 0 then
            SetActive(self.ui.m_btnGo, false)
        end
    -- 结算（胜利）
    elseif type == RefreshUIType.ResultWin then
        self.ui.m_txtResultWin.text = LangMgr:GetLang(8807)
        self:ShowWinOrFailText(true)
        SetActive(self.ui.m_goWinTip, true)
        SetActive(self.ui.m_goWinRewardBg, true)
        SetActive(self.ui.m_btnFullScreen, true)
        if NetOneToOneData.winningChangeScore > 0 then
            EffectConfig:CreateEffect(141, 0, 0, 0, self.ui.m_transEffect, function()
                self:SortOrderAllCom(true)
            end)
        end
        self:ShowButton(ButtonGroup.btnWin)
        SetActive(self.ui.m_goVS, true)
        SetActive(self.ui.m_goSwordEffect, false)
        SetActive(self.ui.m_goWinGoalBG, false)
        -- 至少完成一次比拼
        local completeOnce = NetOneToOneData:GetDataByKey("completeOnce")
        if completeOnce then
            -- 初始请求一次
            self:RequestRankData()
        end
        SetActive(self.ui.m_goTopicBorderParent, false)
        SetActive(self.ui.m_goLight, true)
        self:ShowChatBubble(true)
    -- 结算（平手）
    elseif type == RefreshUIType.ResultDraw then
        self.ui.m_txtResultFail.text = LangMgr:GetLang(8808)
        self:ShowWinOrFailText(false)
        SetActive(self.ui.m_goWinTip, true)
        SetActive(self.ui.m_goWinRewardBg, true)
        self:RefreshWinReward(false)
        SetActive(self.ui.m_btnFullScreen, true)
        self:ShowButton(ButtonGroup.btnFail)
        SetActive(self.ui.m_goVS, true)
        SetActive(self.ui.m_goSwordEffect, false)
        SetActive(self.ui.m_goWinGoalBG, false)
        -- 至少完成一次比拼
        local completeOnce = NetOneToOneData:GetDataByKey("completeOnce")
        if completeOnce then
            -- 初始请求一次
            self:RequestRankData()
        end
        -- 连胜中断飘字
        if NetOneToOneData.winningChangeScore < 0 then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(8890))
            NetOneToOneData.winningChangeScore = 0
        end
        -- 置灰效果
        self:SetGrey(true)
        SetActive(self.ui.m_goTopicBorderParent, false)
        SetActive(self.ui.m_goLight, true)
        self:ShowChatBubble(false)
        self:CheckStreakBreak()
    -- 结算（失败）
    elseif type == RefreshUIType.ResultFail then
        self.ui.m_txtResultFail.text = LangMgr:GetLang(8809)
        self:ShowWinOrFailText(false)
        SetActive(self.ui.m_goWinTip, true)
        SetActive(self.ui.m_goWinRewardBg, true)
        self:RefreshWinReward(false)
        SetActive(self.ui.m_btnFullScreen, true)
        self:ShowButton(ButtonGroup.btnFail)
        SetActive(self.ui.m_goVS, true)
        SetActive(self.ui.m_goSwordEffect, false)
        SetActive(self.ui.m_goWinGoalBG, false)
        -- 至少完成一次比拼
        local completeOnce = NetOneToOneData:GetDataByKey("completeOnce")
        if completeOnce then
            -- 初始请求一次
            self:RequestRankData()
        end
        -- 连胜中断飘字
        if NetOneToOneData.winningChangeScore < 0 then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(8890))
            NetOneToOneData.winningChangeScore = 0
        end
        -- 置灰效果
        self:SetGrey(true)
        SetActive(self.ui.m_goTopicBorderParent, false)
        SetActive(self.ui.m_goLight, true)
        self:ShowChatBubble(false)
        self:CheckStreakBreak()
    -- 主题礼包
    elseif type == RefreshUIType.TopicGift then
        SetActive(self.ui.m_goGiftBorder, true)
        self.ui.m_txtGiftCountDown.text = TimeMgr:ConverSecondToString(time)
        if time <= 0 then
            SetActive(self.ui.m_goGiftBorder, false)
        end
    -- 机器人积分
    elseif type == RefreshUIType.RobotScore then
        self:RefreshScore()
    -- 飞积分
    elseif type == RefreshUIType.FlyScore then
        self:FlyScore()
        self:FlyScoreText()
    -- 请求排行榜数据
    elseif type == RefreshUIType.RequestRank then
        self:RequestRankData()
    -- 获取连胜奖励后返回
    elseif type == RefreshUIType.GetWinStreakReward then
        self:GetWinningStreakReward();
    -- 打开前往箭头
    elseif type == RefreshUIType.OpenGoArraw then
        local competitionResult = NetOneToOneData:GetDataByKey("competitionResult")
        if competitionResult <= 0 then
            SetActive(self.ui.m_goArrow, true);
        end
	elseif type == 66 then
		self.ui.m_txtRank.text = time
	elseif type == 67 then
		self:SetGiftRed()
    elseif type == 68 then
        self:CloseSelf();
        return;
    elseif type == RefreshUIType.AutoStart then
        self:CheckAutoStartByTip()
    end
    SetActive(self.ui.m_goPassPortRed, NetOneToOneData:IsShowPassportRed())
end

function UI_OneToOneView:TickUI(deltaTime)
    local time = self:GetActiveTime()
    if time and time > 0 then
        self.ui.m_txtActivityTime.text = TimeMgr:BaseTime(time, 3, 2)
    else
        self.ui.m_txtActivityTime.text = LangMgr:GetLang(7077)
    end
    -- 报名费用（首次报名免费）
    local isCostFirstSignUp = NetOneToOneData:GetDataByKey("isCostFirstSignUp")
    -- 已消耗首次报名
    if isCostFirstSignUp then
        -- 显示报名费用
        local costID, costNum = self:GetStartCost()
        self.ui.m_txtStartCost.text = tostring(costNum)
        -- 当前货币数量不足
        local currentCurrency = NetUpdatePlayerData:GetResourceNumByID(costID)
        if currentCurrency < costNum then
            -- 费用文本显示红色
            self.ui.m_txtStartCost.text = string.format("<color=%s>%s</color>", "red", costNum)
        end
    end
    self:ActiveCompetitionTime()
end

function UI_OneToOneView:onDestroy()
    -- 清理变量
    self.myHeadNode = nil
    self.otherHeadNode = nil
    self.activityId = nil
    self.activityItem = nil
    self.availableHead = nil
    self.availableBorder = nil
    self.btnGroup = nil
    self.clearScreenAnim = nil
    self.isPlayingClearScreen = nil
    self.canRequestRankData = nil
    self.rankSlideRect = nil
    self.imgPlayerRankReward = nil
    self.playerRewardItemList = nil
    self.playerRanking = nil
    self.winningRewardItemList = nil
    self.nowScore = nil
    if self.rankItemList then
        for i = 1, #self.rankItemList do
            self.rankItemList[i]:onDestroy()
        end
    end
    self.rankItemList = nil
    -- 清理计时器
    TimeMgr:DestroyTimer(UIDefine.UI_OneToOneView)
    -- 清理动画
    if self.txtWinningFlyScoreTween then
        self.txtWinningFlyScoreTween:Kill()
        self.txtWinningFlyScoreTween = nil
    end
    if self.txtPassPortFlyScoreTween then
        self.txtPassPortFlyScoreTween:Kill()
        self.txtPassPortFlyScoreTween = nil
    end
    if self.txtRankFlyScoreTween then
        self.txtRankFlyScoreTween:Kill()
        self.txtRankFlyScoreTween = nil
    end
    if self.txtScoreBlueTween then
        self.txtScoreBlueTween:Kill()
        self.txtScoreBlueTween = nil
    end
    if self.txtScoreRedTween then
        self.txtScoreRedTween:Kill()
        self.txtScoreRedTween = nil
    end
    if self.delayCloseTween then
        self.delayCloseTween:Kill()
        self.delayCloseTween = nil
    end
    if self.delayHideFlyScoreTween then
        self.delayHideFlyScoreTween:Kill()
        self.delayHideFlyScoreTween = nil
    end
    -- 清理推送
    if self.isPush then
        UI_CLOSE(UIDefine.UI_GuideMask)
    end
    self:SetIsUpdateTick(false)
end

function UI_OneToOneView:onUIEventClick(go)
    if self.isPlayingClearScreen then return end
    local name = go.name
    -- 点击其他位置关闭排行榜
    if name ~= "m_btnRank" and self.ui.m_goRankList.activeSelf then
        SetActive(self.ui.m_goRankList, false)
    end
    if self.ui.m_goArrow.activeSelf and (name == "m_btnStart" or name == "m_btnGo"
        or name == "m_btnWin" or name == "m_btnFail") then
        SetActive(self.ui.m_goArrow, false)
    end
    self:CloseShowList()
    -- 关闭界面
    if name == "m_btnClose" then
        self:CloseSelf()
    -- 打开帮助
    elseif name == "m_btnHelp" then
        UI_SHOW(UIDefine.UI_OneToOneHelp)
    -- 打开通行证
    elseif name == "m_btnPassPort" then
        UI_SHOW(UIDefine.UI_OneToOnePassport)
    -- 打开排行榜
    elseif name == "m_btnRank" then
        if self.ui.m_goRankList.activeSelf then
            SetActive(self.ui.m_goRankList, false)
        else
            self:ShowRank()
        end
    -- 玩家排行榜奖励
    elseif name == "m_btnPlayerRank" then
        self:ShowPlayerRankReward()
        SetActive(self.ui.m_goRankList, true)
    elseif name == "m_btnRankListBg" then
        self:CloseShowList()
        SetActive(self.ui.m_goRankList, true)
    -- 打开主题说明
    elseif name == "m_btnTopicEmpty" then
        UI_SHOW(UIDefine.UI_OneToOneHelp)
    -- 打开当前主题说明
    elseif name == "m_btnTopic" then
        UI_SHOW(UIDefine.UI_OneToOneTopicTip)
    -- 打开主题礼包
    elseif name == "m_btnGiftIcon" then
        UI_SHOW(UIDefine.UI_OneToOneTopicGift)
    -- 领取连胜奖励
    elseif name == "m_btnWinningReward" then
        UI_SHOW(UIDefine.UI_OneToOneWinStreak)
    -- 开始按钮
    elseif name == "m_btnStart" then
        self:StartGame()
    -- 前往按钮
    elseif name == "m_btnGo" then
        self:GoToScene()
    -- 胜利按钮
    elseif name == "m_btnWin" then
        if self.singleRoundTimer then
            TimeMgr:DestroyTimer(UIDefine.UI_OneToOneView, self.singleRoundTimer)
        end
        SetActive(self.ui.m_goSingleRoundTime, false)
        NetOneToOneData:ResetCompetitionData()
        self:GetWinReward()
    -- 失败按钮
    elseif name == "m_btnFail" then
        if self.singleRoundTimer then
            TimeMgr:DestroyTimer(UIDefine.UI_OneToOneView, self.singleRoundTimer)
        end
        SetActive(self.ui.m_goSingleRoundTime, false)
        NetOneToOneData:AddPassportAndRankScore()
        NetOneToOneData:ResetCompetitionData()
        SetActive(self.ui.m_goFlyScore, true)
        self:RefreshFlyScoreText()
        SetActive(self.ui.m_goWinningFlyScore, false)
        SetActive(self.ui.m_imgWinningBg, false)
        self:FlyScore()
        self:InitPanel()
        self:DelayHideFlyScore()
    elseif name == "m_btnWinInfo" then
        UI_SHOW(UIDefine.UI_OneToOnePointTips)
    elseif name == "m_btnFullScreen" then
        local competitionResult = NetOneToOneData:GetDataByKey("competitionResult")
        if competitionResult > 0 then
            self:AutoCompetitionResult()
            self:DelayClose()
        end
    end
end

--- 初始化界面
function UI_OneToOneView:InitPanel()
    if not self.ui then return end
    -- 报名费用（首次报名免费）
    local isCostFirstSignUp = NetOneToOneData:GetDataByKey("isCostFirstSignUp")
    -- 已消耗首次报名
    if isCostFirstSignUp then
        -- 显示报名费用
        local costID, costNum = self:GetStartCost()
        local cfg = ConfigMgr:GetDataByID(ConfigDefine.ID.item, costID)
        if cfg then
            SetUIImage(self.ui.m_imgStartCost, cfg.icon_b, false)
        end
        self.ui.m_txtStartCost.text = tostring(costNum)

        -- 当前货币数量不足
        local currentCurrency = NetUpdatePlayerData:GetResourceNumByID(costID)
        if currentCurrency < costNum then
            -- 费用文本显示红色
            self.ui.m_txtStartCost.text = string.format("<color=%s>%s</color>", "red", costNum)
        end
    end
    SetActive(self.ui.m_txtStartFree, not isCostFirstSignUp)
    SetActive(self.ui.m_goStartCost, isCostFirstSignUp)
    SetActive(self.ui.m_btnGo, false)

    local searchingTimeData = OneToOneManager:GetSingleRoundSetting(SingleRound.SearchingTime)
    local timeStr = searchingTimeData.value
    local timeTable = string.split(timeStr, ";")
    countDownMin = v2n(timeTable[1]) or 5
    countDownMax = v2n(timeTable[2]) or 10

    local isSignUp = NetOneToOneData:GetDataByKey("isSignUp")
    -- 已报名
    if isSignUp then
        self:ShowButton(ButtonGroup.btnGo)
        SetActive(self.ui.m_goSearching, false)
        self:StopSearching()
    -- 未报名
    else
        self:ShowButton(ButtonGroup.btnStart)
        --SetActive(self.ui.m_imgHeadRight, false)

        if self.otherHeadNode then
            SetActive(self.otherHeadNode, false)
        end
        SetActive(self.ui.m_imgHeadRightEmpty, true)
        self.ui.m_txtNameRight.text = ""
        SetActive(self.ui.m_goSwordEffect, false)
        SetActive(self.ui.m_goWinGoalBG, false)
    end
    -- 显示奖励
    self:RefreshWinReward(true)

    SetActive(self.ui.m_goWinTip, false)
    SetActive(self.ui.m_goWinRewardBg, true)
    -- 显示当前比拼分数
    self:RefreshScore()
    -- 取消置灰
    self:SetGrey(false)
    SetActive(self.ui.m_goTopicBorderParent, true)
    SetActive(self.ui.m_goLight, true)
    SetActive(self.ui.m_imgChatBubbleLeft, false)
    SetActive(self.ui.m_imgChatBubbleRight, false)
    SetActive(self.ui.m_rtransArrowPos, false)
    SetActive(self.ui.m_btnFullScreen, false)

    local competitionResult = NetOneToOneData:GetDataByKey("competitionResult")
    if competitionResult > 0 then
        self:RefreshScoreResult(true)
    else
        self:RefreshScoreResult(false)
    end
end

function UI_OneToOneView:RefreshWinReward(isWin)
    -- 获胜奖励
    local icon = "Sprite/ui_public/Resource_jifen_duijue.png"
    local winNow = NetOneToOneData:GetDataByKey("winningStreak")
    self.ui.m_txtWinRewardTitle.text = isWin and LangMgr:GetLangFormat(8803, winNow) or LangMgr:GetLang(28)
    local rewardList = self:GetWinRewardList()
    for key, value in ipairs(rewardList) do
        local rewardTable = string.split(value, "|")
        local rewardID = v2n(rewardTable[1])
        local rewardNum = v2n(rewardTable[2])
        local text = GetChild(self.ui.m_goWinRewardBg, "winRewardParent/m_txtWinReward" .. key, UEUI.Text)
        text.text = tostring(rewardNum)
        local cfg = ConfigMgr:GetDataByID(ConfigDefine.ID.item, rewardID)
        if cfg then
            local image = GetChild(text, "m_imgWinReward" .. key, UEUI.Image)
            SetUIImage(image, cfg.icon_b, false)
            if rewardID == 2 or rewardID == 3 then
                SetUISize(image, 92, 92)
            else
                SetUISize(image, 72, 72)
            end
        end
        SetActive(text, isWin)
    end

    -- 积分奖励
    local offset = isWin and 1 or 0
    local winNum = NetOneToOneData:GetDataByKey("winningStreak") + offset
    local config = OneToOneManager:GetVictoryConfig(winNum)
    if config then
        self.ui.m_txtWinReward3.text = tostring(config.passport_points)
        self.ui.m_txtWinReward4.text = tostring(config.rank_points)
    end

    local cfg3 = ConfigMgr:GetDataByID(ConfigDefine.ID.item, 55003)
    if cfg3 then
        SetUIImage(self.ui.m_imgWinReward3, cfg3.icon_b, false)
    end

    local cfg4 = ConfigMgr:GetDataByID(ConfigDefine.ID.item, 55004)
    if cfg4 then
        SetUIImage(self.ui.m_imgWinReward4, cfg4.icon_b, false)
    end
end

function UI_OneToOneView:RefreshScoreResult(isOld)

    local maxCount = self.activityItem.form.max
    local energySch = self.activityItem.info.energySch + 1
    energySch = math.min(energySch, maxCount)
    local totalRankScore = NetOneToOneData:GetDataByKey("totalRankScore")
    local winningStreak = NetOneToOneData:GetDataByKey("winningStreak")
    local winningStreakMax = NetOneToOneData:GetDataByKey("winningStreakMax")
    if isOld then
        energySch = self.activityItem.info.energySch
        totalRankScore = NetOneToOneData.totalRankScoreOld or totalRankScore
        winningStreak = NetOneToOneData.winningStreakOld or winningStreak
        winningStreakMax = NetOneToOneData.winningStreakMaxOld or winningStreakMax
    end

    -- 刷新通行证
    self:RefreshPassport()
    self.ui.m_txtPassPortLevel.text = tostring(energySch)
    SetActive(self.ui.m_goPassPortRed, NetOneToOneData:IsShowPassportRed())
    -- 刷新排行榜积分
    self.ui.m_txtMyRankScore.text = tostring(totalRankScore)
    -- 刷新连胜奖励
    local nextData = NetOneToOneData:GetNextWinningStreak()
    SetActive(self.ui.m_goWinningStreakRed, false)
    if nextData then
        local goal = nextData.win
        SetUIImage(self.ui.m_imgWinningReward, nextData.icon, false)

        local nextConfig = NetOneToOneData:GetNextNotReachWinStreak()
        if nextConfig then
            if winningStreak <= nextConfig.win then
                self.ui.m_txtWinningProgress.text = string.format("%s/%s", winningStreak, nextConfig.win)
                self.ui.m_sliderWinningStreak.value = winningStreak / nextConfig.win
            else
                self.ui.m_txtWinningProgress.text = string.format("%s/%s", nextConfig.win, nextConfig.win)
                self.ui.m_sliderWinningStreak.value = nextConfig.win / nextConfig.win
            end
        end
        if winningStreakMax >= goal then
            SetActive(self.ui.m_goWinningEffect, true)
            SetActive(self.ui.m_goWinningStreakRed, true)
            local anim = GetComponent(self.ui.m_imgWinningReward, UE.Animation)
            if anim then
                anim:Play("activity_itemicon")
            end
        else
            self:ResetWinningStreakRewardAnim()
        end
        SetActive(self.ui.m_imgWinningRewardCheck, false)
        self.ui.m_txtWinningStreak.text = LangMgr:GetLangFormat(8887) .. " ".. nextData.id
    else
        local lastData = NetOneToOneData:GetLastWinningStreak()
        SetUIImage(self.ui.m_imgWinningReward, lastData.icon, false)
        SetActive(self.ui.m_imgWinningRewardCheck, true)
        self.ui.m_txtWinningProgress.text = string.format("%s/%s", lastData.win, lastData.win)
        self.ui.m_sliderWinningStreak.value = 1
        self:ResetWinningStreakRewardAnim()
        self.ui.m_txtWinningStreak.text = LangMgr:GetLangFormat(8887) .. " ".. lastData.id
    end
end

--- 刷新通行证积分进度
function UI_OneToOneView:RefreshPassport()
    local nowScore, nextScore = NetOneToOneData:GetPassportScore()
    self.nowScore = nowScore
    local showNowScore = nowScore
    if showNowScore > nextScore then
        showNowScore = nextScore
    end
    self.ui.m_txtPassPort.text = string.format("%d/%d", showNowScore, nextScore)
    self.ui.m_sliderPassPort.value = self.nowScore / nextScore
end

--- 初始化可用的机器人头像
function UI_OneToOneView:InitRobotHead()
    local allHeadConfigs = ConfigMgr:GetData(ConfigDefine.ID.head_set)
    for _, value in pairs(allHeadConfigs) do
        -- 筛选可用于活动随机显示的头像
        if v2n(value.kind) == 1 and value.show == 1 then
            table.insert(self.availableHead, value)
        end

        -- 筛选可用于活动随机显示的头像框
        if v2n(value.kind) == 2 and value.show == 1 then
            table.insert(self.availableBorder, value)
        end
        
    end
end

--- 初始化按钮组
function UI_OneToOneView:InitButtonGroup()
    self.btnGroup = {
        [1] = self.ui.m_btnStart,
        [2] = self.ui.m_btnGo,
        [3] = self.ui.m_btnWin,
        [4] = self.ui.m_btnFail
    }
end

--- 显示按钮
--- @param index number 按钮索引
function UI_OneToOneView:ShowButton(index)
    if IsTableEmpty(self.btnGroup) then
        self:InitButtonGroup()
    end
    for key, btn in pairs(self.btnGroup) do
        SetActive(btn, key == index)
    end
end

--- 设置随机头像
--- @param image any 图片组件
function UI_OneToOneView:SetRandomHead(image)
    local randomHead = Random(1, #self.availableHead)
    local icon = self.availableHead[randomHead].icon
    SetUIImage(image, icon, false)
end

--获取随机的头像路径
function UI_OneToOneView:GetRandomHead()
    local index = Random(1, #self.availableHead)
    return self.availableHead[index].id
end

--获取随机的头像框路径
function UI_OneToOneView:GetRandomBorder()
    local index = Random(1, #self.availableBorder)
    return self.availableBorder[index].id
end

--- 刷新玩家信息
function UI_OneToOneView:RefreshPlayerInfo()
    -- 刷新玩家头像
    local playerHead = NetUpdatePlayerData:GetPlayerInfo().head
    local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, playerHead)
    --SetUIImage(self.ui.m_imgHeadLeft, headConfig["icon"], false)

    if self.myHeadNode then
        SetMyHeadAndBorderByGo(self.myHeadNode)
    end

    SetUIImage(self.ui.m_imgPlayerRankHead, headConfig["icon"], false)
    -- 刷新玩家名字
    if NetUpdatePlayerData:GetPlayerInfo().name then
        self.ui.m_txtNameLeft.text = NetUpdatePlayerData:GetPlayerInfo().name
    else
        self.ui.m_txtNameLeft.text = "player-" .. NetUpdatePlayerData:GetPlayerInfo().id
    end
end

--- 获取单轮报名费用
--- @return number|nil costID 货币 ID
--- @return number|nil costNum 货币数量
function UI_OneToOneView:GetStartCost()
    local costData = OneToOneManager:GetSingleRoundSetting(SingleRound.Cost)
    local costTable = string.split(costData.value, "|")
    local costID = v2n(costTable[1])
    local costNum = v2n(costTable[2])
    return costID, costNum
end

--- 获取单轮胜利奖励
--- @return table rewardList 奖励列表
function UI_OneToOneView:GetWinRewardList()
    local rewardData = OneToOneManager:GetSingleRoundSetting(SingleRound.WinReward)
    local rewardList = string.split(rewardData.value, ";")
    return rewardList
end

--- 开始游戏
function UI_OneToOneView:StartGame()
    if not self.activityItem then return end
    -- 距离活动结束剩余时间是否足够
    local timeData = OneToOneManager:GetSingleRoundSetting(SingleRound.Time)
    local singleRoundTime = v2n(timeData.value)
    local activityRemainTime = self.activityItem:GetRemainingTimeReal()
    if activityRemainTime < singleRoundTime then
        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(8810))
        return
    end
    -- 消耗货币（首次报名免费）
    local isCostFirstSignUp = NetOneToOneData:GetDataByKey("isCostFirstSignUp")
    if isCostFirstSignUp then
        local costID, costNum = self:GetStartCost()
        local currentCurrency = NetUpdatePlayerData:GetResourceNumByID(costID)
        -- 当前的货币数量足够扣除
        if currentCurrency >= costNum then
            NetUpdatePlayerData:AddResourceNumByID(costID, -costNum, nil, "UI_OneToOneView")
        -- 当前货币数量不足
        else
            -- 打开金币商店
            UI_SHOW(UIDefine.UI_Market, "Money")
            return
        end
    else
        NetOneToOneData:SetDataByKey("isCostFirstSignUp", true)
    end
    NetOneToOneData:SetDataByKey("isSignUp", true)
    -- 匹配倒计时
    SetActive(self.ui.m_btnStart, false)
    SetActive(self.ui.m_goSearching, true)
    self:ReadyToSearch(0)
    if not self.ui.m_togAutoStart.isOn then
        SetActive(self.ui.m_goFlyScore, false)
    end

    -- 重置分数文本
    if self.txtScoreBlueTween then
        self.txtScoreBlueTween:Kill()
        self.txtScoreBlueTween = nil
    end
    self.ui.m_txtScoreBlue.text = 0
    SetActive(self.ui.m_txtScoreBlueAdd, false)

    if self.txtScoreRedTween then
        self.txtScoreRedTween:Kill()
        self.txtScoreRedTween = nil
    end
    self.ui.m_txtScoreRed.text = 0
    SetActive(self.ui.m_txtScoreRedAdd, false)

    self.ui.m_sliderVS:DOKill()
    self.ui.m_sliderVS.value = 0.5

    local x = self.ui.m_rtransVS.anchoredPosition.x
    local y = self.ui.m_rtransVS.anchoredPosition.y
    SetUIPos(self.ui.m_goVS, x, y)
end

--- 准备搜索对手
--- @param remainTime number 剩余时间
function UI_OneToOneView:ReadyToSearch(remainTime)
    local searchingTimeData = OneToOneManager:GetSingleRoundSetting(SingleRound.SearchingTime)
    local timeStr = searchingTimeData.value
    local timeTable = string.split(timeStr, ";")
    countDownMin = v2n(timeTable[1]) or 5
    countDownMax = v2n(timeTable[2]) or 10
    local countDown = Random(countDownMin, countDownMax)
    remain = countDownMaxShow
    -- 保存搜索的总时间和结束时间戳
    local startTime = TimeMgr:GetServerTimestamp()
    NetOneToOneData:SetDataByKey("searchingTotalTime", countDown)
    NetOneToOneData:SetDataByKey("searchingStartTime", startTime)
    NetOneToOneData:CheckSearching()
    self.ui.m_txtSearching.text = TimeMgr:ConverSecondToString(remain)
    self:SearchingOpponent()
end

--- 搜索对手
function UI_OneToOneView:SearchingOpponent()
    -- 头像切换效果
    SetActive(self.ui.m_goMask, true)
    --SetActive(self.ui.m_imgHeadRight, true)
    if self.otherHeadNode then
        SetActive(self.otherHeadNode,true)
    end
    SetActive(self.ui.m_goVS, true)
    SetParent(self.ui.m_goHead, self.ui.m_goMask)
    SetParent(self.ui.m_goVS, self.ui.m_goMask)
    DOScale(self.ui.m_goHeadRight.transform, 1.35, 0.5)
    self.headTimer = TimeMgr:CreateTimer(UIDefine.UI_OneToOneView,
    function()
        -- 随机切换头像
        --self:SetRandomHead(self.ui.m_imgHeadRight)
        local head = self:GetRandomHead()
        local border = self:GetRandomBorder()
        if self.otherHeadNode then
            SetHeadAndBorderByGo(self.otherHeadNode,head,border)
        end
        local randomName = OneToOneManager:GetRandomPlayerName() or ""
        self.ui.m_txtNameRight.text = randomName
    end,
    0.08, 999)
end

--- 停止搜索
function UI_OneToOneView:StopSearching()
    DOScale(self.ui.m_goHeadRight.transform, 1, 0.5)

    -- 设置匹配对手头像
    --local currentRobotHead = NetOneToOneData:GetDataByKey("currentRobotHead")
    --if IsNilOrEmpty(currentRobotHead) then
    --    local randomHead = Random(1, #self.availableHead)
    --    local icon = self.availableHead[randomHead].icon
    --    SetUIImage(self.ui.m_imgHeadRight, icon, false)
    --    NetOneToOneData:SetDataByKey("currentRobotHead", icon)
    --else
    --    SetUIImage(self.ui.m_imgHeadRight, currentRobotHead, false)
    --end

    local currentRobotHead = NetOneToOneData:GetDataByKey("currentRobotHead")
    local currentRobotBorder = NetOneToOneData:GetDataByKey("currentRobotBorder")
    local head = IsNilOrEmpty(currentRobotHead) and self:GetRandomHead() or currentRobotHead
    local border = IsNilOrEmpty(currentRobotBorder) and self:GetRandomBorder() or currentRobotBorder
    if self.otherHeadNode then
        SetHeadAndBorderByGo(self.otherHeadNode,head,border)
    end
    NetOneToOneData:SetDataByKey("currentRobotHead", head)
    NetOneToOneData:SetDataByKey("currentRobotBorder", border)

    -- 设置匹配对手名字
    local currentRobotName = NetOneToOneData:GetDataByKey("currentRobotName")
    if IsNilOrEmpty(currentRobotName) then
        local randomName = OneToOneManager:GetRandomPlayerName() or ""
        self.ui.m_txtNameRight.text = randomName
        NetOneToOneData:SetDataByKey("currentRobotName", randomName)
    else
        self.ui.m_txtNameRight.text = currentRobotName
    end

    --SetActive(self.ui.m_imgHeadRight, true)

    if self.otherHeadNode then
        SetActive(self.otherHeadNode,true)
    end

    SetActive(self.ui.m_imgHeadRightEmpty, false)
    SetActive(self.ui.m_imgHeadMask, false)

    SetActive(self.ui.m_goMask, false)
    SetParent(self.ui.m_goHead, self.ui.m_goHeadParent)
    SetParent(self.ui.m_goVS, self.ui.m_goVSParent)
    SetUIPos(self.ui.m_goVS, 0, 0)
    SetActive(self.ui.m_btnGo, true)
    self:RefreshCompetitionCountDown()
end

--- 设置主题
function UI_OneToOneView:SetTopic()
    SetActive(self.ui.m_goMask, false)
    SetParent(self.ui.m_goHead, self.ui.m_goHeadParent)
    SetParent(self.ui.m_goVS, self.ui.m_goVSParent)
    SetUIPos(self.ui.m_goVS, 0, 0)
    local image = GetComponent(self.ui.m_btnTopic, UEUI.Image)

    local currentTopic = NetOneToOneData:GetDataByKey("currentTopic")
    if currentTopic and currentTopic > 0 then
        local topicConfig = OneToOneManager:GetCompetitionTypeByType(currentTopic)
        self.ui.m_txtTopic.text = LangMgr:GetLang(topicConfig.name)
        SetActive(self.ui.m_btnGo, true)
        self:RefreshCompetitionCountDown()
    else
        -- 随机主题（先确定最终的主题）
        local topic = OneToOneManager:GetRandomTopic(true)
        NetOneToOneData:SetDataByKey("currentTopic", topic)
        local times = 60
        local remain = times
        -- 快速切换主题效果
        self.topicTimer = TimeMgr:CreateTimer(UIDefine.UI_OneToOneView,
        function()
            remain = remain - 1
            -- 随机切换主题图标
            local _, topicConfig = OneToOneManager:GetRandomTopic()
            SetUIImage(image, topicConfig.icon, false)
            -- 随机主题完成
            if remain <= 0 then
                -- 显示预先确定的主题图标
                local preTopic = NetOneToOneData:GetDataByKey("currentTopic")
                local currentTopicConfig = OneToOneManager:GetCompetitionTypeByType(preTopic)
                if not currentTopicConfig then return end
                SetUIImage(image, currentTopicConfig.icon, false)
                self.ui.m_txtTopic.text = LangMgr:GetLang(currentTopicConfig.name)
                SetActive(self.ui.m_btnGo, true)
                self:RefreshCompetitionCountDown()
            end
        end,
        0.08, times)
    end
end

--- 刷新比拼倒计时
function UI_OneToOneView:RefreshCompetitionCountDown()
    local isStartCompetition = NetOneToOneData:GetDataByKey("isStartCompetition")
    -- 比拼已经开始
    if isStartCompetition then
        -- 比拼剩余时间
        local remainTime = NetOneToOneData:GetRemainCompetitionTime()
        if remainTime > 0 then
            -- 初始刷新一次倒计时文本
            self.ui.m_txtGameCountDown.text = TimeMgr:ConverSecondToString(remainTime)
        end
        SetActive(self.ui.m_goVS, false)
        SetActive(self.ui.m_goSwordEffect, true)
        SetActive(self.ui.m_goWinGoalBG, true)
        local winNum = NetOneToOneData:GetDataByKey("winningStreak")
        local config = OneToOneManager:GetVictoryConfig(winNum)
        if config then
            local icon = "Sprite/ui_public/Resource_jifen_duijue.png"
            self.ui.m_txtWinGoal.text = LangMgr:GetLangFormat(8892, icon, config.victory_points)
            local image = GetChild(self.ui.m_txtWinGoal, "Image", UEUI.Image)
            if image then
                AddDOTweenNumberDelay(0, 1, 0.1, 0, function ()
                    SetUIPos(image, -83.5, -5)
                end)
            end
        end
    end
    local isBuytopicGift = NetOneToOneData:GetDataByKey("isBuytopicGift")
    -- 未购买主题礼包
    if not isBuytopicGift then
        -- 主题礼包剩余时间
        local remainTopicGiftTime = NetOneToOneData:GetRemainTopicGiftTime()
        if remainTopicGiftTime > 0 then
            -- 初始刷新一次倒计时文本
            SetActive(self.ui.m_goGiftBorder, true)
            self.ui.m_txtGiftCountDown.text = TimeMgr:ConverSecondToString(remainTopicGiftTime)
        end
    end
end

--- 获得比拼胜利奖励
function UI_OneToOneView:GetWinReward()
    if not NetOneToOneData.singleRewardState then return end
    NetOneToOneData.singleRewardState = false
    -- 获得奖励
    local rewardList = self:GetWinRewardList()
    local rewardAirStr = ""
    local hasAir = false
    for _, value in ipairs(rewardList) do
        local rewardTable = string.split(value, "|")
        local rewardID = v2n(rewardTable[1])
        local rewardNum = v2n(rewardTable[2])
        if rewardID < ItemID._RESOURCE_MAX then
            NetUpdatePlayerData:AddResourceNumByID(rewardID, rewardNum, nil, "UI_OneToOneView")
            local oldPos =  UIMgr:GetObjectScreenPos(self.ui.m_transFlyPos)
            MapController:AddResourceBoomAnim(oldPos.x, oldPos.y, tonumber(rewardID), rewardNum, false)
        else
            rewardAirStr = rewardAirStr .. value .. ";"
            hasAir = true
            local oldPos =  UIMgr:GetObjectScreenPos(self.ui.m_transFlyPos)
            local endPos = EquipmentManager:GetFlyEndPos(rewardID)
            local icon = ItemConfig:GetIcon(rewardID)
            MapController:FlyUIAnimByImg(oldPos.x, oldPos.y,
            icon, rewardNum, endPos.x, endPos.y)
        end
    end
    -- 发送奖励到主营地
    if hasAir then
        rewardAirStr = string.sub(rewardAirStr, 1, string.len(rewardAirStr) - 1)
        MapController:SendRewardToMap(rewardAirStr, nil, nil, nil, "UI_OneToOneView")
    end
    NetOneToOneData:AddPassportAndRankScore()
    self:FlyScore()
    self:RefreshScoreResult(false)
    SetActive(self.ui.m_goFlyScore, true)
    self:RefreshFlyScoreText()
    SetActive(self.ui.m_goWinningFlyScore, true)
    SetActive(self.ui.m_imgWinningBg, true)

    -- 连胜飘字绿色底框自适应
    local winningFlyScoreRT = GetComponent(self.ui.m_goWinningFlyScore, UE.RectTransform)
    UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(winningFlyScoreRT)
    local winningBgRT = GetComponent(self.ui.m_imgWinningBg, UE.RectTransform)
    winningBgRT.sizeDelta = Vector2.New(winningFlyScoreRT.rect.width + 40, winningBgRT.rect.height)

    self:DelayHideFlyScore()
    self:InitPanel()
end

--- 播放清屏动画
--- @param callback1 any 第一个回调
--- @param callback2 any 第二个回调
function UI_OneToOneView:PlayClearScreenAnim(callback1, callback2)
    -- 关闭排行榜
    if self.ui.m_goRankList.activeSelf then
        SetActive(self.ui.m_goRankList, false)
        self:CloseShowList()
    end
    self.isPlayingClearScreen = true
    PlayAnimStatusIndex(self.clearScreenAnim, "onetoone_clearscreen", function ()
        if callback1 then callback1() end
    end, function ()
        if callback2 then callback2() end
        self.isPlayingClearScreen = false
    end)
    -- 保险，防止动画中断 self.isPlayingClearScreen 一直为 true
    self:AnimTimer("onetoone_clearscreen")
end

--- 播放清屏返回动画
function UI_OneToOneView:PlayScreenBackAnim(hasTip)
    self.isPlayingClearScreen = true
    PlayAnimStatusIndex(self.clearScreenAnim, "onetoone_back", function ()
        self.isPlayingClearScreen = false
        self:InitPanel()
        if hasTip then
            -- 连胜中断飘字
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(8890))
        end
    end, nil)
    -- 保险，防止动画中断 self.isPlayingClearScreen 一直为 true
    self:AnimTimer("onetoone_back")
end

--- 动画计时器
--- @param animName string 动画名称
function UI_OneToOneView:AnimTimer(animName)
    local animLength = self.clearScreenAnim:GetClip(animName).length
    self.animTimer = TimeMgr:CreateTimer(UIDefine.UI_OneToOneView,
    function()
        self.isPlayingClearScreen = false
        TimeMgr:DestroyTimer(UIDefine.UI_OneToOneView, self.animTimer)
    end, animLength, 1)
end

--- 获取连胜奖励返回
function UI_OneToOneView:GetWinningStreakReward()
    local competitionResult = NetOneToOneData:GetDataByKey("competitionResult")
    if competitionResult > 0 then
        local winningStreak = NetOneToOneData:GetDataByKey("winningStreak")
        local winningStreakMax = NetOneToOneData:GetDataByKey("winningStreakMax")

        local nextData = NetOneToOneData:GetNextWinningStreak()
        SetActive(self.ui.m_goWinningStreakRed, false)
        if nextData then
            local goal = nextData.win
            SetUIImage(self.ui.m_imgWinningReward, nextData.icon, false)
            self.ui.m_txtWinningProgress.text = string.format("%s/%s", winningStreak, goal)
            self.ui.m_sliderWinningStreak.value = winningStreak / goal
            if winningStreakMax >= goal then
                SetActive(self.ui.m_goWinningEffect, true)
                SetActive(self.ui.m_goWinningStreakRed, true)
                local anim = GetComponent(self.ui.m_imgWinningReward, UE.Animation)
                if anim then
                    anim:Play("activity_itemicon")
                end
            else
                self:ResetWinningStreakRewardAnim()
            end
            SetActive(self.ui.m_imgWinningRewardCheck, false)
            self.ui.m_txtWinningStreak.text = LangMgr:GetLangFormat(8887) .. " ".. nextData.id
        else
            local lastData = NetOneToOneData:GetLastWinningStreak()
            SetUIImage(self.ui.m_imgWinningReward, lastData.icon, false)
            SetActive(self.ui.m_imgWinningRewardCheck, true)
            self.ui.m_txtWinningProgress.text = string.format("%s/%s", lastData.win, lastData.win)
            self.ui.m_sliderWinningStreak.value = 1
            self:ResetWinningStreakRewardAnim()
            self.ui.m_txtWinningStreak.text = LangMgr:GetLangFormat(8887) .. " ".. lastData.id
        end
    else
        self:InitPanel()
    end
end

--- 重置连胜奖励动画
function UI_OneToOneView:ResetWinningStreakRewardAnim()
    SetActive(self.ui.m_goWinningEffect, false)
    SetActive(self.ui.m_goWinningStreakRed, false)
    local anim = GetComponent(self.ui.m_imgWinningReward, UE.Animation)
    if anim then
        anim:Stop()
    end
    local rect = GetComponent(self.ui.m_imgWinningReward, UE.RectTransform)
    rect.anchoredPosition3D = Vector3.New()
    rect.localRotation = Quaternion.Euler(0, 0, 0)
end

--- 初始化排行榜
function UI_OneToOneView:InitRank()
    -- 排行榜循环列表
    self.rankSlideRect = SlideRect.new()
    self.rankSlideRect:Init(self.ui.m_scrollviewRank, 2)
    local item = self.ui.m_goRankCell.transform
    local rankItemList = {}
    -- 预生成 6 个 item
    for i = 1, 6, 1 do
        rankItemList[i] = RankItem.new()
        rankItemList[i]:Init(UEGO.Instantiate(item), self.ui.m_goRewardItem)
    end
    self.rankSlideRect:SetItems(rankItemList, 0, Vector2.New(0, 10))
    self.rankItemList = rankItemList
    -- 当前界面画布层级
    local canvas = self.uiGameObject:GetComponent(TP(UE.Canvas))
    CanvasSorting = canvas.sortingOrder
    self.imgPlayerRankReward = GetComponent(self.ui.m_btnPlayerRank, UEUI.Image)
    self.playerRewardItemList = {}
    -- 至少完成一次比拼
    local completeOnce = NetOneToOneData:GetDataByKey("completeOnce")
    if completeOnce then
        -- 初始请求一次
        self:RequestRankData()
    end
end

--- 显示排行榜
function UI_OneToOneView:ShowRank()
    -- 至少完成一次比拼
    local completeOnce = NetOneToOneData:GetDataByKey("completeOnce")
    if completeOnce then
        -- 每次请求排行榜数据后，有 10 秒的冷却时间，防止频繁点击
        if self.canRequestRankData then
            self.canRequestRankData = false
            self.rankTimer = TimeMgr:CreateTimer(UIDefine.UI_OneToOneView,
            function()
                self.canRequestRankData = true
                TimeMgr:DestroyTimer(UIDefine.UI_OneToOneView, self.rankTimer)
            end, 2, 1)
            self:OpenRank()
        end
        SetActive(self.ui.m_goTip, false)
        SetActive(self.ui.m_scrollviewRank, false)
        SetActive(self.ui.m_goBottom, false)
	else
		UI_SHOW(UIDefine.UI_OneToOneRank,nil,nil,nil,nil,nil,true)
    end
    SetActive(self.ui.m_goRankList, false)
end

function UI_OneToOneView:OpenRank()
	local function rankCallBack(data)
		if data.player.rank then
			if data.player.rank > 0 then
				UI_SHOW(UIDefine.UI_OneToOneRank,data)
			else
				UI_SHOW(UIDefine.UI_OneToOneRank,nil,nil,nil,nil,nil,true)
			end
		else
			UI_SHOW(UIDefine.UI_OneToOneRank,nil,nil,nil,nil,nil,true)
		end
	end
	NetOneToOneData:RequestRankData(rankCallBack)
end

--- 请求排行榜数据
function UI_OneToOneView:RequestRankData()
    local param= {}
    param.id = NetOneToOneData:GetDataByKey("activityId")
    HttpClient:SendToGS("/active/activetranking", param, function(objJson)
        local isErr, _ = HttpClient:GetNetJsonError(objJson)
        if isErr then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(7062))
            return
        end
        local data = objJson["data"]     -- 排行榜数据
        local ranking = data.ranking     -- 全部玩家数据
        local player = data.player       -- 玩家自身数据
        if not self.ui then return end
        -- 显示玩家分数
        self.ui.m_txtPlayerRankScore.text = tostring(player.point)
        -- 显示玩家排名
        local playerRanking = player.rank  -- rank 是平时用的排名
        if playerRanking then
            if self.playerRanking then
                NetOneToOneData.rankChange = self.playerRanking - playerRanking
            end
            self.playerRanking = playerRanking
            if playerRanking > 0 then
                if playerRanking <= 3 then
                    SetUIImage(self.ui.m_imgPlayerRankIcon, string.format(rankIconPath, playerRanking), false)
                    SetActive(self.ui.m_imgPlayerRankIcon, true)
                    SetActive(self.ui.m_txtPlayerRank, false)
                else
                    SetActive(self.ui.m_imgPlayerRankIcon, false)
                    SetActive(self.ui.m_txtPlayerRank, true)
                    self.ui.m_txtPlayerRank.text = tostring(playerRanking)
                end
            end
            self.ui.m_txtRank.text = tostring(playerRanking)
        end
    end)
end

--- 显示玩家排行榜奖励列表
function UI_OneToOneView:ShowPlayerRankReward()
    if not self.playerRanking then return end
    local config = OneToOneManager:GetRankRewardConfig(self.playerRanking)
    if config then
        if self.ui.m_goPlayerRankShowList.gameObject.activeSelf then return end
        local rewardStr = NetSeasonActivity:GetChangeItemId(config.reward)
        -- 隐藏之前显示的奖励列表
        if ShowListTemp then
            SetActive(ShowListTemp, false)
        end
        SetActive(self.ui.m_goPlayerRankShowList, true)
        local go = self.ui.m_goPlayerRankShowList.gameObject
        self:ShowListAnim(go)
        ShowListTemp = self.ui.m_goPlayerRankShowList
        self:GenerateShowList(self.playerRewardItemList, rewardStr, self.ui.m_transPlayerRankContent)
    end
end

--- 排行榜 item 初始化
--- @param transform any 根节点
--- @param rewardItem any 奖励 item
function RankItem:OnInit(transform, rewardItem)
    self.transform = transform.transform
    self.rankIcon = GetChild(transform, "goRank/imgRank", UEUI.Image)
    self.textRank = GetChild(transform, "goRank/txtRank", UEUI.Text)
    self.headIcon = GetChild(transform, "goHead/imgHead", UEUI.Image)
    self.textScore = GetChild(transform, "imgScore/Text", UEUI.Text)
    self.btnReward = GetChild(transform, "btnReward", UEUI.Button)
    self.imgReward = GetChild(transform, "btnReward", UEUI.Image)
    self.showList = GetChild(transform, "showList")
    self.rewardContent = GetChild(transform, "showList/content")
    self.rewardItemList = {}
    self.btnReward.onClick:AddListener(function()
        local config = OneToOneManager:GetRankRewardConfig(self.data.rank)
        if config then
            if self.showList.gameObject.activeSelf then return end
            local rewardStr = NetSeasonActivity:GetChangeItemId(config.reward)
            -- 隐藏之前显示的奖励列表
            if ShowListTemp then
                SetActive(ShowListTemp, false)
            end
            SetActive(self.showList, true)
            local go = self.showList.gameObject
            go.transform:SetLocalScale(0.0, 0.0, 0.0)
            DOKill(go.transform)
            DOScale(go.transform, Vector3.New(1.0, 1.0, 1.0), 0.5, nil, Ease.OutBack)
            ShowListTemp = self.showList
            -- 移除之前的画布组件
            if not IsNil(CanvasTemp) then
                UEGO.Destroy(CanvasTemp)
            end
            -- 添加画布组件，调整层级
            CanvasTemp = GetAndAddComponent(self.showList, TP(UE.Canvas))
            CanvasTemp.overrideSorting = true
            CanvasTemp.sortingOrder = CanvasSorting + 1
            -- 清理原来的奖励列表
            if #self.rewardItemList > 0 then
                for _, value in pairs(self.rewardItemList) do
                    UEGO.Destroy(value)
                end
            end
            -- 生成新的奖励列表
            local rewardList = string.split(rewardStr,";")
            for _, value in pairs(rewardList) do
                local rewardTable = string.split(value,"|")
                local itemID = v2n(rewardTable[1])
                local itemNum = v2n(rewardTable[2])
                local rewardItemGo = UEGO.Instantiate(rewardItem, self.rewardContent.transform)
                SetActive(rewardItemGo, true)
                local itemIcon = GetChild(rewardItemGo.transform, "ItemIcon", UEUI.Image)
                SetUIImage(itemIcon, ItemConfig:GetIcon(itemID), false)
                local itemNumText = GetChild(rewardItemGo.transform, "ItemNum", UEUI.Text)
                itemNumText.text = "x" .. itemNum
                table.insert(self.rewardItemList, rewardItemGo)
            end
        end
    end)
end

--- 排行榜 item 刷新
--- @param data any 数据
--- @param index any 索引
function RankItem:UpdateData(data, index)
    -- 隐藏自身的奖励列表
    SetActive(self.showList, false)
    -- 隐藏之前显示的奖励列表
    if ShowListTemp then
        SetActive(ShowListTemp, false)
    end
    -- 移除之前的画布组件
    if not IsNil(CanvasTemp) then
        UEGO.Destroy(CanvasTemp)
    end
    -- 保存数据并刷新组件
    self.data = data
    if data.rank <= 3 then
        SetUIImage(self.rankIcon, string.format(rankIconPath, data.rank), false)
        SetActive(self.rankIcon, true)
        SetActive(self.textRank, false)
    else
        SetActive(self.rankIcon, false)
        SetActive(self.textRank, true)
    end
    self.textRank.text = tostring(data.rank)
    self.textScore.text = tostring(data.point)
    local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, data.icon)
    if headConfig then
        SetUIImage(self.headIcon, headConfig.icon, false)
    end
    local config = OneToOneManager:GetRankRewardConfig(data.rank)
    if config then
        SetUIImage(self.imgReward, config.icon, false)
        SetActive(self.btnReward, true)
    else
        SetActive(self.btnReward, false)
    end
end

--- 排行榜 item 更新位置
--- @param vec any 位置
function RankItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

--- 排行榜 item 获取位置
--- @return any anchoredPosition 位置
function RankItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

--- 排行榜 item 销毁
function RankItem:onDestroy()
    self.rankIcon = nil
    self.textRank = nil
    self.headIcon = nil
    self.textScore = nil
    self.btnReward = nil
    self.imgReward = nil
    self.showList = nil
    self.rewardContent = nil
    self.rewardItemList = nil
    UEGO.Destroy(self.transform.gameObject)
	UI_CLOSE(UIDefine.UI_OneToOneRank)
end

--- 生成奖励列表
--- @param itemList table 奖励列表
--- @param rewardStr string 奖励数据
--- @param content any 奖励父节点
function UI_OneToOneView:GenerateShowList(itemList, rewardStr, content)
    -- 清理原来的奖励列表
    if #itemList > 0 then
        for _, value in pairs(itemList) do
            UEGO.Destroy(value)
        end
    end
    -- 生成新的奖励列表
    local rewardList = string.split(rewardStr,";")
    for _, value in pairs(rewardList) do
        local rewardTable = string.split(value,"|")
        local itemID = v2n(rewardTable[1])
        local itemNum = v2n(rewardTable[2])
        local rewardItemGo = UEGO.Instantiate(self.ui.m_goRewardItem, content)
        SetActive(rewardItemGo, true)
        local itemIcon = GetChild(rewardItemGo.transform, "ItemIcon", UEUI.Image)
        SetUIImage(itemIcon, ItemConfig:GetIcon(itemID), false)
        local itemNumText = GetChild(rewardItemGo.transform, "ItemNum", UEUI.Text)
        itemNumText.text = "x" .. itemNum
        table.insert(itemList, rewardItemGo)
    end
end

--- 奖励列表显示动画
--- @param go any 奖励列表节点
function UI_OneToOneView:ShowListAnim(go)
    go.transform:SetLocalScale(0.0, 0.0, 0.0)
    DOKill(go.transform)
    DOScale(go.transform, Vector3.New(1.0, 1.0, 1.0), 0.5, nil, Ease.OutBack)
end

--- 关闭奖励列表
function UI_OneToOneView:CloseShowList()
    if ShowListTemp then
        SetActive(ShowListTemp, false)
    end
    if not IsNil(CanvasTemp) then
        UEGO.Destroy(CanvasTemp)
    end
end

--- 跳转场景
function UI_OneToOneView:GoToScene()
    self:CloseSelf()
end

--- 打开新手引导
function UI_OneToOneView:Guide()
    -- 第二步引导回调
    local function GuideCallback2()
        UI_CLOSE(UIDefine.UI_GuideMask)
        self:StartGame()
    end

    -- 关闭帮助界面，再次引导
    local function Guide2()
        local centerPos = self:ConvertToRectPos(self.ui.m_btnStart)
        UI_SHOW(UIDefine.UI_GuideMask, {
            {3, 455, 123},             -- 遮罩类型和大小
            centerPos,                 -- 遮罩位置
            {2, 2},                    -- 遮罩按钮大小
            0.5,                       -- 缩放动画的时长
            function() GuideCallback2() end,   -- 点击回调
            {centerPos[1] / 100, centerPos[2] / 100 + 1.4, 0, 0, 0},   -- 箭头位置
            {-1.5, 0, 89901},                    -- 对话框位置和内容
            "Sprite/new_hero/headFrame_1.png",   -- 对话框头像
            nil,
        })
    end

    -- 第一步引导回调
    local function GuideCallback()
        UI_CLOSE(UIDefine.UI_GuideMask)
        UI_SHOW(UIDefine.UI_OneToOneHelp, Guide2)
    end

    -- 第一步引导
    local centerPos = self:ConvertToRectPos(self.ui.m_goGuidePos)
    UI_SHOW(UIDefine.UI_GuideMask, {
        {2, 0, 90},             -- 遮罩类型和大小
        centerPos,                 -- 遮罩位置
        {2, 2},                    -- 遮罩按钮大小
        0.5,                       -- 缩放动画的时长
        function() GuideCallback() end,   -- 点击回调
        --{centerPos[1] / 100, centerPos[2] / 100 - 2, 0, 0, 180},   -- 箭头位置
        {0, 0},   -- 箭头位置
        {-1.5, 0, 8891},                    -- 对话框位置和内容
        "Sprite/new_hero/headFrame_1.png",   -- 对话框头像
        nil,
        {centerPos[1] / 100 + 1, centerPos[2] / 100 - 1, 0, 0, 225}
    })
end

--- 转换 UI 坐标
--- @param go any UI 节点
--- @return table 坐标表
function UI_OneToOneView:ConvertToRectPos(go)
    local cam = UIMgr:GetCamera()
    local screenPoint = UE.RectTransformUtility.WorldToScreenPoint(cam, go.transform.position)
    local _, pos = UE.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.uiRectTransform,
        Vector2.New(screenPoint.x, screenPoint.y), cam)
    local posTable = {pos.x, pos.y}
    return posTable
end

--- 置灰效果
--- @param flag boolean 是否置灰
function UI_OneToOneView:SetGrey(flag)
	local gray = 0.3
    -- SetUIImageGray(self.ui.m_goVS, flag,true,gray)
    SetUIImageGray(self.ui.m_goHeadLeft, flag,true,gray)
    SetUIImageGray(self.ui.m_goHeadRight, flag,true,gray)

    --self:SetHeadGray(self.myHeadNode,flag,true,gray)
    --self:SetHeadGray(self.otherHeadNode,flag,true,gray)

    if self.myHeadNode then
        SetHeadGrayByGo(self.myHeadNode,flag,true,gray)
    end

    if self.otherHeadNode then
        SetHeadGrayByGo(self.otherHeadNode,flag,true,gray)
    end

    --SetUIImageGray(self.ui.m_imgHeadLeft, flag,true,gray)
    --SetUIImageGray(self.ui.m_imgHeadRight, flag,true,gray)
    SetUIImageGray(self.ui.m_imgMiddleBg, flag,true,gray)
    SetUIImageGray(self.ui.m_imgBlue, flag,true,gray)
    SetUIImageGray(self.ui.m_imgRed, flag,true,gray)
    SetUIImageGray(self.ui.m_imgFillRed, flag,true,gray)
    SetUIImageGray(self.ui.m_imgFillBlue, flag,true,gray)
    -- SetUIImageGray(self.ui.m_imgBody, flag,true,gray)
    -- SetUITextGray(self.ui.m_txtNameLeft, flag,true,gray)
    -- SetUITextGray(self.ui.m_txtNameRight, flag,true,gray)
    -- SetUITextGray(self.ui.m_txtScoreBlue, flag,true,gray)
    -- SetUITextGray(self.ui.m_txtScoreRed, flag,true,gray)

    local colorScoreBlue = flag and "515151" or "184BB1"
    local colorScoreRed = flag and "515151" or "A20000"
    UnifyOutline(self.ui.m_txtScoreBlue, colorScoreBlue)
    UnifyOutline(self.ui.m_txtScoreRed, colorScoreRed)

    local lightPath = flag and "Sprite/ui_bg/effect_light.png" or "Sprite/ui_bg/effect_light2.png"
    SetUIImage(self.ui.m_imgVSLight, lightPath)
    SetUIImageGray(self.ui.m_imgVSIcon, flag, true, gray)
end

--- 初始化积分描述
--- @return nil config 检查配置表
function UI_OneToOneView:InitPointsContent()
    local config = ConfigMgr:GetData(ConfigDefine.ID.one_vs_one_points)
    if not config then
        Log.Error("获取积分配置表错误")
        return nil
    end
    for key, value in pairs(config) do
        local item = UEGO.Instantiate(self.ui.m_goPointsItem, self.ui.m_transPointsContent)
        SetActive(item, true)
        local describeText = GetChild(item.transform, "describe", UEUI.Text)
        local pointsText = GetChild(item.transform, "points", UEUI.Text)
        describeText.text = LangMgr:GetLang(value.describe)
        pointsText.text = string.format("<color=\"#ffffff\">+</color> %s", NumToGameString2(v2n(value.points)))
    end
end

--- 获取活动剩余时间
--- @return integer time 剩余时间
function UI_OneToOneView:GetActiveTime()
    local condition = self.activityItem.info.state
    local time = 0
    if condition == 1 then
        time = self.activityItem:GetRemainingTime()
    elseif condition == 3 then
        time = self.activityItem:GetStartRemainingTime()
    elseif condition == 4 then
        time = self.activityItem:GetWaitTime()
    end
    return time
end

--- 刷新分数
function UI_OneToOneView:RefreshScore()
    local curScore = NetOneToOneData:GetDataByKey("curScore")
    local robotScore = NetOneToOneData:GetDataByKey("robotScore")
    if NetOneToOneData.playerChangeScore > 0 then
        if self.txtScoreBlueTween then
            self.txtScoreBlueTween:Kill()
            self.txtScoreBlueTween = nil
        end
        self.txtScoreBlueTween = Tween.To(function(value)
            self.ui.m_txtScoreBlue.text = tostring(math.floor(value))
        end, curScore - NetOneToOneData.playerChangeScore, curScore, 1):OnComplete(function ()
            local go = self.ui.m_txtScoreBlueAdd
            go.transform:SetLocalScale(1.0, 1.0, 1.0)
            DOKill(go.transform)
            DOScale(go.transform, Vector3.New(0.0, 0.0, 0.0), 0.5, nil, Ease.OutBack):OnComplete(function ()
                SetActive(self.ui.m_txtScoreBlueAdd, false)
            end)
        end)
        SetActive(self.ui.m_txtScoreBlueAdd, true)
        self.ui.m_txtScoreBlueAdd.text = "+" .. NetOneToOneData.playerChangeScore
        local go = self.ui.m_txtScoreBlueAdd
        go.transform:SetLocalScale(0.0, 0.0, 0.0)
        DOKill(go.transform)
        DOScale(go.transform, Vector3.New(1.0, 1.0, 1.0), 0.5, nil, Ease.OutBack)
        NetOneToOneData.playerChangeScore = 0
    else
        self.ui.m_txtScoreBlue.text = tostring(curScore)
    end

    if NetOneToOneData.robotChangeScore > 0 then
        if self.txtScoreRedTween then
            self.txtScoreRedTween:Kill()
            self.txtScoreRedTween = nil
        end
        self.txtScoreRedTween = Tween.To(function(value)
            self.ui.m_txtScoreRed.text = tostring(math.floor(value))
        end, robotScore - NetOneToOneData.robotChangeScore, robotScore, 1):OnComplete(function ()
            local go = self.ui.m_txtScoreRedAdd
            go.transform:SetLocalScale(1.0, 1.0, 1.0)
            DOKill(go.transform)
            DOScale(go.transform, Vector3.New(0.0, 0.0, 0.0), 0.5, nil, Ease.OutBack):OnComplete(function ()
                SetActive(self.ui.m_txtScoreRedAdd, false)
            end)
        end)
        SetActive(self.ui.m_txtScoreRedAdd, true)
        self.ui.m_txtScoreRedAdd.text = "+" .. NetOneToOneData.robotChangeScore
        local go = self.ui.m_txtScoreRedAdd
        go.transform:SetLocalScale(0.0, 0.0, 0.0)
        DOKill(go.transform)
        DOScale(go.transform, Vector3.New(1.0, 1.0, 1.0), 0.5, nil, Ease.OutBack)
        NetOneToOneData.robotChangeScore = 0
    else
        self.ui.m_txtScoreRed.text = tostring(robotScore)
    end

    if curScore > 0 or robotScore > 0 then
        local percent = curScore / (curScore + robotScore)
        local diff = math.abs(percent - 0.5)
        local target = diff * 0.4
        if curScore > robotScore then
            target = 0.5 + target
        else
            target = 0.5 - target
        end
        self.ui.m_sliderVS:DOValue(target, 1)
    else
        self.ui.m_sliderVS.value = 0.5
    end
end

function UI_OneToOneView:FlyScore()
    if NetOneToOneData.passportChangeScore > 0 then
        local oldPos =  UIMgr:GetObjectScreenPos(self.ui.m_transFlyPos)
        local posScreen = UIMgr:GetUIPosByWorld(self.ui.m_goPassPortContent.transform.position)
        local flyId = 55003
        local score = 5
        MapController:FlyUIAnim(oldPos.x, oldPos.y, flyId, score, posScreen.x, posScreen.y,
        nil, nil, nil, 0.7, nil,
        function ()

        end)
    end

    if NetOneToOneData.rankChangeScore > 0 then
        local oldPos =  UIMgr:GetObjectScreenPos(self.ui.m_transFlyPos)
        local posScreen = UIMgr:GetUIPosByWorld(self.ui.m_goRankContent.transform.position)
        local flyId = 55004
        local score = 5
        MapController:FlyUIAnim(oldPos.x, oldPos.y, flyId, score, posScreen.x, posScreen.y,
        nil, nil, nil, 0.7, nil,
        function ()

        end)
    end
end

--- 飞积分飘字
function UI_OneToOneView:FlyScoreText()
    local duration = 0.5
    local delay = 5
    if NetOneToOneData.winningChangeScore > 0 then
        local winningStreak = NetOneToOneData:GetDataByKey("winningStreak")
        if self.txtWinningFlyScoreTween then
            self.txtWinningFlyScoreTween:Kill()
            self.txtWinningFlyScoreTween = nil
        end
        self.txtWinningFlyScoreTween = Tween.To(function(value)
            -- self.ui.m_txtWinningFlyScore.text = "+" .. tostring(math.floor(value))
        end, 0, NetOneToOneData.winningChangeScore, duration):OnComplete(function ()
            local go = self.ui.m_goWinningFlyScore
            go.transform:SetLocalScale(1.0, 1.0, 1.0)
            DOKill(go.transform)
            DOScale(go.transform, Vector3.New(0.0, 0.0, 0.0), 0.5, nil, Ease.OutBack):OnComplete(function ()
                SetActive(self.ui.m_goWinningFlyScore, false)
            end):SetDelay(delay)
        end)
        SetActive(self.ui.m_goWinningFlyScore, true)
        self.ui.m_txtWinningFlyScore.text = "+" .. tostring(NetOneToOneData.winningChangeScore)
        local go = self.ui.m_goWinningFlyScore
        go.transform:SetLocalScale(0.0, 0.0, 0.0)
        DOKill(go.transform)
        DOScale(go.transform, Vector3.New(1.0, 1.0, 1.0), 0.5, nil, Ease.OutBack)
        NetOneToOneData.winningChangeScore = 0
    end

    if NetOneToOneData.passportChangeScore > 0 then
        local activeIntegral = self.activityItem:GetActiveIntegral()
        if self.txtPassPortFlyScoreTween then
            self.txtPassPortFlyScoreTween:Kill()
            self.txtPassPortFlyScoreTween = nil
        end
        self.txtPassPortFlyScoreTween = Tween.To(function(value)
            -- self.ui.m_txtPassPortFlyScore.text = "+" .. tostring(math.floor(value))
        end, 0, NetOneToOneData.passportChangeScore, duration):OnComplete(function ()
            local go = self.ui.m_txtPassPortFlyScore
            go.transform:SetLocalScale(1.0, 1.0, 1.0)
            DOKill(go.transform)
            DOScale(go.transform, Vector3.New(0.0, 0.0, 0.0), 0.5, nil, Ease.OutBack):OnComplete(function ()
                SetActive(self.ui.m_txtPassPortFlyScore, false)
            end):SetDelay(delay)
        end)
        SetActive(self.ui.m_txtPassPortFlyScore, true)
        self.ui.m_txtPassPortFlyScore.text = "+" .. tostring(NetOneToOneData.passportChangeScore)
        local go = self.ui.m_txtPassPortFlyScore
        go.transform:SetLocalScale(0.0, 0.0, 0.0)
        DOKill(go.transform)
        DOScale(go.transform, Vector3.New(1.0, 1.0, 1.0), 0.5, nil, Ease.OutBack)
        NetOneToOneData.passportChangeScore = 0
    end

    if NetOneToOneData.rankChangeScore > 0 then
        if self.txtRankFlyScoreTween then
            self.txtRankFlyScoreTween:Kill()
            self.txtRankFlyScoreTween = nil
        end
        self.txtRankFlyScoreTween = Tween.To(function(value)
            -- self.ui.m_txtRankFlyScore.text = "+" .. tostring(math.floor(value))
        end, 0, NetOneToOneData.rankChangeScore, duration):OnComplete(function ()
            local go = self.ui.m_txtRankFlyScore
            go.transform:SetLocalScale(1.0, 1.0, 1.0)
            DOKill(go.transform)
            DOScale(go.transform, Vector3.New(0.0, 0.0, 0.0), 0.5, nil, Ease.OutBack):OnComplete(function ()
                SetActive(self.ui.m_txtRankFlyScore, false)
            end):SetDelay(delay)
        end)
        SetActive(self.ui.m_txtRankFlyScore, true)
        self.ui.m_txtRankFlyScore.text = "+" .. tostring(NetOneToOneData.rankChangeScore)
        local go = self.ui.m_txtRankFlyScore
        go.transform:SetLocalScale(0.0, 0.0, 0.0)
        DOKill(go.transform)
        DOScale(go.transform, Vector3.New(1.0, 1.0, 1.0), 0.5, nil, Ease.OutBack)
        NetOneToOneData.rankChangeScore = 0
    end

    if NetOneToOneData.rankChange > 0 and self.playerRanking then
        if self.txtRankFlyTween then
            self.txtRankFlyTween:Kill()
            self.txtRankFlyTween = nil
        end
        self.txtRankFlyTween = Tween.To(function(value)
            -- self.ui.m_txtRank.text = tostring(math.floor(value))
        end, NetOneToOneData.rankChange + 1, self.playerRanking, duration):OnComplete(function ()
            local go = self.ui.m_txtRankFly
            go.transform:SetLocalScale(1.0, 1.0, 1.0)
            DOKill(go.transform)
            DOScale(go.transform, Vector3.New(0.0, 0.0, 0.0), 0.5, nil, Ease.OutBack):OnComplete(function ()
                SetActive(self.ui.m_txtRankFly, false)
            end):SetDelay(delay)
        end)
        SetActive(self.ui.m_txtRankFly, true)
        -- self.ui.m_txtRankFly.text = NetOneToOneData.rankChange
        local go = self.ui.m_txtRankFly
        go.transform:SetLocalScale(0.0, 0.0, 0.0)
        DOKill(go.transform)
        DOScale(go.transform, Vector3.New(1.0, 1.0, 1.0), 0.5, nil, Ease.OutBack)

        DOScale(self.ui.m_txtRank.transform, Vector3.New(1.5, 1.5, 1.5), duration, nil, Ease.OutBack):OnComplete(function ()
            DOScale(self.ui.m_txtRank.transform, Vector3.New(1, 1, 1), duration, nil, Ease.OutBack):SetDelay(delay)
        end)
        if self.playerRanking then
            self.ui.m_txtRank.text = tostring(self.playerRanking)
        end
        NetOneToOneData.rankChange = 0
    end
end

--- 显示胜负聊天气泡
--- @param isWin boolean 是否胜利
function UI_OneToOneView:ShowChatBubble(isWin)
    local winChatBubbleData = OneToOneManager:GetSingleRoundSetting(SingleRound.WinChatBubble)
    local failChatBubbleData = OneToOneManager:GetSingleRoundSetting(SingleRound.FailChatBubble)
    local winChatBubbleTable = string.split(winChatBubbleData.value, ";")
    local failChatBubbleTable = string.split(failChatBubbleData.value, ";")
    local winRandom = Random(1, #winChatBubbleTable)
    local failRandom = Random(1, #failChatBubbleTable)
    local winLangID = v2n(winChatBubbleTable[winRandom])
    local failLangID = v2n(failChatBubbleTable[failRandom])
    if isWin then
        self.ui.m_txtChatBubbleLeft.text = LangMgr:GetLang(winLangID)
        self.ui.m_txtChatBubbleRight.text = LangMgr:GetLang(failLangID)
    else
        self.ui.m_txtChatBubbleLeft.text = LangMgr:GetLang(failLangID)
        self.ui.m_txtChatBubbleRight.text = LangMgr:GetLang(winLangID)
    end

    local preferredHeightLeft = self.ui.m_txtChatBubbleLeft.preferredHeight
    local rectHeightLeft = self.ui.m_txtChatBubbleLeft.rectTransform.rect.height
    if preferredHeightLeft > rectHeightLeft then
        self.ui.m_txtChatBubbleLeft.resizeTextForBestFit = true
    end

    local preferredHeightRight = self.ui.m_txtChatBubbleRight.preferredHeight
    local rectHeightRight = self.ui.m_txtChatBubbleRight.rectTransform.rect.height
    if preferredHeightRight > rectHeightRight then
        self.ui.m_txtChatBubbleRight.resizeTextForBestFit = true
    end

    SetActive(self.ui.m_imgChatBubbleLeft, true)
    SetActive(self.ui.m_imgChatBubbleRight, true)

    local goLeft = self.ui.m_imgChatBubbleLeft
    goLeft.transform:SetLocalScale(0.0, 0.0, 0.0)
    DOKill(goLeft.transform)
    DOScale(goLeft.transform, Vector3.New(1.0, 1.0, 1.0), 0.5, nil, Ease.OutBack)

    local goRight = self.ui.m_imgChatBubbleRight
    goRight.transform:SetLocalScale(0.0, 0.0, 0.0)
    DOKill(goRight.transform)
    DOScale(goRight.transform, Vector3.New(1.0, 1.0, 1.0), 0.5, nil, Ease.OutBack)
end

function UI_OneToOneView:SingleRoundResultCountDown()
    if not NetOneToOneData.singleRoundState then return end
    if self.singleRoundTimer then
        TimeMgr:DestroyTimer(UIDefine.UI_OneToOneView, self.singleRoundTimer)
    end
    local remain = 5
    SetActive(self.ui.m_goSingleRoundTime, true)
    self.ui.m_txtSingleRoundTime.text = tostring(remain)
    NetOneToOneData.singleRoundState = false
    self.singleRoundTimer = TimeMgr:CreateTimer(UIDefine.UI_OneToOneView,
    function()
        remain = remain - 1
        self.ui.m_txtSingleRoundTime.text = tostring(remain)
        if remain <= 0 then
            SetActive(self.ui.m_goSingleRoundTime, false)
            self:AutoCompetitionResult()
            TimeMgr:DestroyTimer(UIDefine.UI_OneToOneView, self.singleRoundTimer)
        end
    end, 1, 5)
end

function UI_OneToOneView:AutoCompetitionResult()
    local competitionResult = NetOneToOneData:GetDataByKey("competitionResult")
    if competitionResult > 0 then
        NetOneToOneData:ResetCompetitionData()
        if competitionResult == RefreshUIType.ResultWin then
            self:GetWinReward()
        else
            NetOneToOneData:AddPassportAndRankScore()
            SetActive(self.ui.m_goFlyScore, true)
            self:RefreshFlyScoreText()
            SetActive(self.ui.m_goWinningFlyScore, false)
            SetActive(self.ui.m_imgWinningBg, false)
            self:FlyScore()
            self:InitPanel()
            self:DelayHideFlyScore()
        end
        self:CheckAutoStart()
    end
end

--- 显示胜利或失败的文本
--- @param isWin boolean 是否胜利
function UI_OneToOneView:ShowWinOrFailText(isWin)
    SetActive(self.ui.m_txtResultWin, isWin)
    SetActive(self.ui.m_txtResultFail, not isWin)
    SetActive(self.ui.m_imgRibbonWin, isWin)
    SetActive(self.ui.m_imgRibbonFail, not isWin)
    SetActive(self.ui.m_goWinEffect, isWin)
    self.winTipAnim = GetComponent(self.ui.m_goWinTip, UE.Animation)
    if isWin then
        PlayAnimStatusIndex(self.winTipAnim, "onetoone_wintip")
    else
        PlayAnimStatusIndex(self.winTipAnim, "onetoone_failtip")
    end
    SetActive(self.ui.m_rtransArrowPos, true)
    SetActive(self.ui.m_goArrow, false)
end

function UI_OneToOneView:DelayClose()
    if self.delayCloseTween then
        self.delayCloseTween:Kill()
        self.delayCloseTween = nil
    end
    self.canClose = false
    local duration = 1.5
    self.delayCloseTween = Tween.To(function(value)

    end, 0, 1, duration):OnComplete(function ()
        self.canClose = true
    end)
end

function UI_OneToOneView:DelayHideFlyScore()
    if self.delayHideFlyScoreTween then
        self.delayHideFlyScoreTween:Kill()
        self.delayHideFlyScoreTween = nil
    end
    local duration = 5
    self.delayHideFlyScoreTween = Tween.To(function(value)

    end, 0, 1, duration):OnComplete(function ()
        SetActive(self.ui.m_goFlyScore, false)
    end)
end

function UI_OneToOneView:RefreshFlyScoreText()
    if NetOneToOneData.winningChangeScore > 0 then
        self.ui.m_txtWinningFlyScore.text = "+" .. tostring(NetOneToOneData.winningChangeScore)
        NetOneToOneData.winningChangeScore = 0
    end

    if NetOneToOneData.passportChangeScore > 0 then
        self.ui.m_txtPassPortFlyScore.text = "+" .. tostring(NetOneToOneData.passportChangeScore)
        NetOneToOneData.passportChangeScore = 0
    end

    if NetOneToOneData.rankChangeScore > 0 then
        self.ui.m_txtRankFlyScore.text = "+" .. tostring(NetOneToOneData.rankChangeScore)
        NetOneToOneData.rankChangeScore = 0
    end
end

function UI_OneToOneView:CloseSelf()
    local competitionResult = NetOneToOneData:GetDataByKey("competitionResult")
    if competitionResult > 0 then
        self:AutoCompetitionResult()
        self:DelayClose()
    end
    if self.canClose then
        -- 刷新入口红点
        local activeId = self.activityItem.info.activeId
        UIMgr:RefreshAllMainFace(9,1,{ id = activeId, state = 2 })
        -- self:Close()
        UI_CLOSE(UIDefine.UI_ActivityRankCenter);
    end
end

--- 激活比拼倒计时的显示
function UI_OneToOneView:ActiveCompetitionTime()
    local isShow = false
    local isStartCompetition = NetOneToOneData:GetDataByKey("isStartCompetition")
    local isEndCompetition = NetOneToOneData:GetDataByKey("isEndCompetition")
    -- 比拼开始，未结束
    if isStartCompetition and not isEndCompetition then
        isShow = true
    end
    SetActive(self.ui.m_goTime, isShow)
end

--- 检查连胜中断
function UI_OneToOneView:CheckStreakBreak()
    -- 本次登录不再提醒
    if UIMgr:GetOnly("UI_OneToOneStreakBreak") then return end

    -- 获取连胜中断前的记录
    local winningStreakBefore = NetOneToOneData:GetDataByKey("winningStreakBefore")
    local winCostConfig = OneToOneManager:GetWinCostConfig(winningStreakBefore)
    if winCostConfig then
        UI_SHOW(UIDefine.UI_OneToOneStreakBreak, winCostConfig)
    end
end

--- 检查自动开始
function UI_OneToOneView:CheckAutoStart()
    if self.ui.m_togAutoStart.isOn then
        self:StartGame()
    end
end

--- 检查自动开始（通过提示弹窗触发）
function UI_OneToOneView:CheckAutoStartByTip()
    -- 比拼已经开始了，不用再自动开始
    local isStartCompetition = NetOneToOneData:GetDataByKey("isStartCompetition")
    if isStartCompetition then
        NetOneToOneData:SetDataByKey("autoStartByTip", false)
        return
    end
    local autoStartByTip = NetOneToOneData:GetDataByKey("autoStartByTip")
    if autoStartByTip then
        self:StartGame()
        NetOneToOneData:SetDataByKey("autoStartByTip", false)
    end
end

--设置个性化头像置灰效果
function UI_OneToOneView:SetHeadGray(obj,bIsGray,bIsBlack,black)
    local list = obj.transform:GetComponentsInChildren(TP(UEUI.Image), true)
    for i = 0, list.Length - 1 do
        SetUIImageGray(list[i], bIsGray,bIsBlack,black)
    end
end

--设置头像位置
function UI_OneToOneView:SetHeadPosition(obj,pos)
    local rect = GetComponent(obj,UE.RectTransform)
    rect.anchoredPosition = pos
end

return UI_OneToOneView