Using pre-set license
Built from '2022.3/china_unity/release' branch; Version is '2022.3.62f1c1 (b0109b07edb8) revision 11538587'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'zh' Physical Memory: 32596 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/Project_Merge_Minigame/Client/Project_PT
-logFile
Logs/AssetImportWorker1.log
-srvPort
2448
Successfully changed project path to: D:/Project_Merge_Minigame/Client/Project_PT
D:/Project_Merge_Minigame/Client/Project_PT
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [2852]  Target information:

Player connection [2852]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1112743566 [EditorId] 1112743566 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-ORSLCSS) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [2852] Host joined multi-casting on [***********:54997]...
Player connection [2852] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
Refreshing native plugins compatible for Editor in 2745.78 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.62f1c1 (b0109b07edb8)
[Subsystems] Discovering subsystems at path D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Project_Merge_Minigame/Client/Project_PT/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 (ID=0x2808)
    Vendor:   NVIDIA
    VRAM:     7957 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56192
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Register platform support module: D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002904 seconds.
- Loaded All Assemblies, in  0.233 seconds
Native extension for WindowsStandalone target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 844 ms
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.061 seconds
Domain Reload Profiling: 1293ms
	BeginReloadAssembly (69ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (99ms)
		LoadAssemblies (69ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (97ms)
			TypeCache.Refresh (96ms)
				TypeCache.ScanAssembly (87ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (1061ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1030ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (904ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1ms)
			ProcessInitializeOnLoadAttributes (80ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.945 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Unable to write to 'ProjectSettings\GvhProjectSettings.xml' (System.IO.IOException: Sharing violation on path D:\Project_Merge_Minigame\Client\Project_PT\ProjectSettings\GvhProjectSettings.xml
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.IO.FileOptions options) [0x00000] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean useAsync) [0x00000] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,int,bool)
  at System.Xml.XmlWriterSettings.CreateWriter (System.String outputFileName) [0x00051] in <cc6807978d504e8ea81a34b1b2ec871c>:0 
  at System.Xml.XmlWriter.Create (System.String outputFileName, System.Xml.XmlWriterSettings settings) [0x0000a] in <cc6807978d504e8ea81a34b1b2ec871c>:0 
  at Google.ProjectSettings.Save () [0x0006d] in /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:821 , Project settings were not saved!
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
Google.Logger:Log (string,Google.LogLevel) (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/Logger.cs:136)
Google.ProjectSettings:Save () (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:844)
Google.ProjectSettings:SetBool (string,bool,Google.SettingsLocation) (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:485)
Google.ProjectSettings:SetBool (string,bool) (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:497)
Google.IOSResolver:set_VerboseLoggingEnabled (bool) (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/IOSResolver/src/IOSResolver.cs:998)
Google.IOSResolver:.cctor () (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/IOSResolver/src/IOSResolver.cs:692)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs Line: 844)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.700 seconds
Domain Reload Profiling: 8645ms
	BeginReloadAssembly (102ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (15ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (2777ms)
		LoadAssemblies (2733ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (104ms)
			TypeCache.Refresh (90ms)
				TypeCache.ScanAssembly (77ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (5700ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (5605ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (29ms)
			ProcessInitializeOnLoadAttributes (5523ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=8c532ff6017f2564c9bb24d68449efd3): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 12.15 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4366 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1888 unused Assets / (6.3 MB). Loaded Objects now: 3737.
Memory consumption went from 0.85 GB to 0.84 GB.
Total: 22.753100 ms (FindLiveObjects: 0.441700 ms CreateObjectMapping: 0.127300 ms MarkObjects: 16.535000 ms  DeleteObjects: 5.647700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
========================================================================
Received Import Request.
  Time since last request: 20046.100873 seconds.
  path: Assets/ResPackage/Effect/Material/effect_UI_50.mat
  artifactKey: Guid(b4071732bfee5d642b7dbb2ff1c405d0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Effect/Material/effect_UI_50.mat using Guid(b4071732bfee5d642b7dbb2ff1c405d0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'b4c214b1641df5717c2472698cfce84d') in 0.088308 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/ResPackage/Effect/Prefab/zong.prefab
  artifactKey: Guid(db582fec4056af94dbfa0d61c663ffab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Effect/Prefab/zong.prefab using Guid(db582fec4056af94dbfa0d61c663ffab) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '5fafb5e9660c5eeb10750a1ae5b3ca2d') in 0.040419 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 67
========================================================================
Received Import Request.
  Time since last request: 8.533869 seconds.
  path: Assets/ResPackage/Effect/Texture/FX_Tex_liuguangtiao3 1.png
  artifactKey: Guid(74f0fab4b807fd2448722af5afcb9fa0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Effect/Texture/FX_Tex_liuguangtiao3 1.png using Guid(74f0fab4b807fd2448722af5afcb9fa0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'cf5718dc2c66fe75e501e21904d354ac') in 0.024126 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 156.895280 seconds.
  path: Assets/ResPackage/TinyGame/BcKillArrow/Art/Effects/Textures/f_ka_noise07.png
  artifactKey: Guid(59714377b5659f54ebc393802e1acbd0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/TinyGame/BcKillArrow/Art/Effects/Textures/f_ka_noise07.png using Guid(59714377b5659f54ebc393802e1acbd0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '17dd92ca42fcddb48046421c8021dc90') in 0.040978 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.291 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.361 seconds
Domain Reload Profiling: 1651ms
	BeginReloadAssembly (99ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (145ms)
		LoadAssemblies (180ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (10ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1361ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1178ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (26ms)
			ProcessInitializeOnLoadAttributes (1112ms)
			ProcessInitializeOnLoadMethodAttributes (20ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=39dea3e12810d3f42b219886c0f3f062): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 20.38 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4066 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1884 unused Assets / (6.2 MB). Loaded Objects now: 3785.
Memory consumption went from 0.78 GB to 0.78 GB.
Total: 20.920900 ms (FindLiveObjects: 0.327600 ms CreateObjectMapping: 0.106800 ms MarkObjects: 14.570100 ms  DeleteObjects: 5.915200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  3.095 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Unable to write to 'ProjectSettings\GvhProjectSettings.xml' (System.IO.IOException: Sharing violation on path D:\Project_Merge_Minigame\Client\Project_PT\ProjectSettings\GvhProjectSettings.xml
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.IO.FileOptions options) [0x00000] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean useAsync) [0x00000] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,int,bool)
  at System.Xml.XmlWriterSettings.CreateWriter (System.String outputFileName) [0x00051] in <cc6807978d504e8ea81a34b1b2ec871c>:0 
  at System.Xml.XmlWriter.Create (System.String outputFileName, System.Xml.XmlWriterSettings settings) [0x0000a] in <cc6807978d504e8ea81a34b1b2ec871c>:0 
  at Google.ProjectSettings.Save () [0x0006d] in /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:821 , Project settings were not saved!
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
Google.Logger:Log (string,Google.LogLevel) (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/Logger.cs:136)
Google.ProjectSettings:Save () (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:844)
Google.ProjectSettings:SetBool (string,bool,Google.SettingsLocation) (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:485)
Google.ProjectSettings:SetBool (string,bool) (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:497)
Google.IOSResolver:set_VerboseLoggingEnabled (bool) (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/IOSResolver/src/IOSResolver.cs:998)
Google.IOSResolver:.cctor () (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/IOSResolver/src/IOSResolver.cs:692)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs Line: 844)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in 10.214 seconds
Domain Reload Profiling: 13309ms
	BeginReloadAssembly (97ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (2950ms)
		LoadAssemblies (2987ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (10ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (10214ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (5081ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (27ms)
			ProcessInitializeOnLoadAttributes (5016ms)
			ProcessInitializeOnLoadMethodAttributes (20ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=f4f88e8de68a43743a2a536e42893239): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 13.99 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4066 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1882 unused Assets / (6.2 MB). Loaded Objects now: 3788.
Memory consumption went from 0.78 GB to 0.78 GB.
Total: 19.110200 ms (FindLiveObjects: 0.252100 ms CreateObjectMapping: 0.079700 ms MarkObjects: 13.809000 ms  DeleteObjects: 4.968300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.351 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.002 seconds
Domain Reload Profiling: 2352ms
	BeginReloadAssembly (100ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (198ms)
		LoadAssemblies (238ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (10ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (2002ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1575ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (30ms)
			ProcessInitializeOnLoadAttributes (1501ms)
			ProcessInitializeOnLoadMethodAttributes (26ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=9f63ee1d14b1b93448d1b88b974a775c): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 21.83 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4066 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1882 unused Assets / (6.2 MB). Loaded Objects now: 3791.
Memory consumption went from 0.78 GB to 0.78 GB.
Total: 31.093400 ms (FindLiveObjects: 0.341300 ms CreateObjectMapping: 0.130900 ms MarkObjects: 20.423500 ms  DeleteObjects: 10.195700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.514 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.737 seconds
Domain Reload Profiling: 3251ms
	BeginReloadAssembly (159ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (269ms)
		LoadAssemblies (327ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (18ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (2738ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2202ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (46ms)
			ProcessInitializeOnLoadAttributes (2092ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=3a2e1125c2b363940aa85fb57461a547): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 18.56 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4066 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1882 unused Assets / (6.2 MB). Loaded Objects now: 3794.
Memory consumption went from 0.78 GB to 0.78 GB.
Total: 31.491600 ms (FindLiveObjects: 0.476300 ms CreateObjectMapping: 0.143400 ms MarkObjects: 21.264600 ms  DeleteObjects: 9.606200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.422 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.325 seconds
Domain Reload Profiling: 2748ms
	BeginReloadAssembly (136ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (216ms)
		LoadAssemblies (261ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (20ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (2326ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2019ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (41ms)
			ProcessInitializeOnLoadAttributes (1912ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=169644603ad330c43ad1d3fa5c0ca8ac): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 21.59 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4066 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1882 unused Assets / (6.2 MB). Loaded Objects now: 3797.
Memory consumption went from 0.78 GB to 0.78 GB.
Total: 27.650900 ms (FindLiveObjects: 0.251500 ms CreateObjectMapping: 0.085400 ms MarkObjects: 20.820000 ms  DeleteObjects: 6.492800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.463 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.121 seconds
Domain Reload Profiling: 2583ms
	BeginReloadAssembly (143ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (250ms)
		LoadAssemblies (302ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (17ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (2121ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1794ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (47ms)
			ProcessInitializeOnLoadAttributes (1682ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=6e15ea11c7b1aab4a99bddb71cfc0a27): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 14.34 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4066 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1882 unused Assets / (6.2 MB). Loaded Objects now: 3800.
Memory consumption went from 0.78 GB to 0.78 GB.
Total: 31.201300 ms (FindLiveObjects: 0.345400 ms CreateObjectMapping: 0.323800 ms MarkObjects: 20.928600 ms  DeleteObjects: 9.602300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.441 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
System.IO.IOException: Sharing violation on path D:\Project_Merge_Minigame\Client\Project_PT\ProjectSettings\GvhProjectSettings.xml
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.IO.FileOptions options) [0x00000] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,int,System.IO.FileOptions)
  at System.IO.StreamReader..ctor (System.String path, System.Text.Encoding encoding, System.Boolean detectEncodingFromByteOrderMarks, System.Int32 bufferSize) [0x00055] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at System.IO.StreamReader..ctor (System.String path, System.Boolean detectEncodingFromByteOrderMarks) [0x00007] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at System.IO.StreamReader..ctor (System.String path) [0x00000] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at (wrapper remoting-invoke-with-check) System.IO.StreamReader..ctor(string)
  at Google.XmlUtilities.ParseXmlTextFileElements (System.String filename, Google.Logger logger, Google.XmlUtilities+ParseElement parseElement) [0x0000f] in /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/XmlUtilities.cs:104 
  at Google.ProjectSettings.Load () [0x00012] in /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:777 
  at Google.ProjectSettings.LoadIfEmpty () [0x0001b] in /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:558 
  at Google.ProjectSettings.GetBool (System.String name, System.Boolean defaultValue, Google.SettingsLocation location) [0x0002b] in /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:606 
  at Google.ProjectSettings.GetBool (System.String name, System.Boolean defaultValue) [0x00000] in /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:619 
  at Google.IOSResolver.get_VerboseLoggingEnabled () [0x00000] in /Users/<USER>/Workspace/git/unity-jar-resolver/source/IOSResolver/src/IOSResolver.cs:996 
  at Google.IOSResolver..cctor () [0x001ec] in /Users/<USER>/Workspace/git/unity-jar-resolver/source/IOSResolver/src/IOSResolver.cs:692 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.925 seconds
Domain Reload Profiling: 2366ms
	BeginReloadAssembly (151ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (215ms)
		LoadAssemblies (272ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (16ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1925ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1654ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (42ms)
			ProcessInitializeOnLoadAttributes (1559ms)
			ProcessInitializeOnLoadMethodAttributes (24ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=1571148a564f071479d742786a3a7c4b): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 14.98 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4066 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1882 unused Assets / (6.2 MB). Loaded Objects now: 3803.
Memory consumption went from 0.78 GB to 0.78 GB.
Total: 29.637400 ms (FindLiveObjects: 0.274200 ms CreateObjectMapping: 0.200800 ms MarkObjects: 20.717700 ms  DeleteObjects: 8.443100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.417 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.982 seconds
Domain Reload Profiling: 2399ms
	BeginReloadAssembly (131ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (211ms)
		LoadAssemblies (259ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (16ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (1983ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1723ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (46ms)
			ProcessInitializeOnLoadAttributes (1614ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=f4f10034f777bd04aa6dfa40a6b91c56): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 12.99 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4066 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1882 unused Assets / (6.2 MB). Loaded Objects now: 3806.
Memory consumption went from 0.78 GB to 0.78 GB.
Total: 20.975100 ms (FindLiveObjects: 0.331500 ms CreateObjectMapping: 0.140600 ms MarkObjects: 14.542000 ms  DeleteObjects: 5.959900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
========================================================================
Received Import Request.
  Time since last request: 3197.073289 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_maomi/0cat1_2_0.png
  artifactKey: Guid(4fe82e89493129e48a391662f41f737b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_maomi/0cat1_2_0.png using Guid(4fe82e89493129e48a391662f41f737b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '9facc637d70d53e7e129d0c285c95b8b') in 0.143352 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.112848 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_maomi/0cat1_4_0.png
  artifactKey: Guid(cd227b8448aee164fbc9da6dc7b4e17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_maomi/0cat1_4_0.png using Guid(cd227b8448aee164fbc9da6dc7b4e17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'efa23927d6282fc4a901748469598cb2') in 0.013781 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.242969 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_maomi/0cat3_1_0.png
  artifactKey: Guid(f2fd0b5ffe3a5dd45a3ab60900a1f478) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_maomi/0cat3_1_0.png using Guid(f2fd0b5ffe3a5dd45a3ab60900a1f478) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '9a7214a20cee8d9d8032cc47babca942') in 0.015307 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.276486 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_maomi/0cat3_2_0.png
  artifactKey: Guid(57d8d5f673aa947428162f62e8a2727f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_maomi/0cat3_2_0.png using Guid(57d8d5f673aa947428162f62e8a2727f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '830498b9e0d39b0957b703987a446f8f') in 0.016104 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.248622 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_maomi/0cat4_2_0.png
  artifactKey: Guid(84fd46bfd0ab53d448bd61daf7cd85ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_maomi/0cat4_2_0.png using Guid(84fd46bfd0ab53d448bd61daf7cd85ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '586ed357b098dfb4d1e600fc01d248b2') in 0.016614 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.253812 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_maomi/0cat4_3_0.png
  artifactKey: Guid(7149948b5e5b96649bfde4c317f0089a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_maomi/0cat4_3_0.png using Guid(7149948b5e5b96649bfde4c317f0089a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '7d4644c1186527821ec302768d344a08') in 0.015723 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_maomi/biaoqingbao5.png
  artifactKey: Guid(eb52489d4a00b8e49af0c9d220b81951) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_maomi/biaoqingbao5.png using Guid(eb52489d4a00b8e49af0c9d220b81951) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '369ebeb8532f867534efee9dd2c95406') in 0.014512 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csuipian3.png
  artifactKey: Guid(ce3c9f64936ad5b41a1b695ac5fc4944) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csuipian3.png using Guid(ce3c9f64936ad5b41a1b695ac5fc4944) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '0c77f5d67c1d37922e0a7cb4d13645fd') in 0.016035 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_win5.png
  artifactKey: Guid(c699f6adaf36bc441a615e51ea210857) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_win5.png using Guid(c699f6adaf36bc441a615e51ea210857) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '5f6323a9c589a63da36faf826e359032') in 0.016151 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_suipian2_4.png
  artifactKey: Guid(e7a65c04abb7bf04d8a02b224c9871f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_suipian2_4.png using Guid(e7a65c04abb7bf04d8a02b224c9871f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'c6ecd8d8d67812d522c4703de96ebe12') in 0.017509 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_jiantou.png
  artifactKey: Guid(b70818ae906b031419737005537a10aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_jiantou.png using Guid(b70818ae906b031419737005537a10aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'ec4f818396bb2241df3c063e2034ad1a') in 0.021978 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csp11.png
  artifactKey: Guid(ef518d74e1a31684cb9a093de087ecf9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csp11.png using Guid(ef518d74e1a31684cb9a093de087ecf9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '9aeda09d39963949cb3c7113eebc2455') in 0.015342 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csp9.png
  artifactKey: Guid(0b360123da502564f9ea2b7e46f18209) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csp9.png using Guid(0b360123da502564f9ea2b7e46f18209) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '221aee5f2e1f4baf362d2f0d831d2d68') in 0.015678 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_jijie1_nanguaji/bf_tou.png
  artifactKey: Guid(3781bb264c9ae8443afdeee4c62901e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_jijie1_nanguaji/bf_tou.png using Guid(3781bb264c9ae8443afdeee4c62901e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '9d685a568aa69e77806751ab949ff72c') in 0.017223 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_ctuzi.png
  artifactKey: Guid(45827a099abbf994cb84868a2d906a4f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_ctuzi.png using Guid(45827a099abbf994cb84868a2d906a4f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '52e3e6593a7ef738d162e04136fd1915') in 0.017363 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_win3.png
  artifactKey: Guid(aef03cee29cd23f47a31cc0551d35568) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_win3.png using Guid(aef03cee29cd23f47a31cc0551d35568) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'aa3705b967f3849e3bd2cb0ee6520fc2') in 0.016730 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/button-label.png
  artifactKey: Guid(2390236cce748c345806b6ece5798b62) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/button-label.png using Guid(2390236cce748c345806b6ece5798b62) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '5354a876e5206b9a462a496949424dbd') in 0.018957 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_win7.png
  artifactKey: Guid(8cff904fa691cdb468a1558306bfb459) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_win7.png using Guid(8cff904fa691cdb468a1558306bfb459) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '9b0f769bb6733ef4f034bfcf7f3c6055') in 0.018210 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/ResPackage/Effect/BattleTexture/Battle_tex_paodan10.png
  artifactKey: Guid(be5e387002b018e47bb26502806ae936) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Effect/BattleTexture/Battle_tex_paodan10.png using Guid(be5e387002b018e47bb26502806ae936) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '3253138ae28a76793362480ef19e7b5c') in 0.016257 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csp10.png
  artifactKey: Guid(6e0dd56c79d88154e9202ad38c1909b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csp10.png using Guid(6e0dd56c79d88154e9202ad38c1909b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '762d326a6a8287135e181a3302f67487') in 0.017744 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csp1.png
  artifactKey: Guid(ca8a2ec5474d58e43843594da9e4620c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csp1.png using Guid(ca8a2ec5474d58e43843594da9e4620c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '493484e61d37c21bffe62ab458015548') in 0.015487 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_jijie1_nanguaji/bf_zhuazi1.png
  artifactKey: Guid(8f037f5c09c95864f914f849214e8309) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_jijie1_nanguaji/bf_zhuazi1.png using Guid(8f037f5c09c95864f914f849214e8309) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '8bc702b39f997f8adb1cb3e7fea2f387') in 0.017394 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_maomi/biaoqingbao2.png
  artifactKey: Guid(4341e8f9bac0de846aaf06125c9ab624) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_maomi/biaoqingbao2.png using Guid(4341e8f9bac0de846aaf06125c9ab624) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'e361fb7c1f590daae4b503c18a472a89') in 0.016166 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_maomi/0cat3_4_0.png
  artifactKey: Guid(5638e142b6af0d24b98ee072753b5746) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_maomi/0cat3_4_0.png using Guid(5638e142b6af0d24b98ee072753b5746) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'dbdd3c9bbcfffed8b2ac2560b1a8fbc9') in 0.016674 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_maomi/biaoqingbao3.png
  artifactKey: Guid(93523286dee68564e83d770b4ad1d8d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_maomi/biaoqingbao3.png using Guid(93523286dee68564e83d770b4ad1d8d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'fe3ed82be6939ab29c412ae367bd4a54') in 0.014767 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_jiagnlitu.png
  artifactKey: Guid(96888f7138532dd4f8cc44cd8dcd8034) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_jiagnlitu.png using Guid(96888f7138532dd4f8cc44cd8dcd8034) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '8baf345e86b98a55508af86ebdaf38a4') in 0.015013 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_maomi/biaoqingbao4.png
  artifactKey: Guid(6ce119458f8a65c47b69b1d427d574e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_maomi/biaoqingbao4.png using Guid(6ce119458f8a65c47b69b1d427d574e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '2cd5588bf8753594a78d03ef9fe05989') in 0.014297 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/ResPackage/Sprite/bg/bg_2.jpg
  artifactKey: Guid(ea5b0e27a8dd2104b8d2252a6c8e6a23) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/bg/bg_2.jpg using Guid(ea5b0e27a8dd2104b8d2252a6c8e6a23) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '7f7105ca42beb5fd1cd47350ac42ee76') in 0.020985 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_suipian2_5.png
  artifactKey: Guid(7e079e88c492d794abfd4c094edfa4a6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_suipian2_5.png using Guid(7e079e88c492d794abfd4c094edfa4a6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '82fd1e9fcaeb8febf72026a03af2ec3b') in 0.016469 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/buff_win.png
  artifactKey: Guid(315a9e30ba78e21468ad7a806b76ff13) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/buff_win.png using Guid(315a9e30ba78e21468ad7a806b76ff13) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '839d33d578adb52a85dd3374d8246ada') in 0.016133 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_fuhuo_win3.png
  artifactKey: Guid(3d6d8b85f868e484387d3d29b5cea1c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_fuhuo_win3.png using Guid(3d6d8b85f868e484387d3d29b5cea1c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'c048a6f1e8e9341cc0404e3bd60debe9') in 0.014882 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_suipian2_1.png
  artifactKey: Guid(a42380c240e03c7478913ef7c2ee5b6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_suipian2_1.png using Guid(a42380c240e03c7478913ef7c2ee5b6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '42078e772616739bf39e89557cdee03b') in 0.017013 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_caidan5.png
  artifactKey: Guid(412e4546b7a71ea449ff372b4c82ffce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_caidan5.png using Guid(412e4546b7a71ea449ff372b4c82ffce) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '8fcc8b1f8be41f7dbc452ff0a5db20e2') in 0.018447 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Resources/img/button_fankui.png
  artifactKey: Guid(90e21600473e99244885a52526487499) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/img/button_fankui.png using Guid(90e21600473e99244885a52526487499) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '429da5a7b0dd555655533992dabccc6d') in 0.016988 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csp13.png
  artifactKey: Guid(e4f1627af0949c840980f374be01d3dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csp13.png using Guid(e4f1627af0949c840980f374be01d3dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'cec81f5a703ccb6c6ee717568cb54ea1') in 0.015091 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_jijie1_nanguaji/bf_erduo1.png
  artifactKey: Guid(7438c7625cd5623458fd9716c9729e2c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_jijie1_nanguaji/bf_erduo1.png using Guid(7438c7625cd5623458fd9716c9729e2c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '13005f8b2dfc9e8330f27bd3d22da973') in 0.015528 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csp5.png
  artifactKey: Guid(6279bb5fc133dc74ebf66cb234c22567) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csp5.png using Guid(6279bb5fc133dc74ebf66cb234c22567) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '869de552bf8a4a1ab79f2cca53890d12') in 0.013625 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_tongxingzheng/banner_mask.png
  artifactKey: Guid(10437f206ed68bf4c8e8bebde5fd68e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_tongxingzheng/banner_mask.png using Guid(10437f206ed68bf4c8e8bebde5fd68e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'b085ea070c5ffdd3f9361a3cb7282e59') in 0.017046 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csp4.png
  artifactKey: Guid(fca7e59567c5a414a909a3ac3075699a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csp4.png using Guid(fca7e59567c5a414a909a3ac3075699a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '35aed64702192d58a209bbee9a4a61d7') in 0.014825 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csp12.png
  artifactKey: Guid(87471dd29e071c04f8d468e4af6762dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csp12.png using Guid(87471dd29e071c04f8d468e4af6762dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'c67390a1fb821bef9cfa507d43510050') in 0.016150 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_jijie1_nanguaji/bf_erduo2.png
  artifactKey: Guid(61716b4ab0f3faf439b67300651f66f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_jijie1_nanguaji/bf_erduo2.png using Guid(61716b4ab0f3faf439b67300651f66f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '3d5c8f6fb85649405b03cdf478c072a2') in 0.016484 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_fuhuo_winclose.png
  artifactKey: Guid(6786a737f2409064bb6e8b8108c65dd9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_fuhuo_winclose.png using Guid(6786a737f2409064bb6e8b8108c65dd9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '69d6011a8c74fa962092ed3469749713') in 0.017661 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_huodongceng2.png
  artifactKey: Guid(1bb3652195818e9448d16781b2cc95dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_huodongceng2.png using Guid(1bb3652195818e9448d16781b2cc95dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '64a3536b0ccba15f56a62aca6cc5027f') in 0.016349 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_caidan4.png
  artifactKey: Guid(07157dbbcb2c1914cb3ecfbed0a93e71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_caidan4.png using Guid(07157dbbcb2c1914cb3ecfbed0a93e71) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '970217716d6da58a3ce7e51edf44bd99') in 0.017369 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_suipian1.png
  artifactKey: Guid(fc55b9ee00f2e824987c968a531de545) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_suipian1.png using Guid(fc55b9ee00f2e824987c968a531de545) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '37e855ce7eb61c48976e2b1dad367b4a') in 0.016839 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_caidan1.png
  artifactKey: Guid(15059044cda95c94f875eb25f39bd9b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_caidan1.png using Guid(15059044cda95c94f875eb25f39bd9b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'f0e051d7be02c51414c0a96d6f7ed87a') in 0.017058 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_maomi/0cat2_1_0.png
  artifactKey: Guid(a7984a58aca63464b9fe6da79f3eebcd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_maomi/0cat2_1_0.png using Guid(a7984a58aca63464b9fe6da79f3eebcd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '642c5d173b59f1c15b34507666706779') in 0.016996 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_suipian2_1_2.png
  artifactKey: Guid(10bde74d8305e3e44ae0e3b87e0ed6c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_suipian2_1_2.png using Guid(10bde74d8305e3e44ae0e3b87e0ed6c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '2d579db8bc99524902341a1284e60d79') in 0.016562 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_suipian2_3_2.png
  artifactKey: Guid(59a117e8e476bdc4f9d22b8c1503f22d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_suipian2_3_2.png using Guid(59a117e8e476bdc4f9d22b8c1503f22d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'cd88e7f7bf02d3e3bdeb499fc2b95b72') in 0.014112 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_fuhuo_yishiyong.png
  artifactKey: Guid(40fade7cf991a474da89c58e6e8111bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_fuhuo_yishiyong.png using Guid(40fade7cf991a474da89c58e6e8111bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '7a6286196af0b96d813a3c8b0bfaea48') in 0.017104 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csp2.png
  artifactKey: Guid(40d449009c9719541a9602857089ec4b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csp2.png using Guid(40d449009c9719541a9602857089ec4b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'a713dd1df1d548e7c02183cc8d2767db') in 0.016414 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_huodongceng3.png
  artifactKey: Guid(1eac55e40908b0c44aa4420778bb6961) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_huodongceng3.png using Guid(1eac55e40908b0c44aa4420778bb6961) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '017774e67360ff61f3b85da7c64e2059') in 0.017892 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_suipian2_5_2.png
  artifactKey: Guid(fc41d3db0644b2545bd32c9f23553a45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_suipian2_5_2.png using Guid(fc41d3db0644b2545bd32c9f23553a45) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '4c77acedc99a4b316b961b3ef28fb05a') in 0.016088 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_jijie5_patrick/bizi.png
  artifactKey: Guid(a3af6fa5238ebfd4da6fa5e81d28dc4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_jijie5_patrick/bizi.png using Guid(a3af6fa5238ebfd4da6fa5e81d28dc4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'ddffec0940db6c22b161b70fb9fed807') in 0.015591 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_caidan2.png
  artifactKey: Guid(adef97d996da9d54f9419e617ad4b597) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_caidan2.png using Guid(adef97d996da9d54f9419e617ad4b597) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'bf2c753cf846c2995b4cd1d1c6124c17') in 0.016500 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/activity_close.png
  artifactKey: Guid(a810016401a561f4b8cf25b82dcddbe8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/activity_close.png using Guid(a810016401a561f4b8cf25b82dcddbe8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '45fbe6a874098505f2026a411c1c55c0') in 0.019353 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csuipian2.png
  artifactKey: Guid(342abab0cd5d9cd4a9075c6032f89829) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_dh_csuipian2.png using Guid(342abab0cd5d9cd4a9075c6032f89829) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'e8a8d9c2b837ecb7e01558b122bf46de') in 0.016237 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/caidanmanghe_win6.png
  artifactKey: Guid(06b6d8b001a966346bd712d522b9910d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/caidanmanghe_win6.png using Guid(06b6d8b001a966346bd712d522b9910d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '8ffea67290965c6f3a8051ec8083aeb2') in 0.017570 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_maomi/0cat2_4_0.png
  artifactKey: Guid(a9a0ba8051ac9334d9086d026c24dfc1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_maomi/0cat2_4_0.png using Guid(a9a0ba8051ac9334d9086d026c24dfc1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'a54f7709d595508e6d0bbf41f79c24d1') in 0.017270 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_jijie1_nanguaji/bf_chibang2.png
  artifactKey: Guid(365c8464dcf16a345bc191236a4f4336) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_jijie1_nanguaji/bf_chibang2.png using Guid(365c8464dcf16a345bc191236a4f4336) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '50933913256a199963c28a25b5633d91') in 0.017194 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.531815 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/effect_UI_Light.png
  artifactKey: Guid(48a110006ef19af4c90254a75cdb52c0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/effect_UI_Light.png using Guid(48a110006ef19af4c90254a75cdb52c0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'c33b43509bf2c527811ab58350871030') in 0.014315 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/ResPackage/Sprite/ui_public/loading_effect_ring.png
  artifactKey: Guid(5070e0aeb2ee458499d72b377611c0d3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_public/loading_effect_ring.png using Guid(5070e0aeb2ee458499d72b377611c0d3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'af4f90a8b623b0c51db9bf42dbcf2843') in 0.015726 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Resources/img/loading_effect_ring.png
  artifactKey: Guid(bc50b6d0e33c8864cae907c781121135) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/img/loading_effect_ring.png using Guid(bc50b6d0e33c8864cae907c781121135) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'a46fc35a90e546efacc4b7837875a2d5') in 0.015740 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/ResPackage/Texture/Skin/skin_6/pic_item_land_1_6_effect1.png
  artifactKey: Guid(589c6fc6f8a2df44f930a0e30354697b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Texture/Skin/skin_6/pic_item_land_1_6_effect1.png using Guid(589c6fc6f8a2df44f930a0e30354697b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'a4b8fcbdf22b2da457d621c3116a965c') in 0.016666 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
TcpMessagingSession - receive error
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0