{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Unity.AI.Navigation/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.dll": {}}}, "Unity.AI.Navigation.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AI.Navigation": "1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.Editor.dll": {}}}, "Unity.AI.Navigation.Editor.ConversionSystem/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.Editor.ConversionSystem.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.Editor.ConversionSystem.dll": {}}}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEngine.TestRunner": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}}, "UnityEditor.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.UI.dll": {}}}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}}, "UnityEngine.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.UI.dll": {}}}}}, "libraries": {"Unity.AI.Navigation/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.csproj", "msbuildProject": "Unity.AI.Navigation.csproj"}, "Unity.AI.Navigation.Editor/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.Editor.csproj", "msbuildProject": "Unity.AI.Navigation.Editor.csproj"}, "Unity.AI.Navigation.Editor.ConversionSystem/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.Editor.ConversionSystem.csproj", "msbuildProject": "Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "path": "UnityEditor.TestRunner.csproj", "msbuildProject": "UnityEditor.TestRunner.csproj"}, "UnityEditor.UI/1.0.0": {"type": "project", "path": "UnityEditor.UI.csproj", "msbuildProject": "UnityEditor.UI.csproj"}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "path": "UnityEngine.TestRunner.csproj", "msbuildProject": "UnityEngine.TestRunner.csproj"}, "UnityEngine.UI/1.0.0": {"type": "project", "path": "UnityEngine.UI.csproj", "msbuildProject": "UnityEngine.UI.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Unity.AI.Navigation >= 1.0.0", "Unity.AI.Navigation.Editor >= 1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem >= 1.0.0", "UnityEditor.TestRunner >= 1.0.0", "UnityEditor.UI >= 1.0.0", "UnityEngine.TestRunner >= 1.0.0", "UnityEngine.UI >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Updater.csproj", "projectName": "Unity.AI.Navigation.Updater", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Updater.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Unity.AI.Navigation.Updater\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}}