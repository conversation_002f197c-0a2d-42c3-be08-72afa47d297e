﻿<Project ToolsVersion="Current">
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(Configuration)\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <LangVersion>9.0</LangVersion>
    <Configurations>Debug;Release</Configurations>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\bin\Debug\</OutputPath>
    <DefineConstants>UNITY_2022_3_62;UNITY_2022_3;UNITY_2022;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION;ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT;ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE;PLATFORM_STANDALONE;TEXTCORE_1_0_OR_NEWER;PLATFORM_STANDALONE_WIN;UNITY_STANDALONE_WIN;UNITY_STANDALONE;UNITY_UGP_API;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;PLATFORM_INITIALIZES_MEMORY_MANAGER_EXPLICITLY;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;ODIN_INSPECTOR;ODIN_INSPECTOR_3;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp\bin\Release\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.22</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>StandaloneWindows64:19</UnityBuildTarget>
    <UnityVersion>2022.3.62f1c1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode\extensions\visualstudiotoolsforunity.vstuc-1.1.3\Analyzers\Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\XLua\Gen\StorageManagerWrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\GameLuaObjectData_3.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Json.cs" />
    <Compile Include="Assets\IronSource\Scripts\IUnitySegment.cs" />
    <Compile Include="Assets\Scripts\Game\GameLanuchResource.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\TransformConstraintData.cs" />
    <Compile Include="Assets\XLua\Gen\UIDragWrap.cs" />
    <Compile Include="Assets\XLua\Gen\bc_IFGameBridge.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_SystemInfoWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_WaitForSecondsWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\IConstraint.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\BlendMode.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_EaseFactoryWrap.cs" />
    <Compile Include="Assets\XLua\Src\DelegateBridge.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Other\offsettest.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_SequenceWrap.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\TableViewPlug\Common\HtmlColor.cs" />
    <Compile Include="Assets\FlexReader\Converter\CustomConverters\ListConverter.cs" />
    <Compile Include="Assets\3rd\UIParticles\Scripts\UiParticles.cs" />
    <Compile Include="Assets\Scripts\XLua\LuaScript.cs" />
    <Compile Include="Assets\XLua\Gen\Spine_Unity_SkeletonDataAssetWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\SkeletonBounds.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionDropdown.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\PhysicsTriggerStay.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionMaterial.cs" />
    <Compile Include="Assets\Scripts\Framework\WebSocket\WebSocketSession.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\PageView.cs" />
    <Compile Include="Assets\XLua\Gen\Coffee_UIEffects_UIShadowWrap.cs" />
    <Compile Include="Assets\XLua\Gen\bc_MiniGameBase_CollisionTriggerListenerWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Timeline\SpineAnimationState\SpineAnimationStateMixerBehaviour.cs" />
    <Compile Include="Assets\Scripts\SDK\View\BindingWidget.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\TableView\ITableViewDataSource.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\NetworkEvent\NetworkEvent_ConnectionLost.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_AnimationClipWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_MeshColliderWrap.cs" />
    <Compile Include="Assets\XLua\Gen\PlayerPrefsWrap.cs" />
    <Compile Include="Assets\Scripts\SDK\GameSdkManager.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_RelativeJoint2DWrap.cs" />
    <Compile Include="Assets\Scripts\SDK\View\RequestWidget.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\TableViewPlug\tableView\TableViewV.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionToggle.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\NetworkEvent\Message\MessageStreamException.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_Vector2Wrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Asset Types\SkeletonDataModifierAsset.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_Dropdown_OptionDataListWrap.cs" />
    <Compile Include="Assets\Scripts\SDK\Module\SDKAppleModule.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_ResourcesWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\BoneData.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionSlider.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\GameLuaObjectData.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_Rigidbody2DWrap.cs" />
    <Compile Include="Assets\TinyGame\Common\TinyLang.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceImpressionData.cs" />
    <Compile Include="Assets\FlexReader\Converter\CustomConverter.cs" />
    <Compile Include="Assets\XLua\Gen\bc_MiniGameBase_GameObjectComsWrap.cs" />
    <Compile Include="Assets\XLua\Gen\bc_MiniGameBase_ILuaGameObjectWrap.cs" />
    <Compile Include="Assets\3rd\Demigiant\DOTweenPro\DOTweenAnimation.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_WaitForFixedUpdateWrap.cs" />
    <Compile Include="Assets\XLua\Src\TemplateEngine\TemplateEngine.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\SkeletonRenderSeparator\SkeletonRenderSeparator.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\GameScript\Tools\EffectManager.cs" />
    <Compile Include="Assets\Scripts\UI\UIBloom.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\bc.MiniGameBase\MonoSingleton.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\GameScript\GramScripts\EnumState.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceImpressionDataAndroid.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionTransformArray.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\CircleText.cs" />
    <Compile Include="Assets\FlexReader\Excel2007\WorkBook.cs" />
    <Compile Include="Assets\TinyGame\Scripts\CommonTools\CsGetLuaUtilst.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\SkeletonJson.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_ApplicationWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_LayoutRebuilderWrap.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\GameScript\GramScripts\Ring.cs" />
    <Compile Include="Assets\XLua\Gen\Mosframe_TableViewVWrap.cs" />
    <Compile Include="Assets\Scripts\UI\UIBGScaler.cs" />
    <Compile Include="Assets\XLua\Gen\Mosframe_TableViewHWrap.cs" />
    <Compile Include="Assets\EditorScript\MapEditorTiled.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceOfferwallAndroid.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\Injectionstring.cs" />
    <Compile Include="Assets\Scripts\WX\NativeInputField\InputField\WXInputFieldTmpAdapter.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_ToggleWrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionlongArray.cs" />
    <Compile Include="Assets\XLua\Gen\LanguageManagerWrap.cs" />
    <Compile Include="Assets\FlexReader\Mapping\IGenerator.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\Injectionimage.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\NetworkEvent\NetworkEvent_StartConnect.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourcePlacement.cs" />
    <Compile Include="Assets\TinyGame\Scripts\CommonTools\ObjEx.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\TableView\CCTableViewController.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_ShortcutExtensionsWrap.cs" />
    <Compile Include="Assets\3rd\Unity-Logs-Viewer\Reporter\ReporterGUI.cs" />
    <Compile Include="Assets\FlexReader\Core\Address.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_BoxColliderWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\SlotData.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Components\BoneFollower.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Other\tunnel.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_HorizontalLayoutGroupWrap.cs" />
    <Compile Include="Assets\XLua\Gen\LuaSdkHelperWrap.cs" />
    <Compile Include="Assets\IronSource\Scripts\IUnityRewardedVideo.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_ColorWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_RectTransformUtilityWrap.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\NetworkEvent\NetworkEvent_Disconnect.cs" />
    <Compile Include="Assets\XLua\Gen\MaterialSelectWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_WaitUntilWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_SpriteWrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\CommonTools\LogHelp.cs" />
    <Compile Include="Assets\Scripts\Proto\Role.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\GameScript\GramScripts\AddPedestal.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_TransformWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Components\PointFollower.cs" />
    <Compile Include="Assets\Scripts\Common\CustomScrollRect.cs" />
    <Compile Include="Assets\XLua\Gen\GameAniManagerWrap.cs" />
    <Compile Include="Assets\Scripts\Game\StartGame.cs" />
    <Compile Include="Assets\XLua\Src\ObjectCasters.cs" />
    <Compile Include="Assets\Scripts\EmojiPuzzleInput\EmojiPuzzleLine.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Attachments\AttachmentLoader.cs" />
    <Compile Include="Assets\3rd\Unity-Logs-Viewer\Reporter\Test\TestReporter.cs" />
    <Compile Include="Assets\FlexReader\Mapping\TableAttribute.cs" />
    <Compile Include="Assets\Scripts\SDK\BuglyMgr.cs" />
    <Compile Include="Assets\TinyGame\Common\MiniVibration.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\SkeletonClipping.cs" />
    <Compile Include="Assets\TinyGame\Common\CSVReader.cs" />
    <Compile Include="Assets\XLua\Gen\WrapPusher.cs" />
    <Compile Include="Assets\Scripts\Proto\Fight.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_DOTweenAnimationWrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionAnimationCurve.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionTextArray.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_TweenSettingsExtensionsWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\MathUtils.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceSegmentAndroid.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Static\FunctionLibrary.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\TableViewPlug\tableView\TableViewCell.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\AttachmentTools\AttachmentTools.cs" />
    <Compile Include="Assets\Scripts\SDK\View\LoginView.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionInputField.cs" />
    <Compile Include="Assets\Scripts\SDK\Module\SDKLoginModule.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceIAgent.cs" />
    <Compile Include="Assets\FlexReader\Converter\CustomConverters\RectConverter.cs" />
    <Compile Include="Assets\XLua\Gen\GameNetWork_Net_GameHttpWrap.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_TweenExtensionsWrap.cs" />
    <Compile Include="Assets\Scripts\Framework\ResourceManager\EventManager.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_QualitySettingsWrap.cs" />
    <Compile Include="Assets\IronSource\Scripts\UnsupportedPlatformAgent.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionDOTweenAnimation.cs" />
    <Compile Include="Assets\XLua\Gen\LuaDebugToolWrap.cs" />
    <Compile Include="Assets\XLua\Src\LuaEnv.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\AnimationStateData.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Attachments\MeshAttachment.cs" />
    <Compile Include="Assets\Scripts\XLua\CoroutineRunner.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\NetworkEvent\NetworkPassiveEvent.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\ExposedList.cs" />
    <Compile Include="Assets\XLua\Gen\LuaPerfect_ObjectItemWrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\Injectionstring_3.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Attachments\AtlasAttachmentLoader.cs" />
    <Compile Include="Assets\Scripts\Game\MapGridUtils.cs" />
    <Compile Include="Assets\XLua\Gen\ActionInfoListWrap.cs" />
    <Compile Include="Assets\3rd\Hierarchy 2\Runtime\UIElements.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\TableViewPlug\tableView\TableViewH.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\NetworkEvent\NetworkBaseEvent.cs" />
    <Compile Include="Assets\3rd\AssetBundleManager\AssetBundleConfig.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionSliderArray.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_ScrollbarWrap.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Water\ShuiDao.cs" />
    <Compile Include="Assets\EditorScript\MissionEditor.cs" />
    <Compile Include="Assets\XLua\Src\CopyByValue.cs" />
    <Compile Include="Assets\Scripts\Common\FileUtility.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\IUpdatable.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_ImageWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_RaycastHit2DWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_RawImageWrap.cs" />
    <Compile Include="Assets\Scripts\Game\UIVersionUpdateSilent.cs" />
    <Compile Include="Assets\GoogleSignIn\Plugins\Android\AndroidGoogleSignIn\AndroidGoogleSignInAccount.cs" />
    <Compile Include="Assets\Scripts\MonoWidgets\SeaRenderer.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\CustomMaterials\SkeletonRendererCustomMaterials.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\SlotBlendModes\SlotBlendModes.cs" />
    <Compile Include="Assets\Scripts\Framework\Http\HTTPPacket.cs" />
    <Compile Include="Assets\3rd\Hierarchy 2\Runtime\VisualElementExstensions.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Components\SkeletonAnimation.cs" />
    <Compile Include="Assets\Scripts\Common\DataConfig.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_ButtonWrap.cs" />
    <Compile Include="Assets\FlexReader\Core\Range.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_BoundsWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Asset Types\AnimationReferenceAsset.cs" />
    <Compile Include="Assets\Scripts\Proto\Common.cs" />
    <Compile Include="Assets\Scripts\MonoWidgets\MyScaler.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_ResourceRequestWrap.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\TableView\CCTableView.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Attachments\VertexAttachment.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_Vector3IntWrap.cs" />
    <Compile Include="Assets\XLua\Src\LuaDLL.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Slot.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\PathConstraintData.cs" />
    <Compile Include="Assets\3rd\Unity-Logs-Viewer\Reporter\Test\Rotate.cs" />
    <Compile Include="Assets\XLua\Src\LuaException.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_TimeWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Timeline\SpineSkeletonFlip\SpineSkeletonFlipTrack.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Ragdoll\SkeletonRagdoll.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\GameScript\EventCenter.cs" />
    <Compile Include="Assets\Scripts\Custom\LightningBoltScript.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\YieldInstructions\WaitForSpineTrackEntryEnd.cs" />
    <Compile Include="Assets\XLua\Gen\HttpMonoWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Animation.cs" />
    <Compile Include="Assets\Scripts\Proto\Battle.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_GridLayoutGroupWrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionAnimator.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\TableViewPlug\Common\HtmlColorExtensions.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Attachments\BoundingBoxAttachment.cs" />
    <Compile Include="Assets\FlexReader\Converter\CustomConverters\Vector4Converter.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\GameScript\Tools\BaseManager.cs" />
    <Compile Include="Assets\Scripts\GameLanuch.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\EventData.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_DOVirtualWrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionMaterialArray.cs" />
    <Compile Include="Assets\XLua\Gen\TopTriggerWrap.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\GameScript\GramScripts\Pedestal.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\TCPConnectMgr.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionTextAsset.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_ParticleSystemWrap.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\NetworkEvent\NetworkEvent_SocketClosed.cs" />
    <Compile Include="Assets\TinyGame\Scripts\CommonTools\ScrollRectItem.cs" />
    <Compile Include="Assets\XLua\Gen\RijndaelWrap.cs" />
    <Compile Include="Assets\Scripts\MonoWidgets\UserDataMono.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_EventSystems_EventSystemWrap.cs" />
    <Compile Include="Assets\IronSource\Scripts\IUnityBanner.cs" />
    <Compile Include="Assets\FlexReader\Converter\Validator.cs" />
    <Compile Include="Assets\Scripts\WX\NativeInputField\InputField\TmpTextInit.cs" />
    <Compile Include="Assets\IronSource\Scripts\IUnityRewardedVideoManual.cs" />
    <Compile Include="Assets\Scripts\Common\SkeletonAutoPlay.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Other\checker.cs" />
    <Compile Include="Assets\XLua\Gen\CCTableViewCellWrap.cs" />
    <Compile Include="Assets\FlexReader\Core\ICloneable.cs" />
    <Compile Include="Assets\Scripts\MonoWidgets\ButtonPressed.cs" />
    <Compile Include="Assets\XLua\Gen\PackUnpack.cs" />
    <Compile Include="Assets\Scripts\UI\UIRoot.cs" />
    <Compile Include="Assets\XLua\Gen\bc_MiniGameBase_MiniVibrationWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Atlas.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_ColliderWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_CanvasWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Timeline\SpineSkeletonFlip\SpineSkeletonFlipClip.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\SkeletonGraphic\SkeletonGraphic.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\OrganComponentData.cs" />
    <Compile Include="Assets\Scripts\SDK\Utils\SDKMonoSingleton.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\GameScript\GramScripts\MainPanel.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\GameScript\GramScripts\RewardEntityManager.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Timeline\PlayableHandle Component\SkeletonAnimationPlayableHandle.cs" />
    <Compile Include="Assets\Scripts\SDK\Utils\SDKNetHttp.cs" />
    <Compile Include="Assets\TinyGame\LuaContainer.cs" />
    <Compile Include="Assets\XLua\Src\SignatureLoader.cs" />
    <Compile Include="Assets\XLua\Gen\CCTableViewWrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionDOTweenAnimationArray.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\NetworkEvent\NetworkEvent_StartConnect2.cs" />
    <Compile Include="Assets\XLua\Gen\HyperlinkTextWrap.cs" />
    <Compile Include="Assets\XLua\Gen\StartGameWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_CapsuleColliderWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_MonoBehaviourWrap.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Other\levelloder.cs" />
    <Compile Include="Assets\TinyGame\GameObjectComs.cs" />
    <Compile Include="Assets\Scripts\UI\UIToggle.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Other\levelnumber.cs" />
    <Compile Include="Assets\XLua\Gen\bc_IFGameWrap.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\FlexyRingLevelEnter.cs" />
    <Compile Include="Assets\3rd\AssetBundleManager\AssetBundleLoadOperation.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceEventsDispatcher.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\LinkImageText.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_AnimatorWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_PhysicsWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\SkeletonUtility Modules\SkeletonUtilityGroundConstraint.cs" />
    <Compile Include="Assets\Scripts\Game\UINotice.cs" />
    <Compile Include="Assets\Scripts\XLua\UnityEngineObjectExtention.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_WaitWhileWrap.cs" />
    <Compile Include="Assets\XLua\Gen\CoroutineRunnerWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_RenderTextureWrap.cs" />
    <Compile Include="Assets\Scripts\SDK\View\AcceptWidget.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Other\Sound.cs" />
    <Compile Include="Assets\XLua\Src\TypeExtensions.cs" />
    <Compile Include="Assets\FlexReader\Mapping\TableMapper.cs" />
    <Compile Include="Assets\XLua\Gen\System_GCWrap.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_TweenWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\SkeletonGraphic\SkeletonGraphicMirror.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\TCPConnectSLG.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Static\Static.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_ScreenWrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\Properties\AssemblyInfo.cs" />
    <Compile Include="Assets\IronSource\Scripts\IUnityImpressionData.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionButtonArray.cs" />
    <Compile Include="Assets\Scripts\Common\ThreadManager.cs" />
    <Compile Include="Assets\XLua\Gen\MapTiledWrap.cs" />
    <Compile Include="Assets\Scripts\MonoWidgets\MapTiled.cs" />
    <Compile Include="Assets\XLua\Gen\AnimEventWrap.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\DLYShuiGuanEnter.cs" />
    <Compile Include="Assets\Scripts\UI\UICapture.cs" />
    <Compile Include="Assets\Scripts\Proto\Equip.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_Core_TweenerCore_3_UnityEngine_Vector3_DG_Tweening_Plugins_Core_PathCore_Path_DG_Tweening_Plugins_Options_PathOptions_Wrap.cs" />
    <Compile Include="Assets\Scripts\WX\NativeInputField\InputField\WXInputFieldAdapter.cs" />
    <Compile Include="Assets\XLua\Gen\GameLuaBehaviour_NewWrap.cs" />
    <Compile Include="Assets\Scripts\Proto\Gate.cs" />
    <Compile Include="Assets\IronSource\Scripts\IUnityInterstitial.cs" />
    <Compile Include="Assets\XLua\Gen\ButtonPressedWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Skeleton.cs" />
    <Compile Include="Assets\XLua\Gen\LogManWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_SkinnedMeshRendererWrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\ActionInfoList.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\OrganBaseXLuaGenConfig.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionTexture2D.cs" />
    <Compile Include="Assets\Scripts\Common\Lson\Util.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_SphereColliderWrap.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Level\Level.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionAnimatorArray.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\CrowdRunnersXLuaGenConfig.cs" />
    <Compile Include="Assets\XLua\Gen\OrganComponentDataWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_ComponentWrap.cs" />
    <Compile Include="Assets\XLua\Gen\Mosframe_TableViewWrap.cs" />
    <Compile Include="Assets\Scripts\Framework\Http\GameHttp.cs" />
    <Compile Include="Assets\EditorScript\Game\DirectoryWatcher.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionAnimationCurveArray.cs" />
    <Compile Include="Assets\UnityWebSocket\Demo\UnityWebSocketDemo.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_TouchWrap.cs" />
    <Compile Include="Assets\Scripts\Common\Lson\OffsetToLineCol.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Triangulator.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\FlexyRingEnter.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\NetworkEvent\NetworkEvent_ConnectionError.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_ShortcutExtensions50Wrap.cs" />
    <Compile Include="Assets\Scripts\Proto\Animal.cs" />
    <Compile Include="Assets\XLua\Src\GenAttributes.cs" />
    <Compile Include="Assets\XLua\Gen\UIDragXYDirWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_Button_ButtonClickedEventWrap.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\GameScript\LevelController.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_TweenerWrap.cs" />
    <Compile Include="Assets\Scripts\Framework\ResourceManager\AssetManager.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_MathfWrap.cs" />
    <Compile Include="Assets\XLua\Gen\LuaPerfect_ObjectFormaterWrap.cs" />
    <Compile Include="Assets\XLua\Gen\TMPro_TextMeshProWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Skin.cs" />
    <Compile Include="Assets\Scripts\MonoWidgets\HollowOutMask.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionAudioClipArray.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Other\cuderotation.cs" />
    <Compile Include="Assets\Scripts\Proto\Tower.cs" />
    <Compile Include="Assets\Scripts\Game\GameAniManager.cs" />
    <Compile Include="Assets\XLua\Gen\TMPro_TextMeshProUGUIWrap.cs" />
    <Compile Include="Assets\Scripts\Framework\Singleton\MonoSingleton.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_TextAssetWrap.cs" />
    <Compile Include="Assets\3rd\Unity-Logs-Viewer\Reporter\MultiKeyDictionary.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\NetworkEvent\NetworkEvent_SendMessage.cs" />
    <Compile Include="Assets\Scripts\Framework\Http\HTTPRequest.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_DOTweenPathWrap.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceMediationSettings.cs" />
    <Compile Include="Assets\Scripts\Common\ScreenShot.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_RectOffsetWrap.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\TCPConnect.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Asset Types\BlendModeMaterialsAsset.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceBannerAndroid.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_CollisionWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_QuaternionWrap.cs" />
    <Compile Include="Assets\XLua\Gen\Mosframe_TableViewCellWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_RectWrap.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Static\Singleton.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_ScrollRectWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_EventSystems_EventTrigger_EntryWrap.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Other\headofrays.cs" />
    <Compile Include="Assets\FlexReader\Converter\ValueConverter.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\GameLuaBehaviour.cs" />
    <Compile Include="Assets\XLua\Gen\ScriptExtendWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_RendererWrap.cs" />
    <Compile Include="Assets\XLua\Src\MethodWarpsCache.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\IkConstraint.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionGameObject.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\SkeletonUtility\SkeletonUtilityConstraint.cs" />
    <Compile Include="Assets\Scripts\Game\UILogin.cs" />
    <Compile Include="Assets\XLua\Src\ObjectPool.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_Dropdown_DropdownEventWrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\bc.MiniGameBase\MonoSingletonCreator.cs" />
    <Compile Include="Assets\XLua\Gen\LinkImageTextWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_CanvasScalerWrap.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceConstants.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Data\LevelData.cs" />
    <Compile Include="Assets\XLua\Gen\bc_MiniGameBase_LuaContainerWrap.cs" />
    <Compile Include="Assets\XLua\Gen\LevelManagerWrap.cs" />
    <Compile Include="Assets\FlexReader\Converter\CustomConverters\Color32Converter.cs" />
    <Compile Include="Assets\GoogleSignIn\Plugins\Android\AndroidGoogleSignIn\AndroidGoogleSignIn.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\SkeletonUtility\SkeletonUtility.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_MeshFilterWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_BoxCollider2DWrap.cs" />
    <Compile Include="Assets\XLua\Gen\System_Collections_Generic_List_1_System_Int32_Wrap.cs" />
    <Compile Include="Assets\Scripts\UI\UIDrag.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\Coroutine_Runner.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_ScrollRect_ScrollRectEventWrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\Injectionlong.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionGameObjectArray.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_EventSystems_EventTriggerWrap.cs" />
    <Compile Include="Assets\FlexReader\Core\Row.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSource.cs" />
    <Compile Include="Assets\Scripts\Framework\WebSocket\NetworkWebSocketEventMgr.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_Color32Wrap.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\TableViewPlug\Extensions\GameObjectEx.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\bc.MiniGameBase\MonoSingletonPathAttribute.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_SliderWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_SceneManagement_SceneManagerWrap.cs" />
    <Compile Include="Assets\IronSource\Scripts\iOSAgent.cs" />
    <Compile Include="Assets\Scripts\MonoWidgets\ControlExpand.cs" />
    <Compile Include="Assets\XLua\Gen\bc_MiniGameBase_ILuaGameObjectBridge.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_AudioSourceWrap.cs" />
    <Compile Include="Assets\XLua\Src\RawObject.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\GameScript\GramScripts\RingBehaviourLogic.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_PlaneWrap.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceRewardedVideoManualAndroid.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_Ray2DWrap.cs" />
    <Compile Include="Assets\Scripts\UI\UIButtonScale.cs" />
    <Compile Include="Assets\EditorScript\EditorStyleViewer.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_RaycastHitWrap.cs" />
    <Compile Include="Assets\XLua\Gen\System_Collections_Generic_Dictionary_2_System_String_System_String_Wrap.cs" />
    <Compile Include="Assets\Scripts\Common\NotchFit.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionButton.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Attachments\PathAttachment.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\SkeletonRenderSeparator\SkeletonPartsRenderer.cs" />
    <Compile Include="Assets\XLua\Gen\AnimationCurvesWrap.cs" />
    <Compile Include="Assets\FlexReader\Mapping\Mapper.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceRewardedVideoAndroid.cs" />
    <Compile Include="Assets\XLua\Gen\NetworkEventMgrWrap.cs" />
    <Compile Include="Assets\FlexReader\Converter\CustomConverters\ArrayConverter.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\GameScript\AddRingC.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_ContentSizeFitterWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\TK2D\SpriteCollectionAttachmentLoader.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Flower\FlowerAnimation.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\AnimationState.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_AsyncOperationWrap.cs" />
    <Compile Include="Assets\XLua\Src\GenericDelegateBridge.cs" />
    <Compile Include="Assets\FlexReader\Mapping\TableMapperBase.cs" />
    <Compile Include="Assets\3rd\Unity-Logs-Viewer\Reporter\ReporterMessageReceiver.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_CapsuleCollider2DWrap.cs" />
    <Compile Include="Assets\Scripts\SDK\LuaSdkHelper.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\SkeletonUtility Modules\SkeletonUtilityKinematicShadow.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\GameScript\DelayManager.cs" />
    <Compile Include="Assets\FlexReader\Excel2007\SharedStringCollection.cs" />
    <Compile Include="Assets\XLua\Gen\MonoLinkLuaDataWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_CanvasGroupWrap.cs" />
    <Compile Include="Assets\TinyGame\Common\LoadMgr.cs" />
    <Compile Include="Assets\XLua\Gen\SortingLayerMonoWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_WaitForEndOfFrameWrap.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\FancyScrollView\UIScrollView.cs" />
    <Compile Include="Assets\3rd\AssetBundleManager\Utility.cs" />
    <Compile Include="Assets\XLua\Gen\OrganBaseComponentWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_MeshRendererWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_Collision2DWrap.cs" />
    <Compile Include="Assets\XLua\Src\LuaFunction.cs" />
    <Compile Include="Assets\XLua\Gen\ControlExpandWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_Dropdown_OptionDataWrap.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\NetworkEventMgr.cs" />
    <Compile Include="Assets\Scripts\Common\Encrypt\Rijndael.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\AnimationMatchModifier\AnimationMatchModifierAsset.cs" />
    <Compile Include="Assets\XLua\Src\LuaTable.cs" />
    <Compile Include="Assets\XLua\Gen\LuaManagerWrap.cs" />
    <Compile Include="Assets\FlexReader\Converter\CustomConverters\DictionaryConverter.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Asset Types\SkeletonDataAsset.cs" />
    <Compile Include="Assets\ThirdParty\LuaPerfect\ObjectFormater.cs" />
    <Compile Include="Assets\Scripts\Framework\Singleton\Singleton.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_VerticalLayoutGroupWrap.cs" />
    <Compile Include="Assets\Scripts\Common\ThreadWorker.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\NetworkEvent\NetworkEvent_ConnectFail.cs" />
    <Compile Include="Assets\Scripts\MonoWidgets\SliderRect.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Timeline\SpineSkeletonFlip\SpineSkeletonFlipBehaviour.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_RayWrap.cs" />
    <Compile Include="Assets\Scripts\SDK\AdsManager.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\Injectionlong_3.cs" />
    <Compile Include="Assets\XLua\Gen\SDKLoginModuleWrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\GameLuaBehaviour_New.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Timeline\SpineAnimationState\SpineAnimationStateClip.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionAudioClip.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Asset Types\SpineAtlasAsset.cs" />
    <Compile Include="Assets\XLua\Gen\System_Collections_IEnumeratorBridge.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\CoroutineConfig.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_Collider2DWrap.cs" />
    <Compile Include="Assets\Scripts\Game\GameLanuchState.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_DebugWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Bone.cs" />
    <Compile Include="Assets\XLua\Gen\UICaptureWrap.cs" />
    <Compile Include="Assets\XLua\Src\Utils.cs" />
    <Compile Include="Assets\FlexReader\Converter\CustomConverters\ObjectConverter.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\GameScript\GramScripts\DragBox.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Ghost\SkeletonGhost.cs" />
    <Compile Include="Assets\XLua\Gen\Coroutine_RunnerWrap.cs" />
    <Compile Include="Assets\Scripts\Proto\Gm.cs" />
    <Compile Include="Assets\3rd\Effects Pro\Scripts\Effects.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionGameLuaBehaviour.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\bc.MiniGameBase\FuncCreateLua2Cs.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\IkConstraintData.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\GameLuaBehaviour_3.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_ShortcutExtensions46Wrap.cs" />
    <Compile Include="Assets\XLua\Gen\DelegatesGensBridge.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\TableViewPlug\Common\RichText.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_Core_ABSSequentiableWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_AnimationCurveWrap.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Other\AudioManager.cs" />
    <Compile Include="Assets\Scripts\Framework\ResourceManager\StorageManager.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_PolygonCollider2DWrap.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\GameScript\PlayAudios.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_RenderSettingsWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Attachments\Attachment.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_RandomWrap.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\FancyScrollView\Cell.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_BehaviourWrap.cs" />
    <Compile Include="Assets\XLua\Gen\LuaPerfect_ObjectRefWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_InputFieldWrap.cs" />
    <Compile Include="Assets\FlexReader\Converter\IConverter.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionArray.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Attachments\RegionAttachment.cs" />
    <Compile Include="Assets\Scripts\XLua\BuildInInit.cs" />
    <Compile Include="Assets\Scripts\MonoWidgets\MonoLinkLuaData.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\YieldInstructions\WaitForSpineAnimationComplete.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Ragdoll\SkeletonRagdoll2D.cs" />
    <Compile Include="Assets\Scripts\EmojiPuzzleInput\EmojiPuzzleInput.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Manager\EventManager\EventManager.cs" />
    <Compile Include="Assets\Scripts\MonoWidgets\SortingLayerMono.cs" />
    <Compile Include="Assets\FlexReader\Core\Cell.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\GameScript\GramScripts\GameData.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Asset Types\EventDataReferenceAsset.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_ShortcutExtensions43Wrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionimageArray.cs" />
    <Compile Include="Assets\Scripts\SDK\Module\SDKClipboardModule.cs" />
    <Compile Include="Assets\Scripts\SDK\Module\SDKFBModule.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\bc.MiniGameBase\XLuaManager.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\HyperlinkText.cs" />
    <Compile Include="Assets\Scripts\Common\CompressTookit.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_WaitForSecondsRealtimeWrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionstringArray.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\Injectiondouble_3.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_RigidbodyWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UserDataMonoWrap.cs" />
    <Compile Include="Assets\Scripts\Common\Utils\GizmosUtility.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Attachments\AttachmentType.cs" />
    <Compile Include="Assets\XLua\Src\StaticLuaCallbacks.cs" />
    <Compile Include="Assets\XLua\Gen\System_ObjectWrap.cs" />
    <Compile Include="Assets\FlexReader\CSV\Document.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_Vector3Wrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\SkeletonGraphic\BoneFollowerGraphic.cs" />
    <Compile Include="Assets\Scripts\SDK\View\ChangeLoginWidget.cs" />
    <Compile Include="Assets\Scripts\SDK\Utils\SDKSingleton.cs" />
    <Compile Include="Assets\TinyGame\Common\TinyLocalizationText.cs" />
    <Compile Include="Assets\FlexReader\Mapping\MapperBase.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Timeline\SpineAnimationState\SpineAnimationStateTrack.cs" />
    <Compile Include="Assets\Scripts\SDK\Utils\SDKUtility.cs" />
    <Compile Include="Assets\IronSource\Scripts\IUnityInitialization.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\TransformConstraint.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\bc.MiniGameBase\XLuaHelp.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\YieldInstructions\WaitForSpineEvent.cs" />
    <Compile Include="Assets\XLua\Gen\UIScrollViewWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_GameObjectWrap.cs" />
    <Compile Include="Assets\Scripts\SDK\Module\SDKLangModule.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngineObjectExtentionWrap.cs" />
    <Compile Include="Assets\Scripts\MonoWidgets\AnimEvent.cs" />
    <Compile Include="Assets\XLua\Gen\UI_UGUIExtendMini_ScrollRectItemWrap.cs" />
    <Compile Include="Assets\Scripts\Common\KVTextTool.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Components\SkeletonMecanim.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionInputFieldArray.cs" />
    <Compile Include="Assets\Scripts\SDK\Module\SDKFireModule.cs" />
    <Compile Include="Assets\3rd\Hierarchy 2\Runtime\HierarchyLocalData.cs" />
    <Compile Include="Assets\XLua\Gen\GameHelperWrap.cs" />
    <Compile Include="Assets\FlexReader\Core\Table.cs" />
    <Compile Include="Assets\Scripts\SDK\View\AgreeWidget.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_Physics2DWrap.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Level\DLYShuiGuanLevel.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceEvents.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_Core_TweenerCore_3_UnityEngine_Vector3_UnityEngine_Vector3_DG_Tweening_Plugins_Options_VectorOptions_Wrap.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceInitializationAndroid.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_DropdownWrap.cs" />
    <Compile Include="Assets\Scripts\SDK\Module\SDKNotifyModule.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionTexture.cs" />
    <Compile Include="Assets\Scripts\Game\GameSilentResource.cs" />
    <Compile Include="Assets\Scripts\MonoWidgets\TutorialBlock.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\GameLuaObjectDataComparer_3.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionToggleArray.cs" />
    <Compile Include="Assets\FlexReader\Mapping\Mapping.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionTextureArray.cs" />
    <Compile Include="Assets\XLua\Gen\TouchMonoWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_CircleCollider2DWrap.cs" />
    <Compile Include="Assets\3rd\AssetBundleManager\AssetBundleManager.cs" />
    <Compile Include="Assets\FlexReader\Converter\Extensions.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceJSON.cs" />
    <Compile Include="Assets\XLua\Src\LuaBase.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionRawImage.cs" />
    <Compile Include="Assets\XLua\Src\InternalGlobals.cs" />
    <Compile Include="Assets\Scripts\UI\UIGradient.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionTextAssetArray.cs" />
    <Compile Include="Assets\Scripts\SDK\Utils\AdvertisingType.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Other\rotater.cs" />
    <Compile Include="Assets\FlexReader\Mapping\ITableGenerator.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_TweenParamsWrap.cs" />
    <Compile Include="Assets\FlexReader\Converter\CustomConverters\Vector3Converter.cs" />
    <Compile Include="Assets\Scripts\Proto\Hero.cs" />
    <Compile Include="Assets\Scripts\UI\UIMask.cs" />
    <Compile Include="Assets\3rd\Demigiant\DOTween\Modules\DOTweenModuleUI.cs" />
    <Compile Include="Assets\Scripts\Common\Lson\Lson.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Other\ui.cs" />
    <Compile Include="Assets\Scripts\Game\LanguageManager.cs" />
    <Compile Include="Assets\XLua\Gen\EnumWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_KeyframeWrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectiondoubleArray.cs" />
    <Compile Include="Assets\XLua\Gen\AssetManagerWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\ISkeletonAnimation.cs" />
    <Compile Include="Assets\3rd\Hierarchy 2\Runtime\CustomRowItem.cs" />
    <Compile Include="Assets\Scripts\Common\Encrypt\AesRijndael.cs" />
    <Compile Include="Assets\Scripts\XLua\LuaManager.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Asset Types\AtlasAssetBase.cs" />
    <Compile Include="Assets\3rd\Unity-Logs-Viewer\Reporter\Reporter.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionTransform.cs" />
    <Compile Include="Assets\Scripts\Proto\Roledata.cs" />
    <Compile Include="Assets\Scripts\Common\GameHelper.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Manager\ConfigManager\ConfigManager.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Manager\UIManager\UIManager.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Other\waterstart.cs" />
    <Compile Include="Assets\XLua\Src\LuaDebugTool.cs" />
    <Compile Include="Assets\XLua\Gen\TMPro_TMP_InputFieldWrap.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\NetworkEvent\NetworkEvent_ConnectOK.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionText.cs" />
    <Compile Include="Assets\FlexReader\Converter\CustomConverters\ColorConverter.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionGameLuaBehaviourArray.cs" />
    <Compile Include="Assets\Scripts\Common\PlayerPrefsEx.cs" />
    <Compile Include="Assets\Scripts\Proto\Dungeon.cs" />
    <Compile Include="Assets\XLua\Gen\CCTableViewControllerWrap.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\TableView\CCTableViewCell.cs" />
    <Compile Include="Assets\Scripts\Framework\Http\HTTPParamField.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\TableViewPlug\tableView\TableView.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\ColorText.cs" />
    <Compile Include="Assets\Scripts\Game\GameVersion.cs" />
    <Compile Include="Assets\XLua\Gen\System_Collections_Generic_Dictionary_2_System_String_UnityEngine_GameObject_Wrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionspriteArray.cs" />
    <Compile Include="Assets\FlexReader\Mapping\ColumnAttribute.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_Events_UnityEventWrap.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\DeletegateCall.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\NetworkEvent\Message\MessageStream.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_LayoutElementWrap.cs" />
    <Compile Include="Assets\FlexReader\Converter\CustomConverters\Vector2Converter.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_AnimationWrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\Injectiondouble.cs" />
    <Compile Include="Assets\XLua\Gen\PageViewWrap.cs" />
    <Compile Include="Assets\3rd\Brotli\brotli.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Event.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\GameLuaObjectDataComparer.cs" />
    <Compile Include="Assets\IronSource\Scripts\AndroidAgent.cs" />
    <Compile Include="Assets\Scripts\MonoWidgets\HttpMono.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceUtils.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Components\SkeletonRenderer.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_DOTweenModuleUIWrap.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceInitilizer.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_ToggleGroupWrap.cs" />
    <Compile Include="Assets\EditorScript\EditorListener.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Timeline\SpineSkeletonFlip\SpineSkeletonFlipMixerBehaviour.cs" />
    <Compile Include="Assets\3rd\Hierarchy 2\Runtime\Texture2DExtensions.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_LayerMaskWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\SkeletonExtensions.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionRawImageArray.cs" />
    <Compile Include="Assets\XLua\Gen\UIMaskWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\SkeletonUtility\SkeletonUtilityBone.cs" />
    <Compile Include="Assets\TinyGame\Scripts\EventMessage\CollisionTriggerListener.cs" />
    <Compile Include="Assets\Scripts\Common\ScriptExtend.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\BoundingBoxFollower\BoundingBoxFollower.cs" />
    <Compile Include="Assets\Scripts\Custom\UITrigger.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_AnimatorStateInfoWrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionTexture2DArray.cs" />
    <Compile Include="Assets\TinyGame\GameManage\Script\LevelManager.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\SkeletonBinary.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Timeline\SpineAnimationState\SpineAnimationStateBehaviour.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_InputWrap.cs" />
    <Compile Include="Assets\TinyGame\Common\MusicController.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Mesh Generation\SpineMesh.cs" />
    <Compile Include="Assets\Scripts\MonoWidgets\AnimationCurves.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_GraphicRaycasterWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_RectTransformWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\SkeletonData.cs" />
    <Compile Include="Assets\Scripts\Framework\Http\HTTPResponse.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_LineRendererWrap.cs" />
    <Compile Include="Assets\Scripts\MonoWidgets\MaterialSelect.cs" />
    <Compile Include="Assets\TinyGame\ILuaGameObject.cs" />
    <Compile Include="Assets\XLua\Gen\PlayerPrefsExWrap.cs" />
    <Compile Include="Assets\Scripts\UI\UIDragXYDir.cs" />
    <Compile Include="Assets\3rd\UIParticles\Scripts\SetPropertyUtility.cs" />
    <Compile Include="Assets\Scripts\SDK\IAPSystem.cs" />
    <Compile Include="Assets\EditorScript\Game\LuaFileWatcher.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\SkeletonUtility Modules\SkeletonUtilityEyeConstraint.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_SpriteRendererWrap.cs" />
    <Compile Include="Assets\XLua\Gen\Spine_Unity_SkeletonGraphicWrap.cs" />
    <Compile Include="Assets\IronSource\Scripts\IUnityOfferwall.cs" />
    <Compile Include="Assets\XLua\Gen\XLuaGenAutoRegister.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\Injection_object.cs" />
    <Compile Include="Assets\XLua\Src\CodeEmit.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\NetworkEvent\NetworkActiveEvent.cs" />
    <Compile Include="Assets\Scripts\MonoWidgets\RunInBack.cs" />
    <Compile Include="Assets\XLua\Gen\Spine_Unity_SkeletonRendererWrap.cs" />
    <Compile Include="Assets\Scripts\Custom\TopTrigger.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Attachments\ClippingAttachment.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\UI\UIMain.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\InjectionDropdownArray.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\Attachments\PointAttachment.cs" />
    <Compile Include="Assets\Scripts\Framework\ResourceManager\SpriteAtlasMgr.cs" />
    <Compile Include="Assets\XLua\Gen\Spine_Unity_SkeletonAnimationWrap.cs" />
    <Compile Include="Assets\Scripts\Framework\Tcp\NetworkEvent\NetworkEvent_ReceivedMessage.cs" />
    <Compile Include="Assets\XLua\Gen\UIRootWrap.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_UI_TextWrap.cs" />
    <Compile Include="Assets\Scripts\MonoWidgets\TouchMono.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\FlexyRing\Script\FlexyRingGameData.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_AnimationStateWrap.cs" />
    <Compile Include="Assets\Scripts\Proto\Item.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\TableViewPlug\Extensions\RectTransformEx.cs" />
    <Compile Include="Assets\XLua\Src\ObjectTranslatorPool.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_TrailRendererWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Mesh Generation\DoubleBuffered.cs" />
    <Compile Include="Assets\FlexReader\Excel2007\WorkSheet.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Timeline\PlayableHandle Component\SpinePlayableHandleBase.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceSegment.cs" />
    <Compile Include="Assets\XLua\Gen\Spine_AnimationStateWrap.cs" />
    <Compile Include="Assets\TinyGame\Common\CasualGameTag.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\Injectionsprite.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_ObjectWrap.cs" />
    <Compile Include="Assets\XLua\Gen\GameSdkManagerWrap.cs" />
    <Compile Include="Assets\XLua\Src\ObjectTranslator.cs" />
    <Compile Include="Assets\XLua\Gen\TutorialBlockWrap.cs" />
    <Compile Include="Assets\TinyGame\Scripts\ScriptDLL\OrganBaseComponent.cs" />
    <Compile Include="Assets\EditorScript\MapEditorEventWin.cs" />
    <Compile Include="Assets\TinyGame\GameManage\Script\IFGame.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_MaterialWrap.cs" />
    <Compile Include="Assets\Scripts\Proto\Protocol.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Data\UIData.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Asset Types\RegionlessAttachmentLoader.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\SpineAttributes.cs" />
    <Compile Include="Assets\Scripts\UIPlugins\TableViewPlug\Common\ScrollbarHandleSize.cs" />
    <Compile Include="Assets\TinyGame\CasualGame\DLYShuiGuan\Script\Manager\InstanceBase.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceError.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceConfig.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-csharp\PathConstraint.cs" />
    <Compile Include="Assets\Scripts\Framework\Http\HTTPQueue.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_Vector4Wrap.cs" />
    <Compile Include="Assets\Scripts\Common\LogMan.cs" />
    <Compile Include="Assets\TinyGame\Common\IFTinyNet.cs" />
    <Compile Include="Assets\IronSource\Scripts\IronSourceInterstitialAndroid.cs" />
    <Compile Include="Assets\XLua\Gen\DG_Tweening_DOTweenWrap.cs" />
    <Compile Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Ghost\SkeletonGhostRenderer.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets\ResPackage\Spine\animal_12049\haixiang_7.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\CBB_Additive_Mask_JZB.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\TerrainShaders\Splats\Specular-Base.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13065\leilong5ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_1010\ZS_juese36_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\AlphaTest-Glossy.shader" />
    <None Include="Assets\ResPackage\Spine\arena_badge_7\duanwei_7.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\chengbao13_1\yishizhongjianjianzhu.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\meigui_4\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Reflect-ParallaxSpec.shader" />
    <None Include="Assets\ResPackage\Shader\Spine-Skeleton-Color.shader" />
    <None Include="Assets\ResPackage\Spine\event_38_2\damoxin.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1020\jiaose20liyishi.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityGlobalIllumination.cginc" />
    <None Include="Assets\ResPackage\Spine\animal_12084\tiane04.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12016\xingxing6ji.atlas.txt" />
    <None Include="Assets\3rd\TextMesh Pro\Shaders\TMPro.cginc" />
    <None Include="Assets\ResPackage\Spine\hero_3008\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\jijie_item_11_damo\damo.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\xulie_uv_X.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\TerrainShaders\Splats\Standard-Base.shader" />
    <None Include="Assets\3rd\UIParticles\Shaders\Ui Particle Multiply.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\TerrainEngine.cginc" />
    <None Include="Assets\ResPackage\Spine\frame_6_1\touxiang6_shang.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13064\leilong4ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1002_3\niuzaimaxiu.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityInstancing.cginc" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\CBB_Dissolution_Additvie_JZB.shader" />
    <None Include="Assets\ResPackage\Spine\role_show_1012\ZS_juese32_feiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12083\tiane03.atlas.txt" />
    <None Include="Assets\3rd\Spine\version.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\TerrainShaders\Splats\Specular-FirstPass.shader" />
    <None Include="Assets\ResPackage\Spine\hero_1001_7\shuiliaoyupao.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15058\meidusha8.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11033\yingwu3ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13097\bawanglong7.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_2003\gongrenda.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13085\yilong5ji.atlas.txt" />
    <None Include="Assets\3rd\TextMesh Pro\Shaders\TMP_SDF-Mobile Overlay.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13022\jianchihu2ji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\VR\Shaders\BlitTexArraySlice.shader" />
    <None Include="Assets\ResPackage\Spine\animal_15063\meirenyu3.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\trucks_1\huoche_bai.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12073\eyu03.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Alpha-BumpSpec.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13016\dujiaoshou6ji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Mobile\Mobile-Skybox.shader" />
    <None Include="Assets\ResPackage\Spine\animal_15023\yesha3.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBBNoCulling\CBB_NoCulling_Dissolution_Additvie.shader" />
    <None Include="Assets\ResPackage\Spine\arena_badge_1\duanwei_1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_2001\gongren12.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11024\tuoniao4ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15056\meidusha6.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_2018\ZS_juese40_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15084\niutouren4.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\jijie_qiqiu_1\xiaoqiqiu.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\meigui_2\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\event_38_3\xuyuanshu3.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityCG.cginc" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Nature\SoftOcclusion\TreeSoftOcclusionBarkRendertex.shader" />
    <None Include="Assets\ResPackage\Spine\animal_12034\haidiao174.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\SkeletonRenderSeparator\SkeletonRenderSeparator.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBBNoCulling\CBB_NoCulling_Shuxie.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityBuiltin2xTreeLibrary.cginc" />
    <None Include="Assets\ResPackage\Spine\slg_role_2011\juese30_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_4020\ZS_lianmeng4020_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13025\jianchihu5ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_4018\ZS_lianmeng4018_feiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11094\changjinglu4.atlas.txt" />
    <None Include="Assets\Parse\Plugins\Unity.Compat.dll" />
    <None Include="Assets\ResPackage\Spine\animal_15032\mihuojushou2.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12091\xiongmao1ji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\VR\Shaders\BlitCopyFromTexArray.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\TerrainShaders\Splats\FirstPass.shader" />
    <None Include="Assets\ResPackage\Spine\role_show_1003\ZS_juese3_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\BcKillArrow\Shader\Eff\BcKillArrowToonV12.shader" />
    <None Include="Assets\ResPackage\Shader\MySprite_Default.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Nature\SoftOcclusion\TreeSoftOcclusionBark.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\SpriteUVAni.shader" />
    <None Include="Assets\ResPackage\Spine\animal_11081\qie1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_4021\lianmeng4021_feiji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-BlitCopyWithDepth.shader" />
    <None Include="Assets\ResPackage\Spine\slg_role_2009\juese25_tanke.atlas.txt" />
    <None Include="Assets\FacebookSDK\link.xml" />
    <None Include="Assets\FacebookSDK\Plugins\iOS\Facebook.Unity.IOS.dll" />
    <None Include="Assets\ResPackage\Spine\animal_13067\leilong07.atlas.txt" />
    <None Include="Assets\3rd\Demigiant\DOTween\DOTween43.xml" />
    <None Include="Assets\ResPackage\Spine\animal_15044\zimingnade4.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13053\ying03.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\UI\base\UI-CompositeOverdraw.shader" />
    <None Include="Assets\ResPackage\Spine\animal_12036\haidiao176.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13082\yilong2ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_3001_1\lucy_daxuezhang.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_1018\ZS_juese39_zhandoufeiji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResources\base\Internal-Clear.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Nature\TreeCreator\TreeCreatorBark.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\VideoDecode.shader" />
    <None Include="Assets\ResPackage\Spine\hero_1010_1\jianshenjiaolian.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Shaders\Utility\HiddenPass.shader" />
    <None Include="Assets\ResPackage\Spine\animal_11084\qie04.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15016\fenghang6.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\frame_vip2_2\VIPkuang2_2.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12022\xiniu2ji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Cubemaps\CubeBlurOdd.shader" />
    <None Include="Assets\ResPackage\Spine\animal_11061\banma1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_3005\yashezhulaili.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12003\kongque3ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\meigui_1\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\BcKillArrow\Shader\CBB\BcKillArrowCBB_Additive_Mask.shader" />
    <None Include="Assets\ResPackage\Spine\role_show_4022\ZS_lianmeng4022_daodan.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_4014\ZS_lianmeng4014_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBBNoCulling\CBB_NoCulling_Additive_Mask.shader" />
    <None Include="Assets\ResPackage\Spine\youwu_penquan_2\penquanbanshou.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\arena_badge_3\duanwei_3.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CGIncludes\ShaderShared.cginc" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\UI\base\UI-Lit-Detail.shader" />
    <None Include="Assets\ResPackage\Spine\pharaoh_102\fanaowang2.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CGIncludes\ShaderMaths.cginc" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CGIncludes\SpriteSpecular.cginc" />
    <None Include="Assets\ResPackage\Spine\role_show_4019\ZS_lianmeng4019_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12021\xiniu1ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_2005\juese18_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11031\yingwuxiao1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_4025\lianmeng4025_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_3018\zhubaoshejishi.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12045\haibao165.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12076\eyu06.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13024\jianchihu4ji.atlas.txt" />
    <None Include="Assets\3rd\TextMesh Pro\Shaders\TMP_Bitmap.shader" />
    <None Include="Assets\ResPackage\Spine\jijie_balloon_9\paiofuqiqiudj.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Nature\TreeCreator\TreeCreatorBarkOptimized.shader" />
    <None Include="Assets\ResPackage\Spine\slg_die_1\mubei_die.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\frame_4_2\touxiang4_xia.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15043\zimingnade3.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15005\shijiu5.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\youwu_manyan_3\xiyouqidonghua_3.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\GLSLSupport.glslinc" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityStandardCore.cginc" />
    <None Include="Assets\ResPackage\Spine\animal_15033\mihuojushou3.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13013\dujiaoshou3ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11005\labuladuo5.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_2006\gongren12.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12062\shizi02.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\UI\base\UI-Lit-Bumped.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13017\dujiaoshou7ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1006_1\6xingguang.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Spine-Skeleton-Tint.shader" />
    <None Include="Assets\ResPackage\Spine\hero_1012\zuqiu.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15042\zimingnade2.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_1019\juese19_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\tile_13_1\zhandoubamogu.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_1013\juese20_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13086\yilong6ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15083\miluotao3.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\GIDebug\ShowLightMask.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13007\yazuishou7ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_2016\ZS_juese13_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_hero_3003\juese44_saileinaSSR.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_4023\lianmeng4023_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11004\labuladuo4ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12055\xiong5.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\chengbao11_1\zhuangban11-senglingyisheng.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15054\meidusha4.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_2004\juese27_zhandoufeiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12032\haidiao172.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Skybox.shader" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\SpriteVertexLighting.cginc" />
    <None Include="Assets\ResPackage\Spine\animal_11015\mao5ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_1004\ZS_juese14_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBBNoCulling\CBB_NoCulling_Fresnel_Animation.shader" />
    <None Include="Assets\ResPackage\Spine\animal_12035\haidiao175.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\cat_role\maonpc22.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\Motion.shader" />
    <None Include="Assets\ResPackage\Spine\slg_role_4024\lianmeng4024_feiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15087\miluotao7.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-DeferredReflections.shader" />
    <None Include="Assets\ResPackage\Spine\judadjtai\judadjtai.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11013\mao3ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13034\mengmaxiang04.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1016\xianfangyuantangmu.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\tile_10_2\xiarikuanghuan.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\pharaoh_106\shenmiao.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\zoo_tile_2_2\shendanhuochezhan.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\GIDebug\TextureUV.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Normal-Glossy.shader" />
    <None Include="Assets\ResPackage\Spine\motorbikes_1\huaxuemotuo.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Particle AddSmooth.shader" />
    <None Include="Assets\ResPackage\Spine\passport_box\jianglibaoxiang.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_3012\jiaoshe12jimi.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Illumin-VertexLit.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\GIDebug\UV1sAsPositions.shader" />
    <None Include="Assets\ResPackage\Spine\hero_1003\chushi.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\BcKillArrow\Shader\Eff\Karos_BRDF.cginc" />
    <None Include="Assets\ResPackage\Spine\zoo_tile_1_1\munaiyishuxiong.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\VideoDecodeAndroid.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13036\mengmaxiang06.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15081\miluotao1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15073\banrenma3.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13055\juying05.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_2015\ZS_juese24_zhandoufeiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15008\shijiu8.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12071\eyu01.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\UI\base\UI-Overdraw.shader" />
    <None Include="Assets\3rd\UIParticles\Shaders\Ui Particle Add.shader" />
    <None Include="Assets\ResPackage\Spine\animal_12054\xiong4.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\jijie_balloon_10\shangufengzheng.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12061\shizi01.atlas.txt" />
    <None Include="Assets\3rd\UIParticles\version.txt" />
    <None Include="Assets\ResPackage\Shader\scroll.shader" />
    <None Include="Assets\XLua\Resources\xlua\util.lua.txt" />
    <None Include="Assets\3rd\Demigiant\DOTween\DOTween46.xml" />
    <None Include="Assets\ResPackage\Spine\animal_15082\miluotao2.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CameraNormalsTexture.shader" />
    <None Include="Assets\3rd\Demigiant\DOTween\DOTween50.xml" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Shaders\BlendModes\Spine-Skeleton-PMA-Additive.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13083\yilong3ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13062\leilong2ji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Sprites-Default.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Skybox-Cubed.shader" />
    <None Include="Assets\ResPackage\Spine\item_daoyu_2_2\motianlun_2.atlas.txt" />
    <None Include="Assets\ResPackage\Shader\MySprite_Ab.shader" />
    <None Include="Assets\ResPackage\Spine\windmill_2\fengchedonghua2.atlas.txt" />
    <None Include="Assets\Parse\Plugins\Unity.Tasks.dll" />
    <None Include="Assets\ResPackage\Spine\hero_4002\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\frame_3_1\touxiang3_shang.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Sprites-Default-Mask.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Lightmap-BumpSpec.shader" />
    <None Include="Assets\ResPackage\Spine\animal_12024\xiniu4ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_2001\ZS_juese11_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\Shader\MySprite_Gray.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\CBB_Blend_Mask.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBBNoCulling\NoCulling_xulie_uv_Y.shader" />
    <None Include="Assets\3rd\TextMesh Pro\Shaders\TMPro_Mobile.cginc" />
    <None Include="Assets\ResPackage\Spine\frame_1_2\touxiang1_xia.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12002\kongque2ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\frame_vip5_2\VIPkuang5_2.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\TerrainSplatmapCommon.cginc" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\TerrainShaders\Details\WavingGrassBillboard.shader" />
    <None Include="Assets\ResPackage\Spine\frame_vip1_2\VIPkuang1_2.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityStandardMeta.cginc" />
    <None Include="Assets\ResPackage\Spine\animal_11053\houzi3.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12011\xingxing1ji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\AlphaTest-SoftEdgeUnlit.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Reflect-Parallax.shader" />
    <None Include="Assets\ResPackage\Spine\animal_15027\yesha7.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_3003\juese43_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\frame_6_2\touxiang6_xia.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_4014\lianmeng4014_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Unlit\Unlit-Alpha.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Particle Premultiply Blend.shader" />
    <None Include="Assets\Scripts\SDK\Res\Resources\LoginLanauge\Terms Of Service_en.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1001_5\jiaose_maiji_xiari.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11025\tuoniao5ji.atlas.txt" />
    <None Include="Assets\3rd\TextMesh Pro\Shaders\TMP_SDF SSD.shader" />
    <None Include="Assets\3rd\Demigiant\readme_DOTweenPro.txt" />
    <None Include="Assets\ResPackage\Spine\meigui_3\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_1019\ZS_juese19_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityStandardCoreForwardSimple.cginc" />
    <None Include="Assets\ResPackage\Spine\slg_role_1006\juese4_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1002\mengxinzhiyuan.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_4001\guaiwu1_sanlun.atlas.txt" />
    <None Include="Assets\3rd\Demigiant\DOTween\DOTween.XML" />
    <None Include="Assets\ResPackage\Spine\slg_role_1020\juese5_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\youwu_manyan_1\xiyouqidonghua.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1002_7\kafeishijian.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Alpha-VertexLit.shader" />
    <None Include="Assets\ResPackage\Spine\animal_11045\bianselong5ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11012\mao2ji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Nature\SoftOcclusion\TreeSoftOcclusionLeavesRendertex.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13074\jianlong04.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\SpritesUnlit.shader" />
    <None Include="Assets\ResPackage\Spine\frame_vip10_2\VIPkuang10_2.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\tile_10_1\meikuihuayuan.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1001_1\mofashi.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15067\meirenyu7.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Reflect-VertexLit.shader" />
    <None Include="Assets\ResPackage\Spine\hero_1013\yishujia.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\trucks_2\huoche_lv.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12013\xingxing3ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\arena_badge_2\duanwei_2.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\erosion.shader" />
    <None Include="Assets\ResPackage\Spine\hero_1015\juese15yuhangyuan.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15041\zimingnade1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15046\zimingnade6.atlas.txt" />
    <None Include="Assets\ResPackage\Shader\Additive_Mask.shader" />
    <None Include="Assets\ResPackage\Spine\frame_vip3_2\VIPkuang3_2.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\AnimationMatchModifier\AnimationMatchModifierAsset.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12014\xingxing4ji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-BlitCopy.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityShadowLibrary.cginc" />
    <None Include="Assets\ResPackage\Spine\jijie_item_11_fengling\xiarijiqiaiu.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\trucks_3\huoche_lan.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15055\meidusha5.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15037\mihuojushou7.atlas.txt" />
    <None Include="Assets\FacebookSDK\Plugins\Settings\Facebook.Unity.Settings.dll" />
    <None Include="Assets\ResPackage\Spine\animal_15018\fenghuang8.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\meigui_5\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15098\anvbisi8.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Sprites-MaskHero.shader" />
    <None Include="Assets\ResPackage\Spine\hero_1003_1\yuhangyuan.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_3014\yalishanda.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-GUITextureBlit.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Normal-DiffuseDetail.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Reflect-Diffuse.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Nature\base\SpeedTree.shader" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\SpriteLighting.cginc" />
    <None Include="Assets\ResPackage\Spine\event_38_4\shenshexing.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13063\leilong3.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\daoyu_4_3\zhangyuhuahuati.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12056\xiong6.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1001_9\maiji9_tanzhang.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnitySprites.cginc" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Spine-Skeleton-Fill.shader" />
    <None Include="Assets\ResPackage\Spine\animal_12012\xingxing2ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1004\shishangguwen.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\TerrainShaders\Splats\Standard-FirstPass.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBBNoCulling\CBB_NoCulling_Niuqu.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-Halo.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\TerrainShaders\Trees\BillboardTree.shader" />
    <None Include="Assets\ResPackage\Spine\slg_role_2010\juese29_zhandoufeiji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\AutoLight.cginc" />
    <None Include="Assets\ResPackage\Spine\animal_13054\juying04.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_2002\juese23_zhandoufeiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1004_2\jiaose4xixuegui.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13014\dujiaoshou4ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\arena_badge_5\duanwei_5.atlas.txt" />
    <None Include="Assets\3rd\TextMesh Pro\Shaders\TMP_SDF-Surface-Mobile.shader" />
    <None Include="Assets\ResPackage\Spine\animal_11073\yangtuo03.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_1015\juese35_zhandoufeiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\jiweijiutai\jiweijiuta.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\VR\Shaders\SpatialMappingWireframe.shader" />
    <None Include="Assets\ResPackage\Spine\zoo_tile_1_2\jinhunyixie.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_4013\lianmeng4013_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\SpeedTreeWind.cginc" />
    <None Include="Assets\ResPackage\Spine\animal_12095\xiongmao5ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12074\eyu04.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_2012\ZS_juese6_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\UI\base\UI-Unlit-Transparent.shader" />
    <None Include="Assets\FacebookSDK\Plugins\Canvas\Facebook.Unity.Canvas.dll" />
    <None Include="Assets\ResPackage\Spine\slg_bosscome_2\slg_bossattack_big.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\item_daoyu_3_2\gejuyuan_3.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15006\shijiou6.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\UI\base\UI-Unlit-Detail.shader" />
    <None Include="Assets\ResPackage\Spine\hero_1001_8\shendanshuijingqiu.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\VR\Shaders\Internal-VRDistortion.shader" />
    <None Include="Assets\ResPackage\Spine\animal_11065\banma5.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11003\labuladuo3ji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBBNoCulling\CBB_NoCulling_Dissolution_blend.shader" />
    <None Include="Assets\ResPackage\Spine\role_show_4011\ZS_lianmeng4011_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\BcKillArrow\Shader\Eff\BcKillArrow_EffectDissolve.shader" />
    <None Include="Assets\ResPackage\Spine\frame_4_1\touxiang4_shang.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_1011\juese17_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_2019\feiji_2019.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_2017\juese10_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\UI\base\UI-DefaultETC1.shader" />
    <None Include="Assets\ResPackage\Font\NumberFont.fnt" />
    <None Include="Assets\ResPackage\Spine\animal_15077\banrenma7.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_4019\lianmeng4019_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Standard.shader" />
    <None Include="Assets\ResPackage\Spine\slg_role_4020\lianmeng4020_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\TerrainShaders\Splats\Specular-AddPass.shader" />
    <None Include="Assets\ResPackage\Spine\role_show_1016\ZS_juese34_feiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1001_3\gaosuhuaban.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\xichen_1\xiyouqidonghua.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityDeferredLibrary.cginc" />
    <None Include="Assets\ResPackage\Spine\animal_15048\zimingnade8.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Particle MultiplyDouble.shader" />
    <None Include="Assets\ResPackage\Spine\role_show_1014\ZS_juese9_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Normal-ParallaxSpec.shader" />
    <None Include="Assets\ResPackage\Spine\animal_15024\yesha4.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Illumin-Diffuse.shader" />
    <None Include="Assets\ResPackage\Spine\animal_11072\yangtuo02.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13075\jianlong05.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_2004\ZS_juese27_zhandoufeiji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Mobile\Mobile-VertexLit-OnlyDirectionalLights.shader" />
    <None Include="Assets\ResPackage\Spine\animal_11095\changjinglu5.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15096\anvbisi6.atlas.txt" />
    <None Include="Assets\3rd\Demigiant\DemiLib\DemiLib.dll" />
    <None Include="Assets\ResPackage\Spine\slg_role_1016\juese34_feiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13095\bawanglong05.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1003_3\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15002\shijiu2.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13094\bawanglong04.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Shaders\Utility\Hidden-Spine-Bones.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13045\she05.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Alpha-Glossy.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Particle Multiply.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Sprites-Diffuse.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13046\she06.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\CBB_Shuxie.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-CombineDepthNormals.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Alpha-Diffuse.shader" />
    <None Include="Assets\ResPackage\Spine\frame_5_1\touxiang5_shang.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\motorbikes_go\gogogo.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_4012\ZS_lianmeng4012_feiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12001\kongque1ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\jijie_item_11_deng1\yuedeng2.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_3010\qianshuiyuanfulan.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1001_2\maiji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Cubemaps\CubeBlend.shader" />
    <None Include="Assets\ResPackage\Spine\hero_2005\sheyingshi.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15021\yesha1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\frame_vip4_2\VIPkuang4_2.atlas.txt" />
    <None Include="Assets\XLua\Resources\xlua\protoc.lua.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\SpriteUnlit.cginc" />
    <None Include="Assets\ResPackage\Spine\animal_13081\yilong1ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13031\mengmaxiang01.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Spine-Special-Skeleton-Blackscale.shader" />
    <None Include="Assets\ResPackage\Spine\animal_12075\eyu05.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBBNoCulling\NoCulling_lizi_katong.shader" />
    <None Include="Assets\ResPackage\Spine\animal_15092\anvbisi2.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\pharaoh_101\fanaowang1.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\BcKillArrow\Shader\CBB\BcKillArrowCBB_Additive.shader" />
    <None Include="Assets\ResPackage\Spine\animal_12005\kongque5.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\SkeletonGraphic\Shaders\Spine-SkeletonGraphic-TintBlack.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\AlphaTest-Diffuse.shader" />
    <None Include="Assets\ResPackage\Spine\slg_role_1012\juese32_feiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12066\shizi6ji.atlas.txt" />
    <None Include="Assets\3rd\Effects Pro\Shaders\Greyscale.shader" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CGIncludes\SpriteUnlit.cginc" />
    <None Include="Assets\ResPackage\Spine\animal_11014\mao4ji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Particle VertexLit Blended.shader" />
    <None Include="Assets\ResPackage\Spine\role_show_1005\ZS_juese33_zhandoufeiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_1018\juese39_zhandoufeiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\frame_vip2_1\VIPkuang2_1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1002_5\shengdanmaxiu.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Nature\TreeCreator\TreeCreatorLeaves.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityCG.glslinc" />
    <None Include="Assets\ResPackage\Spine\hero_3020\shipingbozhu_20.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15094\anvbisi4.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11092\changjinglu2.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\frame_vip7_1\VIPkuang7_1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12023\xiniu3ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_1017\juese12_daodanche.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CameraDepthNormalsTexture.shader" />
    <None Include="Assets\ResPackage\Spine\animal_11044\bianselong04.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\jijie_8_tv\zuoqiudianshi.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_4013\ZS_lianmeng4013_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Particle Alpha Blend.shader" />
    <None Include="Assets\ResPackage\Spine\role_show_3002\ZS_juese42_zhandoufeiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13061\leilong1ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11055\houzi5ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_2004\jiushengyuan.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\ship_dock\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13091\bawanglong01.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_1007\juese8_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13003\yazuishou3ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13015\dujiaoshou5.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15075\banrenma5.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_4022\lianmeng4022_daodan.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_2014\ZS_juese15_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\CBB_Dissolution_Blend_JZB.shader" />
    <None Include="Assets\ResPackage\Spine\animal_12064\shizi04.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_4017\ZS_lianmeng4017_feiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13012\dujiaoshou2ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\meigui_6\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\youwu_chuxian_1\xiyouqidonghua.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-StencilWrite.shader" />
    <None Include="Assets\ResPackage\Spine\slg_role_2007\juese28_feiji.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CGIncludes\SpriteShadows.cginc" />
    <None Include="Assets\ResPackage\Spine\animal_12052\xiong2.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CGIncludes\SpriteVertexLighting.cginc" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResources\PerformanceTools\FrameDebuggerRenderTargetDisplay.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Unlit\Unlit-Normal.shader" />
    <None Include="Assets\ResPackage\Spine\animal_11063\banma3.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15062\meirenyu2.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11034\yingwu4ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1005\xiaochou01.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_3016\taoyishi_jiaose16.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_1004\juese14_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12042\haibao162.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15025\yesha5.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityShaderUtilities.cginc" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CameraDepthTexture.shader" />
    <None Include="Assets\3rd\TextMesh Pro\Shaders\TMP_SDF-Surface.shader" />
    <None Include="Assets\ResPackage\Spine\tuzi\tuzi.atlas.txt" />
    <None Include="Assets\Scripts\SDK\Res\Resources\LoginLanauge\LoginLanguage_en.txt" />
    <None Include="Assets\FacebookSDK\Plugins\Facebook.Unity.dll" />
    <None Include="Assets\ResPackage\Spine\frame_vip5_1\VIPkuang5_1.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\SpeedTreeCommon.cginc" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-GUITexture.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13072\jianlong02.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Lightmap-Glossy.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\lizi_katong.shader" />
    <None Include="Assets\ResPackage\Font\numSlgYellow.fnt" />
    <None Include="Assets\ResPackage\Spine\animal_15035\mihuojushou5.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_hero_3001\juese41_geruiUR.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\tile_11_1\dongtishengchun.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Ghost\Shaders\Spine-Special-Skeleton-Ghost.shader" />
    <None Include="Assets\3rd\Effects Pro\Shaders\Contrast&amp;Brightness.shader" />
    <None Include="Assets\3rd\Demigiant\DOTween\DOTween43.dll" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\SpritePixelLighting.cginc" />
    <None Include="Assets\3rd\TextMesh Pro\Sprites\EmojiOne Attribution.txt" />
    <None Include="Assets\ResPackage\Spine\pharaoh_105\jinzita.atlas.txt" />
    <None Include="Assets\XLua\Resources\perf\profiler.lua.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-ScreenSpaceShadows.shader" />
    <None Include="Assets\3rd\TextMesh Pro\Shaders\TMP_Bitmap-Custom-Atlas.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13027\jianchihu7ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\tile_12_1\haidimibao.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12072\eyu02.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_4021\ZS_lianmeng4021_feiji.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity.txt" />
    <None Include="Assets\3rd\TextMesh Pro\Shaders\TMP_SDF.shader" />
    <None Include="Assets\ResPackage\Spine\animal_15097\anvbisi7.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15076\banrenma6.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Normal-Diffuse.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityStandardBRDF.cginc" />
    <None Include="Assets\ResPackage\Shader\MyParticle Add.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityBuiltin3xTreeLibrary.cginc" />
    <None Include="Assets\ResPackage\Spine\slg_role_4002\guaiwu2_fenghuolun.atlas.txt" />
    <None Include="Assets\ResPackage\Shader\MobileBloom.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBBNoCulling\CBB_NoCulling_Blend_Mask.shader" />
    <None Include="Assets\ResPackage\Spine\hero_3017\jiaoxiangyuewayier-17.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13005\yazuishou5ji.atlas.txt" />
    <None Include="Assets\3rd\Demigiant\DOTween\DOTween.dll" />
    <None Include="Assets\ResPackage\Spine\animal_13096\bawanglong06.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13026\jianchihu6ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15038\mihuojushou8.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12025\xiniu5ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13093\bawanglong03.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Decal.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13035\mengmaxiang05.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15051\meidusha1.atlas.txt" />
    <None Include="Assets\3rd\TextMesh Pro\Shaders\TMP_SDF-Mobile SSD.shader" />
    <None Include="Assets\ResPackage\Shader\UIShader_Gray.shader" />
    <None Include="Assets\ResPackage\Spine\role_show_4015\ZS_lianmeng4015_feiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_2003\juese7_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15004\shijiu4.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\frame_vip3_1\VIPkuang3_1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13051\juying1ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11082\qie02.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\Tessellation.cginc" />
    <None Include="Assets\3rd\TextMesh Pro\Resources\LineBreaking Leading Characters.txt" />
    <None Include="Assets\ResPackage\Spine\frame_vip4_1\VIPkuang4_1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_1008\juese21_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Alpha-Parallax.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Unlit\Unlit-AlphaTest.shader" />
    <None Include="Assets\ResPackage\Spine\zoo_tile_2_1\shengdanxiaohuoceh.atlas.txt" />
    <None Include="Assets\FlexReader\ICSharpCode.SharpZipLib.dll" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResources\base\Font.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Illumin-BumpSpec.shader" />
    <None Include="Assets\ResPackage\TinyGame\BcKillArrow\Shader\CBBNoCulling\BcKillArrowCBB_NoCulling_Blend_Mask.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13023\jianchihu3ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15071\banrenma1.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityDeprecated.cginc" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Reflect-BumpVertexLit.shader" />
    <None Include="Assets\ResPackage\Spine\animal_15013\fenghuang3.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\caiqi_1\caiqi5.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityShaderVariables.cginc" />
    <None Include="Assets\ResPackage\Spine\slg_bosscome_1\slg_bossattack_big.atlas.txt" />
    <None Include="Assets\3rd\UIParticles\Shaders\Ui Glow Additive Simple.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Reflect-Bumped.shader" />
    <None Include="Assets\ResPackage\Spine\animal_15072\banrenma2.atlas.txt" />
    <None Include="Assets\ResPackage\Font\numSlgRed.fnt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\CBB_Additive_Mask.shader" />
    <None Include="Assets\3rd\TextMesh Pro\Shaders\TMPro_Properties.cginc" />
    <None Include="Assets\ResPackage\Spine\windmill_1\fengchedonghua.atlas.txt" />
    <None Include="Assets\ResPackage\Shader\ColorAE.shader" />
    <None Include="Assets\ResPackage\Spine\hero_jijie_1\tutongjiaosekaixi.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1017\jiase17Ada2.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15093\anvbisi3.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_1006\ZS_juese4_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_3013\jiaoshe13kedi.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\jijie_item_11_deng2\yuedneg3.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Nature\TreeCreator\TreeCreatorLeavesFast.shader" />
    <None Include="Assets\ResPackage\Spine\slg_role_4018\lianmeng4018_feiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hua_you_1\zuigaojiebie-youhua.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Normal-VertexLit.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\UI\base\UI-Default.shader" />
    <None Include="Assets\ResPackage\Shader\CommonSea.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\StandardRoughness.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Particle Anim Alpha Blend.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityImageBasedLighting.cginc" />
    <None Include="Assets\ResPackage\Spine\animal_13057\juying7.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13021\jianchihu1ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_2011\ZS_juese30_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\UI\base\UI-Unlit-TextDetail.shader" />
    <None Include="Assets\FacebookSDK\Plugins\Gameroom\Facebook.Unity.Gameroom.dll" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Reflect-BumpSpec.shader" />
    <None Include="Assets\ResPackage\Spine\animal_15022\yesha2.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1001_4\sugelan.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Alpha-ParallaxSpec.shader" />
    <None Include="Assets\ResPackage\Spine\hero_1002_6\shatangsheying.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\frame_vip9_2\VIPkuang9_2.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityCustomRenderTexture.cginc" />
    <None Include="Assets\3rd\TextMesh Pro\Shaders\TMPro_Surface.cginc" />
    <None Include="Assets\ResPackage\TinyGame\BcKillArrow\Shader\CBBNoCulling\BcKillArrowCBB_NoCulling_Additive_Mask.shader" />
    <None Include="Assets\ResPackage\Spine\jijie_item_11_tree\xuyuanshu.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11032\yingwuxiao2.atlas.txt" />
    <None Include="Assets\3rd\Demigiant\DOTween\DOTween46.dll" />
    <None Include="Assets\ResPackage\Spine\frame_vip8_2\VIPkuang8_2.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\xulie_uv_Y.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13037\mengmaxiang07.atlas.txt" />
    <None Include="Assets\FlexReader\readme.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\AR\Shaders\TangoARRender.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\CBB_Niuqu.shader" />
    <None Include="Assets\ResPackage\Spine\zoo_tile_1_3\guihunzhuangyuan.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\item_daoyu_4_2\liushengji_4.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15064\meirenyu4.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11041\bianselong2.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\BcKillArrow\Shader\CGIncludes\EffectDissolveForwardPass.cginc" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Mobile\Mobile-Bumped.shader" />
    <None Include="Assets\ResPackage\Spine\animal_12006\kongque6.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\zoo_tile_2_4\shendanlaoren.atlas.txt" />
    <None Include="Assets\3rd\Demigiant\DemiLib\DemiLib.xml" />
    <None Include="Assets\ResPackage\Spine\youwu_manyan_2\xiyouqidonghua_2.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-DepthNormalsTexture.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13042\she02.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\frame_vip9_1\VIPkuang9_1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11093\changjinglu3.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11083\qie3.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityStandardShadow.cginc" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Mobile\Mobile-VertexLit.shader" />
    <None Include="Assets\ResPackage\Spine\trucks_5\huoche_huang.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12026\xiniu6ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1009\zhiwuyanjiuyuan.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Reflect-BumpNolight.shader" />
    <None Include="Assets\ResPackage\Spine\frame_5_2\touxiang5_xia.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\frame_3_2\touxiang3_xia.atlas.txt" />
    <None Include="Assets\ResPackage\Shader\MySprite_Cloud.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-GUIRoundedRect.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Normal-Bumped.shader" />
    <None Include="Assets\ResPackage\Spine\animal_15066\meirenyu6.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15007\shijiu7.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11074\yangtuo04.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11062\banma2.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_1001\juese26_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12085\tiane5ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11054\houzi4ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\Card_red\kapai-hong.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12048\haixiang_8.atlas.txt" />
    <None Include="Assets\XLua\Resources\perf\memory.lua.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_die\tongyongshiwang.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\frame_2_1\touxiang2_shang.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_2009\ZS_juese25_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\VR\Shaders\ClippingMask.shader" />
    <None Include="Assets\ResPackage\TinyGame\BcKillArrow\Shader\CBB\BcKillArrowCBB_Blend_Mask.shader" />
    <None Include="Assets\ResPackage\TinyGame\BcKillArrow\Shader\CGIncludes\EffectDissolveInput.cginc" />
    <None Include="Assets\ResPackage\Spine\hero_3004\nvhai.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15095\anvbisi5.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\UI\base\UI-Unlit-Text.shader" />
    <None Include="Assets\ResPackage\Spine\animal_15011\fenghuang1.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Illumin-Glossy.shader" />
    <None Include="Assets\ResPackage\Spine\animal_15045\zimingnade5.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15012\fenghuang2.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12046\haibao16.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_3009\simon.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_1002\ZS_juese2_zhishengji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15074\banrenma4.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\VideoDecodeOSX.shader" />
    <None Include="Assets\3rd\Demigiant\DOTweenPro\DOTweenPro.dll" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\CBB_Dissolution_Additvie.shader" />
    <None Include="Assets\3rd\Demigiant\DOTweenPro\readme.txt" />
    <None Include="Assets\3rd\Effects Pro\Shaders\Negative.shader" />
    <None Include="Assets\ResPackage\Spine\animal_15034\mihuojushou4.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13073\jianlong03.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_2012\juese6_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15014\fenghuang4.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_3019\wuadaojiaokalingna_19.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_4001\ZS_guaiwu1_sanlun.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13084\yilong4ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1001\nvzhu.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_3015\qixiangjuxialuote.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_2001\juese11_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\clock_1\icon-clock.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Cubemaps\CubeCopy.shader" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Spine-Special-Skeleton-Grayscale.shader" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\SkeletonGraphic\Shaders\Spine-SkeletonGraphic.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13092\bawanglong02.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\trucks_4\huoche_zi.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\AlphaTest-Bumped.shader" />
    <None Include="Assets\ResPackage\Font\numSlgGreen.fnt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Normal-BumpSpec.shader" />
    <None Include="Assets\3rd\TextMesh Pro\Resources\LineBreaking Following Characters.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11002\labuladuo2ji.atlas.txt" />
    <None Include="Assets\3rd\Effects Pro\Shaders\Sepia.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Sprites-Default_1.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\Lighting.cginc" />
    <None Include="Assets\ResPackage\Spine\animal_12033\haidiao17.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\jijie_balloon_7\yunduoqiqiu.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\tile_6_1\siluodeyiji.atlas.txt" />
    <None Include="Assets\Scripts\SDK\Res\Resources\LoginLanauge\LoginLanguage_ch.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBBNoCulling\NoCulling_xulie_uv_X.shader" />
    <None Include="Assets\ResPackage\Spine\hero_1008_1\8baise.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_1013\ZS_juese20_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_4002\ZS_guaiwu2_fenghuolun.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Sprites-Mask.shader" />
    <None Include="Assets\ResPackage\Spine\frame_vip8_1\VIPkuang8_1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_2019\feizhizhanshi_2019.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_1010\juese36_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15017\fenghuang7.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_2020\ZS_juese38_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_1020\ZS_juese5_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12081\tiane1ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13071\jianlong01.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_2017\ZS_juese10_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_3001\ZS_juese41_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\jijie_light_1\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_1002\juese2_zhishengji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_4015\lianmeng4015_feiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\jijie_balloon_11\xiarijiqiaiu.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\jijie_qiqiufly\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1019\jiaose19luojie.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Lightmap-Bumped.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13004\yazuishou4ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15036\mihuojushou6.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Shaders\BlendModes\Spine-Skeleton-PMA-Multiply.shader" />
    <None Include="Assets\ResPackage\Spine\pharaoh_104\shibei.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\VR\Shaders\SpatialMappingOcclusion.shader" />
    <None Include="Assets\ResPackage\Spine\role_show_1009\ZS_juese31_zhandoufeiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12015\xingxing5.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\UI\base\UI-Lit-Transparent.shader" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\SpriteDepthNormalsTexture.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Normal-Parallax.shader" />
    <None Include="Assets\ResPackage\Spine\animal_11064\banma4.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\land_1_9base_2\zhuangshi5.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15052\meidusha2.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_2008\juese22_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\magnifying_glass_1\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\cat_island_1\maonpc2.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12065\shizi05.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12051\xiong1.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityPBSLighting.cginc" />
    <None Include="Assets\3rd\Demigiant\DOTweenPro\DOTweenPro.XML" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResources\base\internalskinning.compute" />
    <None Include="Assets\ResPackage\Spine\role_show_3003\ZS_juese43_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_1015\ZS_juese35_zhandoufeiji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityStandardUtils.cginc" />
    <None Include="Assets\ResPackage\Spine\animal_15001\shijiu1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1011\yisheng.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_3011\jiaoshe11.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Cubemaps\CubeBlur.shader" />
    <None Include="Assets\FacebookSDK\Plugins\Android\Facebook.Unity.Android.dll" />
    <None Include="Assets\ResPackage\Spine\role_show_1011\ZS_juese17_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_1017\ZS_juese12_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\zoo_tile_1_4\mulaiyichuxing.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CGIncludes\SpriteLighting.cginc" />
    <None Include="Assets\ResPackage\Spine\animal_15053\meidusha3.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_2020\juese38_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15088\miluotao8.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\land_1_9base_1\dengguang.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_4003\guaiwu3_xiaobai.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\SpritesVertexLit.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Lightmap-Diffuse.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13032\mengmaxiang02.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12053\xiong3.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\TerrainShaders\Details\WavingGrass.shader" />
    <None Include="Assets\ResPackage\Spine\animal_11011\mao1ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\chengbao12_1\feichuanjianzhu.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1014\14meijiashi.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15015\fenghuang5.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\Tree.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Mobile\Mobile-Lightmap-Unlit.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13044\she04.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\jijie_butterfly_1\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_2008\ZS_juese22_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15068\meirenyu8.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\chengbao10_1\jianshengfang.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Nature\TreeCreator\TreeCreatorBarkRendertex.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Nature\TreeCreator\TreeCreatorLeavesRendertex.shader" />
    <None Include="Assets\ResPackage\Spine\slg_role_4011\lianmeng4011_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\caidai_1\zuigaojibie_1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11091\changjinglu1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13052\juying2ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\bird_1\fengchedonghua3.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\CBB_Additive.shader" />
    <None Include="Assets\ResPackage\Spine\pharaoh_103\fanaowang3.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_2002\ZS_juese23_zhandoufeiji.atlas.txt" />
    <None Include="Assets\Scripts\SDK\Res\Resources\LoginLanauge\Privacy Policy_en.txt" />
    <None Include="Assets\3rd\TextMesh Pro\Shaders\TMP_SDF-Mobile Masking.shader" />
    <None Include="Assets\ResPackage\Spine\slg_role_2014\juese15_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\xichen_2\xiyouqidonghua.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityMetaPass.cginc" />
    <None Include="Assets\ResPackage\TinyGame\DLYShuiGuan\Shader\Mask.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13041\she01.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Flare.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\CBB_Dissolution_blend.shader" />
    <None Include="Assets\ResPackage\TinyGame\BcKillArrow\Shader\Eff\BcKillArrowHightLight.shader" />
    <None Include="Assets\ResPackage\Spine\animal_11023\tuoniao3ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_3007\jiaose-chushilichade.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Illumin-ParallaxSpec.shader" />
    <None Include="Assets\ResPackage\Spine\frame_1_1\touxiang1_shang.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\DLYShuiGuan\Shader\Water.shader" />
    <None Include="Assets\3rd\Demigiant\DOTween\readme.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15085\miluotao5.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_3002\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\AlphaTest-BumpSpec.shader" />
    <None Include="Assets\ResPackage\Spine\hero_3006\kate.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12031\haidiao171.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15026\yesha6.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\UI\base\UI-Lit-RefractionDetail.shader" />
    <None Include="Assets\3rd\UIParticles\Shaders\UI Particle Alpha Blend.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Lightmap-VertexLit.shader" />
    <None Include="Assets\ResPackage\Spine\jijie_balloon_8\piaofuzuoqiu.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\youwu_manyan_4\xiyouqidonghua_4.atlas.txt" />
    <None Include="Assets\Parse\Plugins\dotNet45\Unity.Compat.dll" />
    <None Include="Assets\ResPackage\Spine\animal_12086\tiane06.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13056\juying06.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11052\houzi2.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\TerrainShaders\Splats\Standard-AddPass.shader" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\CustomMaterials\SkeletonRendererCustomMaterials.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12063\shizi3ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12094\xiongmao4ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_3002_1\xinyuanxiaoxiong_danie.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11021\tuoniao1ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1004_1\meilihaidao.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-BlitCopyDepth.shader" />
    <None Include="Assets\ResPackage\Spine\slg_role_4016\lianmeng4016_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12093\xiongmao3ji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\SpeedTreeVertex.cginc" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-MotionVectors.shader" />
    <None Include="Assets\ResPackage\Spine\animal_15078\banrenma8.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1003_2\meiguiyuanchahui.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Alpha-Bumped.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13002\yazuishou2ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15057\meidusha6.atlas.txt" />
    <None Include="Assets\ResPackage\Proto\ProtoConfig.txt" />
    <None Include="Assets\ResPackage\Spine\hero_2002\gongren12_hong.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\event_38_5\yu.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\BcKillArrow\Shader\Eff\Karos_Utility.cginc" />
    <None Include="Assets\ResPackage\Spine\hero_1002_4\maxiuzhuangban4.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-GUITextureClip.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Reflect-Glossy.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Mobile\Mobile-Particle-Add.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13066\leilong6ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11051\houzi1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1002_1\chaojiwan.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\UI\base\UI-Lit-Refraction.shader" />
    <None Include="Assets\ResPackage\Shader\Spine-SkeletonLit.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13087\yilong7ji.atlas.txt" />
    <None Include="Assets\Parse\Plugins\dotNet45\Unity.Tasks.dll" />
    <None Include="Assets\ResPackage\Spine\animal_15086\miluotao6.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\frame_vip1_1\VIPkuang1_1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1006\yaogunmingxing.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12004\kongque4ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_4017\lianmeng4017_feiji.atlas.txt" />
    <None Include="Assets\3rd\TextMesh Pro\Shaders\TMP_SDF Overlay.shader" />
    <None Include="Assets\ResPackage\Spine\animal_12041\haibao161.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\zoo_tile_2_5\shanyaoshendanshu.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityUI.cginc" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Mobile\Mobile-Particle-Alpha-VertexLit.shader" />
    <None Include="Assets\ResPackage\Spine\zoo_tile_1_5\youlindidao.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-GUITextureClipText.shader" />
    <None Include="Assets\ResPackage\Spine\hero_1001_6\maijixiaruji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Unlit\Unlit-Color.shader" />
    <None Include="Assets\ResPackage\Spine\frame_vip6_1\VIPkuang6_1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\youwu_penquan_1\penquan.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_2016\juese13_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\BcKillArrow\Shader\Eff\Karos_Standard.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13033\mengmaxiang03.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\frame_vip7_2\VIPkuang7_2.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\UI\base\UI-DefaultFont.shader" />
    <None Include="Assets\ResPackage\Spine\hero_1018\jiaoshe18bangqiushou.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_2007\ZS_juese28_feiji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Nature\TreeCreator\TreeCreatorLeavesFastOptimized.shader" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Shaders\Spine-Skeleton.shader" />
    <None Include="Assets\3rd\TextMesh Pro\Shaders\TMP_Bitmap-Mobile.shader" />
    <None Include="Assets\ResPackage\Spine\role_show_2010\ZS_juese29_zhandoufeiji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBBNoCulling\NoCulling_SpriteUVAni.shader" />
    <None Include="Assets\ResPackage\Spine\role_show_2013\ZS_juese1_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_4016\ZS_lianmeng4016_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15031\mihuojushou1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12082\tiane02.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\ship_1\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11042\bianselong2.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Particle Blend.shader" />
    <None Include="Assets\ResPackage\Spine\animal_15028\yesha8.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\CBB_Fresnel_Opaque.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Mobile\Mobile-BumpSpec.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Illumin-Parallax.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Illumin-Bumped.shader" />
    <None Include="Assets\XLua\Gen\link.xml" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Nature\TreeCreator\TreeCreatorLeavesOptimized.shader" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Shaders\BlendModes\Spine-Skeleton-PMA-Screen.shader" />
    <None Include="Assets\ResPackage\Spine\role_show_2006\ZS_juese16_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityLightingCommon.cginc" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Nature\base\SpeedTreeBillboard.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13076\jianlong6.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1010\jianshenjiaolian.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1008\tuhao.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_1001\ZS_juese26_daodanche.atlas.txt" />
    <None Include="Assets\3rd\TextMesh Pro\Fonts\LiberationSans - OFL.txt" />
    <None Include="Assets\ResPackage\Shader\MyUIAlpha Blend.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityStandardCoreForward.cginc" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\TerrainShaders\Splats\AddPass.shader" />
    <None Include="Assets\ResPackage\Spine\slg_role_3002\juese42_zhandoufeiji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Normal-DiffuseFast.shader" />
    <None Include="Assets\3rd\Demigiant\DOTween\DOTween50.dll" />
    <None Include="Assets\ResPackage\Spine\slg_role_3001\juese41_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\TerrainShaders\Details\VertexLit.shader" />
    <None Include="Assets\ResPackage\Spine\animal_13001\yazuishou1ji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\AlphaTest-VertexLit.shader" />
    <None Include="Assets\ResPackage\Spine\hero_4001\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\Shader\Additive.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Mobile\Mobile-BumpSpec-1DirectionalLight.shader" />
    <None Include="Assets\ResPackage\Spine\animal_11001\labuladuo1ji.atlas.txt" />
    <None Include="Assets\XLua\Resources\tdr\tdr.lua.txt" />
    <None Include="Assets\ResPackage\TinyGame\BcKillArrow\Shader\CBBNoCulling\BcKillArrowCBB_NoCulling_Additive.shader" />
    <None Include="Assets\ResPackage\Spine\hero_3001\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13011\dujiaoshou1ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\zoo_tile_2_3\shendanxinjian.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12047\haixiang_9.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_1009\juese31_zhandoufeiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_1008\ZS_juese21_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Particle AddMultiply.shader" />
    <None Include="Assets\ResPackage\Spine\frame_vip6_2\VIPkuang6_2.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11071\yangtuo01.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\ShaderShared.cginc" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBB\CBB_Fresnel_Animation.shader" />
    <None Include="Assets\ResPackage\Spine\role_show_4003\ZS_guaiwu3_xiaobai.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBBNoCulling\CBB_NoCulling_Additive.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\GIDebug\VertexColors.shader" />
    <None Include="Assets\ResPackage\Spine\role_show_2005\ZS_juese18_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-PrePassLighting.shader" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Shaders\Spine-Skeleton-TintBlack.shader" />
    <None Include="Assets\ResPackage\Spine\slg_role_1005\juese33_zhandoufeiji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResources\base\Internal-Colored.shader" />
    <None Include="Assets\ResPackage\Spine\hero_1005_1\xiaochou.atlas.txt" />
    <None Include="Assets\3rd\TextMesh Pro\Shaders\TMP_SDF-Mobile.shader" />
    <None Include="Assets\ResPackage\Shader\MySprite_Highlight_Anim.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Mobile\Mobile-Particle-Alpha.shader" />
    <None Include="Assets\ResPackage\Spine\jijie_item_11_denglong\meng.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\VR\Shaders\VideoBackground.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResources\base\Internal-ErrorShader.shader" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\SpriteShadows.cginc" />
    <None Include="Assets\ResPackage\Spine\frame_vip10_1\VIPkuang10_1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1009_1\yanjiuyanyongzhuang.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_1014\juese9_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11075\yangtuo05.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13006\yazuishou6ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11085\qie05.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12044\haibao164.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\arena_badge_6\duanwei_6.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_11022\tuoniao2ji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_3003\skeleton.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1002_2\maxiu.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\HLSLSupport.cginc" />
    <None Include="Assets\ResPackage\Spine\animal_11035\yingwu.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1007\tiaojiushi.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_4012\lianmeng4012_feiji.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\arena_badge_4\duanwei_4.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_2013\juese1_daodanche.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_hero_3002\juese42_sikailaSSR.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hua_zuo_1\zuigaohua_zuo.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityStandardInput.cginc" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Nature\SoftOcclusion\TreeSoftOcclusionLeaves.shader" />
    <None Include="Assets\ResPackage\Shader\Spine-Skeleton-Color_UIMask.shader" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\SpritesPixelLit.shader" />
    <None Include="Assets\ResPackage\Spine\animal_11043\bianselong03.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_15047\zimingnade7.atlas.txt" />
    <None Include="Assets\3rd\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CGIncludes\SpritePixelLighting.cginc" />
    <None Include="Assets\ResPackage\Spine\role_show_1007\ZS_juese8_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_12092\xiongmao2ji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CBBNoCulling\CBB_NoCulling_Fresnel_Opaque.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-ConvertTexture.shader" />
    <None Include="Assets\ResPackage\Spine\frame_2_2\touxiang2_xia.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\role_show_4004\ZS_guaiwu4_bosstanke.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_2015\juese24_zhandoufeiji.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-DeferredShading.shader" />
    <None Include="Assets\ResPackage\Spine\slg_role_1003\juese3_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityGBuffer.cginc" />
    <None Include="Assets\ResPackage\Spine\animal_15065\meirenyu5.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\FlexyRing\Shader\PolyLitSurface.shader" />
    <None Include="Assets\ResPackage\Spine\animal_15003\shijiu3.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13043\she03.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13077\jianlong07.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\animal_13047\she07.atlas.txt" />
    <None Include="Assets\3rd\TextMesh Pro\Shaders\TMP_Sprite.shader" />
    <None Include="Assets\FacebookSDK\Plugins\Gameroom\FacebookNamedPipeClient.dll" />
    <None Include="Assets\ResPackage\Spine\role_show_2003\ZS_juese7_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Mobile\Mobile-Particle-Multiply.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\SpeedTreeBillboardCommon.cginc" />
    <None Include="Assets\ResPackage\Spine\animal_12096\xiongmao6ji.atlas.txt" />
    <None Include="Assets\TinyGame\Plugins\3rdLib\Newtonsoft.Json.dll" />
    <None Include="Assets\ResPackage\Spine\slg_role_4004\guaiwu4_bosstanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Particle Add.shader" />
    <None Include="Assets\ResPackage\Spine\animal_15091\anvbisi1.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Internal-Flare.shader" />
    <None Include="Assets\ResPackage\Spine\event_38_1\niaoxinju.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\hero_1007_1\7chonglang.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\CGIncludes\UnityStandardConfig.cginc" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\StandardSpecular.shader" />
    <None Include="Assets\ResPackage\Shader\CircleShader.shader" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\base\Skybox-Procedural.shader" />
    <None Include="Assets\ResPackage\Spine\animal_15061\meirenyu1.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_2018\juese40_tanke.atlas.txt" />
    <None Include="Assets\ResPackage\TinyGame\Empty\Shaders\DefaultResourcesExtra\Mobile\Mobile-Diffuse.shader" />
    <None Include="Assets\ResPackage\Spine\animal_12043\haibao163.atlas.txt" />
    <None Include="Assets\ResPackage\Spine\slg_role_2006\juese16_tanke.atlas.txt" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="wx-runtime-editor">
      <HintPath>Assets\WX-WASM-SDK-V2\Runtime\Plugins\wx-runtime-editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Compat">
      <HintPath>Assets\Parse\Plugins\dotNet45\Unity.Compat.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.FontABTool">
      <HintPath>Assets\WX-WASM-SDK-V2\Runtime\Plugins\Unity.FontABTool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Sirenix.Serialization">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>Assets\Plugins\Third\Protobuf\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Tasks">
      <HintPath>Assets\Parse\Plugins\dotNet45\Unity.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="LitJson">
      <HintPath>Assets\WX-WASM-SDK-V2\Runtime\Plugins\LitJson.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Sirenix.Serialization.Config">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.Serialization.Config.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Sirenix.OdinInspector.Editor">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinInspector.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityStore">
      <HintPath>Assets\Plugins\UnityChannel\UnityStore.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Facebook.Unity.Settings">
      <HintPath>Assets\FacebookSDK\Plugins\Settings\Facebook.Unity.Settings.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DemiLib">
      <HintPath>Assets\3rd\Demigiant\DemiLib\DemiLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Sirenix.OdinInspector.Attributes">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinInspector.Attributes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Google.Protobuf">
      <HintPath>Assets\Plugins\Third\Protobuf\Google.Protobuf.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Facebook.Unity">
      <HintPath>Assets\FacebookSDK\Plugins\Facebook.Unity.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween43">
      <HintPath>Assets\3rd\Demigiant\DOTween\DOTween43.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Sirenix.Utilities">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.Utilities.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets\3rd\Demigiant\DOTween\DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib">
      <HintPath>Assets\FlexReader\ICSharpCode.SharpZipLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Facebook.Unity.Gameroom">
      <HintPath>Assets\FacebookSDK\Plugins\Gameroom\Facebook.Unity.Gameroom.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween46">
      <HintPath>Assets\3rd\Demigiant\DOTween\DOTween46.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenPro">
      <HintPath>Assets\3rd\Demigiant\DOTweenPro\DOTweenPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ChannelPurchase">
      <HintPath>Assets\Plugins\UnityChannel\ChannelPurchase.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UDP">
      <HintPath>Assets\Plugins\UDP\UDP.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Sirenix.Utilities.Editor">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.Utilities.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween50">
      <HintPath>Assets\3rd\Demigiant\DOTween\DOTween50.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="FacebookNamedPipeClient">
      <HintPath>Assets\FacebookSDK\Plugins\Gameroom\FacebookNamedPipeClient.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Assets\TinyGame\Plugins\3rdLib\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="wx-perf">
      <HintPath>Assets\WX-WASM-SDK-V2\Runtime\Plugins\wx-perf.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.GradleProject">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.GradleProject.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\ref\2.1.0\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Windows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Assembly-CSharp-firstpass.csproj" />
    <ProjectReference Include="FancyScrollView.csproj" />
    <ProjectReference Include="Unity.AI.Navigation.csproj" />
    <ProjectReference Include="UnityWebSocket.Editor.csproj" />
    <ProjectReference Include="Sirenix.OdinInspector.Modules.UnityMathematics.csproj" />
    <ProjectReference Include="Unity.Timeline.Editor.csproj" />
    <ProjectReference Include="Lofelt.NiceVibrations.Editor.csproj" />
    <ProjectReference Include="IngameDebugConsole.Editor.csproj" />
    <ProjectReference Include="Unity.VisualStudio.Editor.csproj" />
    <ProjectReference Include="Sirenix.OdinInspector.CompatibilityLayer.Editor.csproj" />
    <ProjectReference Include="UnityEditor.UI.csproj" />
    <ProjectReference Include="UnityEngine.UI.csproj" />
    <ProjectReference Include="Unity.Rider.Editor.csproj" />
    <ProjectReference Include="Lofelt.NiceVibrations.csproj" />
    <ProjectReference Include="Unity.2D.Sprite.Editor.csproj" />
    <ProjectReference Include="CasualGame.Dreamteck.Splines.Editor.csproj" />
    <ProjectReference Include="Sirenix.OdinInspector.CompatibilityLayer.csproj" />
    <ProjectReference Include="Unity.Mathematics.Editor.csproj" />
    <ProjectReference Include="Wx.csproj" />
    <ProjectReference Include="CasualGame.Dreamteck.Splines.csproj" />
    <ProjectReference Include="Unity.Timeline.csproj" />
    <ProjectReference Include="AppleAuth.Editor.csproj" />
    <ProjectReference Include="FancyScrollView.Editor.csproj" />
    <ProjectReference Include="CasualGame.Dreamteck.Utilities.csproj" />
    <ProjectReference Include="CasualGame.Dreamteck.Utilities.Editor.csproj" />
    <ProjectReference Include="Unity.AI.Navigation.Updater.csproj" />
    <ProjectReference Include="Unity.PlasticSCM.Editor.csproj" />
    <ProjectReference Include="WxEditor.csproj" />
    <ProjectReference Include="Unity.Mathematics.csproj" />
    <ProjectReference Include="UnityWebSocket.Runtime.csproj" />
    <ProjectReference Include="Unity.TextMeshPro.csproj" />
    <ProjectReference Include="Unity.TextMeshPro.Editor.csproj" />
    <ProjectReference Include="Unity.AI.Navigation.Editor.csproj" />
    <ProjectReference Include="Unity.AI.Navigation.Editor.ConversionSystem.csproj" />
    <ProjectReference Include="UIEffect.csproj" />
    <ProjectReference Include="IngameDebugConsole.Runtime.csproj" />
    <ProjectReference Include="Unity.VSCode.Editor.csproj" />
    <ProjectReference Include="AppleAuth.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
