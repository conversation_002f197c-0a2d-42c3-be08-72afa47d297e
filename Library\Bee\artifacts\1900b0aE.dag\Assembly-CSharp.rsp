-target:library
-out:"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll"
-refout:"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.ref.dll"
-define:UNITY_2022_3_62
-define:UNITY_2022_3
-define:UNITY_2022
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_VIDEO
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION
-define:ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT
-define:ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE
-define:PLATFORM_STANDALONE
-define:TEXTCORE_1_0_OR_NEWER
-define:PLATFORM_STANDALONE_WIN
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:UNITY_UGP_API
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_INITIALIZES_MEMORY_MANAGER_EXPLICITLY
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:ODIN_INSPECTOR
-define:ODIN_INSPECTOR_3
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/3rd/Demigiant/DemiLib/DemiLib.dll"
-r:"Assets/3rd/Demigiant/DOTween/DOTween.dll"
-r:"Assets/3rd/Demigiant/DOTween/DOTween43.dll"
-r:"Assets/3rd/Demigiant/DOTween/DOTween46.dll"
-r:"Assets/3rd/Demigiant/DOTween/DOTween50.dll"
-r:"Assets/3rd/Demigiant/DOTweenPro/DOTweenPro.dll"
-r:"Assets/FacebookSDK/Plugins/Facebook.Unity.dll"
-r:"Assets/FacebookSDK/Plugins/Gameroom/Facebook.Unity.Gameroom.dll"
-r:"Assets/FacebookSDK/Plugins/Gameroom/FacebookNamedPipeClient.dll"
-r:"Assets/FacebookSDK/Plugins/Settings/Facebook.Unity.Settings.dll"
-r:"Assets/FlexReader/ICSharpCode.SharpZipLib.dll"
-r:"Assets/Parse/Plugins/dotNet45/Unity.Compat.dll"
-r:"Assets/Parse/Plugins/dotNet45/Unity.Tasks.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.OdinInspector.Attributes.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.OdinInspector.Editor.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.Serialization.Config.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.Serialization.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.Utilities.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.Utilities.Editor.dll"
-r:"Assets/Plugins/Third/Protobuf/Google.Protobuf.dll"
-r:"Assets/Plugins/Third/Protobuf/System.Runtime.CompilerServices.Unsafe.dll"
-r:"Assets/Plugins/UDP/UDP.dll"
-r:"Assets/Plugins/UnityChannel/ChannelPurchase.dll"
-r:"Assets/Plugins/UnityChannel/UnityStore.dll"
-r:"Assets/TinyGame/Plugins/3rdLib/Newtonsoft.Json.dll"
-r:"Assets/WX-WASM-SDK-V2/Runtime/Plugins/LitJson.dll"
-r:"Assets/WX-WASM-SDK-V2/Runtime/Plugins/Unity.FontABTool.dll"
-r:"Assets/WX-WASM-SDK-V2/Runtime/Plugins/wx-perf.dll"
-r:"Assets/WX-WASM-SDK-V2/Runtime/Plugins/wx-runtime-editor.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.GradleProject.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll"
-r:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/AppleAuth.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UIEffect.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Wx.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/WxEditor.ref.dll"
-analyzer:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
"Assets/3rd/AssetBundleManager/AssetBundleConfig.cs"
"Assets/3rd/AssetBundleManager/AssetBundleLoadOperation.cs"
"Assets/3rd/AssetBundleManager/AssetBundleManager.cs"
"Assets/3rd/AssetBundleManager/Utility.cs"
"Assets/3rd/Brotli/brotli.cs"
"Assets/3rd/Demigiant/DOTween/Modules/DOTweenModuleUI.cs"
"Assets/3rd/Demigiant/DOTweenPro/DOTweenAnimation.cs"
"Assets/3rd/Effects Pro/Scripts/Effects.cs"
"Assets/3rd/Hierarchy 2/Runtime/CustomRowItem.cs"
"Assets/3rd/Hierarchy 2/Runtime/HierarchyLocalData.cs"
"Assets/3rd/Hierarchy 2/Runtime/Texture2DExtensions.cs"
"Assets/3rd/Hierarchy 2/Runtime/UIElements.cs"
"Assets/3rd/Hierarchy 2/Runtime/VisualElementExstensions.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Animation.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/AnimationState.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/AnimationStateData.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Atlas.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Attachments/AtlasAttachmentLoader.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Attachments/Attachment.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Attachments/AttachmentLoader.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Attachments/AttachmentType.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Attachments/BoundingBoxAttachment.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Attachments/ClippingAttachment.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Attachments/MeshAttachment.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Attachments/PathAttachment.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Attachments/PointAttachment.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Attachments/RegionAttachment.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Attachments/VertexAttachment.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/BlendMode.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Bone.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/BoneData.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Event.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/EventData.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/ExposedList.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/IConstraint.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/IkConstraint.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/IkConstraintData.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/IUpdatable.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Json.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/MathUtils.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/PathConstraint.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/PathConstraintData.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Skeleton.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/SkeletonBinary.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/SkeletonBounds.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/SkeletonClipping.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/SkeletonData.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/SkeletonJson.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Skin.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Slot.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/SlotData.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/TransformConstraint.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/TransformConstraintData.cs"
"Assets/3rd/Spine/Runtime/spine-csharp/Triangulator.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Asset Types/AnimationReferenceAsset.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Asset Types/AtlasAssetBase.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Asset Types/BlendModeMaterialsAsset.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Asset Types/EventDataReferenceAsset.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Asset Types/RegionlessAttachmentLoader.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Asset Types/SkeletonDataAsset.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Asset Types/SkeletonDataModifierAsset.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Asset Types/SpineAtlasAsset.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Components/BoneFollower.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Components/PointFollower.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Components/SkeletonAnimation.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Components/SkeletonMecanim.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Components/SkeletonRenderer.cs"
"Assets/3rd/Spine/Runtime/spine-unity/ISkeletonAnimation.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Mesh Generation/DoubleBuffered.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Mesh Generation/SpineMesh.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/AnimationMatchModifier/AnimationMatchModifierAsset.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/AttachmentTools/AttachmentTools.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/BoundingBoxFollower/BoundingBoxFollower.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/CustomMaterials/SkeletonRendererCustomMaterials.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/Ghost/SkeletonGhost.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/Ghost/SkeletonGhostRenderer.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/Ragdoll/SkeletonRagdoll.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/Ragdoll/SkeletonRagdoll2D.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/SkeletonGraphic/BoneFollowerGraphic.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/SkeletonGraphic/SkeletonGraphic.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/SkeletonGraphic/SkeletonGraphicMirror.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/SkeletonRenderSeparator/SkeletonPartsRenderer.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/SkeletonRenderSeparator/SkeletonRenderSeparator.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/SkeletonUtility Modules/SkeletonUtilityEyeConstraint.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/SkeletonUtility Modules/SkeletonUtilityGroundConstraint.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/SkeletonUtility Modules/SkeletonUtilityKinematicShadow.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/SlotBlendModes/SlotBlendModes.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/Timeline/PlayableHandle Component/SkeletonAnimationPlayableHandle.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/Timeline/PlayableHandle Component/SpinePlayableHandleBase.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/Timeline/SpineAnimationState/SpineAnimationStateBehaviour.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/Timeline/SpineAnimationState/SpineAnimationStateClip.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/Timeline/SpineAnimationState/SpineAnimationStateMixerBehaviour.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/Timeline/SpineAnimationState/SpineAnimationStateTrack.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/Timeline/SpineSkeletonFlip/SpineSkeletonFlipBehaviour.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/Timeline/SpineSkeletonFlip/SpineSkeletonFlipClip.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/Timeline/SpineSkeletonFlip/SpineSkeletonFlipMixerBehaviour.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/Timeline/SpineSkeletonFlip/SpineSkeletonFlipTrack.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/TK2D/SpriteCollectionAttachmentLoader.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/YieldInstructions/WaitForSpineAnimationComplete.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/YieldInstructions/WaitForSpineEvent.cs"
"Assets/3rd/Spine/Runtime/spine-unity/Modules/YieldInstructions/WaitForSpineTrackEntryEnd.cs"
"Assets/3rd/Spine/Runtime/spine-unity/SkeletonExtensions.cs"
"Assets/3rd/Spine/Runtime/spine-unity/SkeletonUtility/SkeletonUtility.cs"
"Assets/3rd/Spine/Runtime/spine-unity/SkeletonUtility/SkeletonUtilityBone.cs"
"Assets/3rd/Spine/Runtime/spine-unity/SkeletonUtility/SkeletonUtilityConstraint.cs"
"Assets/3rd/Spine/Runtime/spine-unity/SpineAttributes.cs"
"Assets/3rd/UIParticles/Scripts/SetPropertyUtility.cs"
"Assets/3rd/UIParticles/Scripts/UiParticles.cs"
"Assets/3rd/Unity-Logs-Viewer/Reporter/MultiKeyDictionary.cs"
"Assets/3rd/Unity-Logs-Viewer/Reporter/Reporter.cs"
"Assets/3rd/Unity-Logs-Viewer/Reporter/ReporterGUI.cs"
"Assets/3rd/Unity-Logs-Viewer/Reporter/ReporterMessageReceiver.cs"
"Assets/3rd/Unity-Logs-Viewer/Reporter/Test/Rotate.cs"
"Assets/3rd/Unity-Logs-Viewer/Reporter/Test/TestReporter.cs"
"Assets/EditorScript/EditorListener.cs"
"Assets/EditorScript/EditorStyleViewer.cs"
"Assets/EditorScript/Game/DirectoryWatcher.cs"
"Assets/EditorScript/Game/LuaFileWatcher.cs"
"Assets/EditorScript/MapEditorEventWin.cs"
"Assets/EditorScript/MapEditorTiled.cs"
"Assets/EditorScript/MissionEditor.cs"
"Assets/FlexReader/Converter/CustomConverter.cs"
"Assets/FlexReader/Converter/CustomConverters/ArrayConverter.cs"
"Assets/FlexReader/Converter/CustomConverters/Color32Converter.cs"
"Assets/FlexReader/Converter/CustomConverters/ColorConverter.cs"
"Assets/FlexReader/Converter/CustomConverters/DictionaryConverter.cs"
"Assets/FlexReader/Converter/CustomConverters/ListConverter.cs"
"Assets/FlexReader/Converter/CustomConverters/ObjectConverter.cs"
"Assets/FlexReader/Converter/CustomConverters/RectConverter.cs"
"Assets/FlexReader/Converter/CustomConverters/Vector2Converter.cs"
"Assets/FlexReader/Converter/CustomConverters/Vector3Converter.cs"
"Assets/FlexReader/Converter/CustomConverters/Vector4Converter.cs"
"Assets/FlexReader/Converter/Extensions.cs"
"Assets/FlexReader/Converter/IConverter.cs"
"Assets/FlexReader/Converter/Validator.cs"
"Assets/FlexReader/Converter/ValueConverter.cs"
"Assets/FlexReader/Core/Address.cs"
"Assets/FlexReader/Core/Cell.cs"
"Assets/FlexReader/Core/ICloneable.cs"
"Assets/FlexReader/Core/Range.cs"
"Assets/FlexReader/Core/Row.cs"
"Assets/FlexReader/Core/Table.cs"
"Assets/FlexReader/CSV/Document.cs"
"Assets/FlexReader/Excel2007/SharedStringCollection.cs"
"Assets/FlexReader/Excel2007/WorkBook.cs"
"Assets/FlexReader/Excel2007/WorkSheet.cs"
"Assets/FlexReader/Mapping/ColumnAttribute.cs"
"Assets/FlexReader/Mapping/IGenerator.cs"
"Assets/FlexReader/Mapping/ITableGenerator.cs"
"Assets/FlexReader/Mapping/Mapper.cs"
"Assets/FlexReader/Mapping/MapperBase.cs"
"Assets/FlexReader/Mapping/Mapping.cs"
"Assets/FlexReader/Mapping/TableAttribute.cs"
"Assets/FlexReader/Mapping/TableMapper.cs"
"Assets/FlexReader/Mapping/TableMapperBase.cs"
"Assets/GoogleSignIn/Plugins/Android/AndroidGoogleSignIn/AndroidGoogleSignIn.cs"
"Assets/GoogleSignIn/Plugins/Android/AndroidGoogleSignIn/AndroidGoogleSignInAccount.cs"
"Assets/IronSource/Scripts/AndroidAgent.cs"
"Assets/IronSource/Scripts/iOSAgent.cs"
"Assets/IronSource/Scripts/IronSource.cs"
"Assets/IronSource/Scripts/IronSourceBannerAndroid.cs"
"Assets/IronSource/Scripts/IronSourceConfig.cs"
"Assets/IronSource/Scripts/IronSourceConstants.cs"
"Assets/IronSource/Scripts/IronSourceError.cs"
"Assets/IronSource/Scripts/IronSourceEvents.cs"
"Assets/IronSource/Scripts/IronSourceEventsDispatcher.cs"
"Assets/IronSource/Scripts/IronSourceIAgent.cs"
"Assets/IronSource/Scripts/IronSourceImpressionData.cs"
"Assets/IronSource/Scripts/IronSourceImpressionDataAndroid.cs"
"Assets/IronSource/Scripts/IronSourceInitializationAndroid.cs"
"Assets/IronSource/Scripts/IronSourceInitilizer.cs"
"Assets/IronSource/Scripts/IronSourceInterstitialAndroid.cs"
"Assets/IronSource/Scripts/IronSourceJSON.cs"
"Assets/IronSource/Scripts/IronSourceMediationSettings.cs"
"Assets/IronSource/Scripts/IronSourceOfferwallAndroid.cs"
"Assets/IronSource/Scripts/IronSourcePlacement.cs"
"Assets/IronSource/Scripts/IronSourceRewardedVideoAndroid.cs"
"Assets/IronSource/Scripts/IronSourceRewardedVideoManualAndroid.cs"
"Assets/IronSource/Scripts/IronSourceSegment.cs"
"Assets/IronSource/Scripts/IronSourceSegmentAndroid.cs"
"Assets/IronSource/Scripts/IronSourceUtils.cs"
"Assets/IronSource/Scripts/IUnityBanner.cs"
"Assets/IronSource/Scripts/IUnityImpressionData.cs"
"Assets/IronSource/Scripts/IUnityInitialization.cs"
"Assets/IronSource/Scripts/IUnityInterstitial.cs"
"Assets/IronSource/Scripts/IUnityOfferwall.cs"
"Assets/IronSource/Scripts/IUnityRewardedVideo.cs"
"Assets/IronSource/Scripts/IUnityRewardedVideoManual.cs"
"Assets/IronSource/Scripts/IUnitySegment.cs"
"Assets/IronSource/Scripts/UnsupportedPlatformAgent.cs"
"Assets/Scripts/Common/CompressTookit.cs"
"Assets/Scripts/Common/CustomScrollRect.cs"
"Assets/Scripts/Common/DataConfig.cs"
"Assets/Scripts/Common/Encrypt/AesRijndael.cs"
"Assets/Scripts/Common/Encrypt/Rijndael.cs"
"Assets/Scripts/Common/FileUtility.cs"
"Assets/Scripts/Common/GameHelper.cs"
"Assets/Scripts/Common/KVTextTool.cs"
"Assets/Scripts/Common/LogMan.cs"
"Assets/Scripts/Common/Lson/Lson.cs"
"Assets/Scripts/Common/Lson/OffsetToLineCol.cs"
"Assets/Scripts/Common/Lson/Util.cs"
"Assets/Scripts/Common/NotchFit.cs"
"Assets/Scripts/Common/PlayerPrefsEx.cs"
"Assets/Scripts/Common/ScreenShot.cs"
"Assets/Scripts/Common/ScriptExtend.cs"
"Assets/Scripts/Common/SkeletonAutoPlay.cs"
"Assets/Scripts/Common/ThreadManager.cs"
"Assets/Scripts/Common/ThreadWorker.cs"
"Assets/Scripts/Common/Utils/GizmosUtility.cs"
"Assets/Scripts/Custom/LightningBoltScript.cs"
"Assets/Scripts/Custom/TopTrigger.cs"
"Assets/Scripts/Custom/UITrigger.cs"
"Assets/Scripts/EmojiPuzzleInput/EmojiPuzzleInput.cs"
"Assets/Scripts/EmojiPuzzleInput/EmojiPuzzleLine.cs"
"Assets/Scripts/Framework/Http/GameHttp.cs"
"Assets/Scripts/Framework/Http/HTTPPacket.cs"
"Assets/Scripts/Framework/Http/HTTPParamField.cs"
"Assets/Scripts/Framework/Http/HTTPQueue.cs"
"Assets/Scripts/Framework/Http/HTTPRequest.cs"
"Assets/Scripts/Framework/Http/HTTPResponse.cs"
"Assets/Scripts/Framework/ResourceManager/AssetManager.cs"
"Assets/Scripts/Framework/ResourceManager/EventManager.cs"
"Assets/Scripts/Framework/ResourceManager/SpriteAtlasMgr.cs"
"Assets/Scripts/Framework/ResourceManager/StorageManager.cs"
"Assets/Scripts/Framework/Singleton/MonoSingleton.cs"
"Assets/Scripts/Framework/Singleton/Singleton.cs"
"Assets/Scripts/Framework/Tcp/NetworkEvent/Message/MessageStream.cs"
"Assets/Scripts/Framework/Tcp/NetworkEvent/Message/MessageStreamException.cs"
"Assets/Scripts/Framework/Tcp/NetworkEvent/NetworkActiveEvent.cs"
"Assets/Scripts/Framework/Tcp/NetworkEvent/NetworkBaseEvent.cs"
"Assets/Scripts/Framework/Tcp/NetworkEvent/NetworkEvent_ConnectFail.cs"
"Assets/Scripts/Framework/Tcp/NetworkEvent/NetworkEvent_ConnectionError.cs"
"Assets/Scripts/Framework/Tcp/NetworkEvent/NetworkEvent_ConnectionLost.cs"
"Assets/Scripts/Framework/Tcp/NetworkEvent/NetworkEvent_ConnectOK.cs"
"Assets/Scripts/Framework/Tcp/NetworkEvent/NetworkEvent_Disconnect.cs"
"Assets/Scripts/Framework/Tcp/NetworkEvent/NetworkEvent_ReceivedMessage.cs"
"Assets/Scripts/Framework/Tcp/NetworkEvent/NetworkEvent_SendMessage.cs"
"Assets/Scripts/Framework/Tcp/NetworkEvent/NetworkEvent_SocketClosed.cs"
"Assets/Scripts/Framework/Tcp/NetworkEvent/NetworkEvent_StartConnect.cs"
"Assets/Scripts/Framework/Tcp/NetworkEvent/NetworkEvent_StartConnect2.cs"
"Assets/Scripts/Framework/Tcp/NetworkEvent/NetworkPassiveEvent.cs"
"Assets/Scripts/Framework/Tcp/NetworkEventMgr.cs"
"Assets/Scripts/Framework/Tcp/TCPConnect.cs"
"Assets/Scripts/Framework/Tcp/TCPConnectMgr.cs"
"Assets/Scripts/Framework/Tcp/TCPConnectSLG.cs"
"Assets/Scripts/Framework/WebSocket/NetworkWebSocketEventMgr.cs"
"Assets/Scripts/Framework/WebSocket/WebSocketSession.cs"
"Assets/Scripts/Game/GameAniManager.cs"
"Assets/Scripts/Game/GameLanuchResource.cs"
"Assets/Scripts/Game/GameLanuchState.cs"
"Assets/Scripts/Game/GameSilentResource.cs"
"Assets/Scripts/Game/GameVersion.cs"
"Assets/Scripts/Game/LanguageManager.cs"
"Assets/Scripts/Game/MapGridUtils.cs"
"Assets/Scripts/Game/StartGame.cs"
"Assets/Scripts/Game/UILogin.cs"
"Assets/Scripts/Game/UINotice.cs"
"Assets/Scripts/Game/UIVersionUpdateSilent.cs"
"Assets/Scripts/GameLanuch.cs"
"Assets/Scripts/MonoWidgets/AnimationCurves.cs"
"Assets/Scripts/MonoWidgets/AnimEvent.cs"
"Assets/Scripts/MonoWidgets/ButtonPressed.cs"
"Assets/Scripts/MonoWidgets/ControlExpand.cs"
"Assets/Scripts/MonoWidgets/HollowOutMask.cs"
"Assets/Scripts/MonoWidgets/HttpMono.cs"
"Assets/Scripts/MonoWidgets/MapTiled.cs"
"Assets/Scripts/MonoWidgets/MaterialSelect.cs"
"Assets/Scripts/MonoWidgets/MonoLinkLuaData.cs"
"Assets/Scripts/MonoWidgets/MyScaler.cs"
"Assets/Scripts/MonoWidgets/RunInBack.cs"
"Assets/Scripts/MonoWidgets/SeaRenderer.cs"
"Assets/Scripts/MonoWidgets/SliderRect.cs"
"Assets/Scripts/MonoWidgets/SortingLayerMono.cs"
"Assets/Scripts/MonoWidgets/TouchMono.cs"
"Assets/Scripts/MonoWidgets/TutorialBlock.cs"
"Assets/Scripts/MonoWidgets/UserDataMono.cs"
"Assets/Scripts/Proto/Animal.cs"
"Assets/Scripts/Proto/Battle.cs"
"Assets/Scripts/Proto/Common.cs"
"Assets/Scripts/Proto/Dungeon.cs"
"Assets/Scripts/Proto/Equip.cs"
"Assets/Scripts/Proto/Fight.cs"
"Assets/Scripts/Proto/Gate.cs"
"Assets/Scripts/Proto/Gm.cs"
"Assets/Scripts/Proto/Hero.cs"
"Assets/Scripts/Proto/Item.cs"
"Assets/Scripts/Proto/Protocol.cs"
"Assets/Scripts/Proto/Role.cs"
"Assets/Scripts/Proto/Roledata.cs"
"Assets/Scripts/Proto/Tower.cs"
"Assets/Scripts/SDK/AdsManager.cs"
"Assets/Scripts/SDK/BuglyMgr.cs"
"Assets/Scripts/SDK/GameSdkManager.cs"
"Assets/Scripts/SDK/IAPSystem.cs"
"Assets/Scripts/SDK/LuaSdkHelper.cs"
"Assets/Scripts/SDK/Module/SDKAppleModule.cs"
"Assets/Scripts/SDK/Module/SDKClipboardModule.cs"
"Assets/Scripts/SDK/Module/SDKFBModule.cs"
"Assets/Scripts/SDK/Module/SDKFireModule.cs"
"Assets/Scripts/SDK/Module/SDKLangModule.cs"
"Assets/Scripts/SDK/Module/SDKLoginModule.cs"
"Assets/Scripts/SDK/Module/SDKNotifyModule.cs"
"Assets/Scripts/SDK/Utils/AdvertisingType.cs"
"Assets/Scripts/SDK/Utils/SDKMonoSingleton.cs"
"Assets/Scripts/SDK/Utils/SDKNetHttp.cs"
"Assets/Scripts/SDK/Utils/SDKSingleton.cs"
"Assets/Scripts/SDK/Utils/SDKUtility.cs"
"Assets/Scripts/SDK/View/AcceptWidget.cs"
"Assets/Scripts/SDK/View/AgreeWidget.cs"
"Assets/Scripts/SDK/View/BindingWidget.cs"
"Assets/Scripts/SDK/View/ChangeLoginWidget.cs"
"Assets/Scripts/SDK/View/LoginView.cs"
"Assets/Scripts/SDK/View/RequestWidget.cs"
"Assets/Scripts/UI/UIBGScaler.cs"
"Assets/Scripts/UI/UIBloom.cs"
"Assets/Scripts/UI/UIButtonScale.cs"
"Assets/Scripts/UI/UICapture.cs"
"Assets/Scripts/UI/UIDrag.cs"
"Assets/Scripts/UI/UIDragXYDir.cs"
"Assets/Scripts/UI/UIGradient.cs"
"Assets/Scripts/UI/UIMask.cs"
"Assets/Scripts/UI/UIRoot.cs"
"Assets/Scripts/UI/UIToggle.cs"
"Assets/Scripts/UIPlugins/CircleText.cs"
"Assets/Scripts/UIPlugins/ColorText.cs"
"Assets/Scripts/UIPlugins/DeletegateCall.cs"
"Assets/Scripts/UIPlugins/FancyScrollView/Cell.cs"
"Assets/Scripts/UIPlugins/FancyScrollView/UIScrollView.cs"
"Assets/Scripts/UIPlugins/HyperlinkText.cs"
"Assets/Scripts/UIPlugins/LinkImageText.cs"
"Assets/Scripts/UIPlugins/PageView.cs"
"Assets/Scripts/UIPlugins/TableView/CCTableView.cs"
"Assets/Scripts/UIPlugins/TableView/CCTableViewCell.cs"
"Assets/Scripts/UIPlugins/TableView/CCTableViewController.cs"
"Assets/Scripts/UIPlugins/TableView/ITableViewDataSource.cs"
"Assets/Scripts/UIPlugins/TableViewPlug/Common/HtmlColor.cs"
"Assets/Scripts/UIPlugins/TableViewPlug/Common/HtmlColorExtensions.cs"
"Assets/Scripts/UIPlugins/TableViewPlug/Common/RichText.cs"
"Assets/Scripts/UIPlugins/TableViewPlug/Common/ScrollbarHandleSize.cs"
"Assets/Scripts/UIPlugins/TableViewPlug/Extensions/GameObjectEx.cs"
"Assets/Scripts/UIPlugins/TableViewPlug/Extensions/RectTransformEx.cs"
"Assets/Scripts/UIPlugins/TableViewPlug/tableView/TableView.cs"
"Assets/Scripts/UIPlugins/TableViewPlug/tableView/TableViewCell.cs"
"Assets/Scripts/UIPlugins/TableViewPlug/tableView/TableViewH.cs"
"Assets/Scripts/UIPlugins/TableViewPlug/tableView/TableViewV.cs"
"Assets/Scripts/WX/NativeInputField/InputField/TmpTextInit.cs"
"Assets/Scripts/WX/NativeInputField/InputField/WXInputFieldAdapter.cs"
"Assets/Scripts/WX/NativeInputField/InputField/WXInputFieldTmpAdapter.cs"
"Assets/Scripts/XLua/BuildInInit.cs"
"Assets/Scripts/XLua/CoroutineRunner.cs"
"Assets/Scripts/XLua/LuaManager.cs"
"Assets/Scripts/XLua/LuaScript.cs"
"Assets/Scripts/XLua/UnityEngineObjectExtention.cs"
"Assets/ThirdParty/LuaPerfect/ObjectFormater.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Data/LevelData.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Data/UIData.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/DLYShuiGuanEnter.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Flower/FlowerAnimation.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Level/DLYShuiGuanLevel.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Level/Level.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Manager/ConfigManager/ConfigManager.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Manager/EventManager/EventManager.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Manager/InstanceBase.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Manager/UIManager/UIManager.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Other/AudioManager.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Other/checker.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Other/cuderotation.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Other/headofrays.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Other/levelloder.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Other/levelnumber.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Other/offsettest.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Other/rotater.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Other/Sound.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Other/tunnel.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Other/ui.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Other/waterstart.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Static/FunctionLibrary.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Static/Singleton.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Static/Static.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/UI/UIMain.cs"
"Assets/TinyGame/CasualGame/DLYShuiGuan/Script/Water/ShuiDao.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/FlexyRingEnter.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/FlexyRingGameData.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/FlexyRingLevelEnter.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/GameScript/AddRingC.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/GameScript/DelayManager.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/GameScript/EventCenter.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/GameScript/GramScripts/AddPedestal.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/GameScript/GramScripts/DragBox.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/GameScript/GramScripts/EnumState.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/GameScript/GramScripts/GameData.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/GameScript/GramScripts/MainPanel.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/GameScript/GramScripts/Pedestal.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/GameScript/GramScripts/RewardEntityManager.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/GameScript/GramScripts/Ring.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/GameScript/GramScripts/RingBehaviourLogic.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/GameScript/LevelController.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/GameScript/PlayAudios.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/GameScript/Tools/BaseManager.cs"
"Assets/TinyGame/CasualGame/FlexyRing/Script/GameScript/Tools/EffectManager.cs"
"Assets/TinyGame/Common/CasualGameTag.cs"
"Assets/TinyGame/Common/CSVReader.cs"
"Assets/TinyGame/Common/IFTinyNet.cs"
"Assets/TinyGame/Common/LoadMgr.cs"
"Assets/TinyGame/Common/MiniVibration.cs"
"Assets/TinyGame/Common/MusicController.cs"
"Assets/TinyGame/Common/TinyLang.cs"
"Assets/TinyGame/Common/TinyLocalizationText.cs"
"Assets/TinyGame/GameManage/Script/IFGame.cs"
"Assets/TinyGame/GameManage/Script/LevelManager.cs"
"Assets/TinyGame/GameObjectComs.cs"
"Assets/TinyGame/ILuaGameObject.cs"
"Assets/TinyGame/LuaContainer.cs"
"Assets/TinyGame/Scripts/CommonTools/CsGetLuaUtilst.cs"
"Assets/TinyGame/Scripts/CommonTools/LogHelp.cs"
"Assets/TinyGame/Scripts/CommonTools/ObjEx.cs"
"Assets/TinyGame/Scripts/CommonTools/ScrollRectItem.cs"
"Assets/TinyGame/Scripts/EventMessage/CollisionTriggerListener.cs"
"Assets/TinyGame/Scripts/ScriptDLL/ActionInfoList.cs"
"Assets/TinyGame/Scripts/ScriptDLL/bc.MiniGameBase/FuncCreateLua2Cs.cs"
"Assets/TinyGame/Scripts/ScriptDLL/bc.MiniGameBase/MonoSingleton.cs"
"Assets/TinyGame/Scripts/ScriptDLL/bc.MiniGameBase/MonoSingletonCreator.cs"
"Assets/TinyGame/Scripts/ScriptDLL/bc.MiniGameBase/MonoSingletonPathAttribute.cs"
"Assets/TinyGame/Scripts/ScriptDLL/bc.MiniGameBase/XLuaHelp.cs"
"Assets/TinyGame/Scripts/ScriptDLL/bc.MiniGameBase/XLuaManager.cs"
"Assets/TinyGame/Scripts/ScriptDLL/CoroutineConfig.cs"
"Assets/TinyGame/Scripts/ScriptDLL/Coroutine_Runner.cs"
"Assets/TinyGame/Scripts/ScriptDLL/CrowdRunnersXLuaGenConfig.cs"
"Assets/TinyGame/Scripts/ScriptDLL/GameLuaBehaviour.cs"
"Assets/TinyGame/Scripts/ScriptDLL/GameLuaBehaviour_3.cs"
"Assets/TinyGame/Scripts/ScriptDLL/GameLuaBehaviour_New.cs"
"Assets/TinyGame/Scripts/ScriptDLL/GameLuaObjectData.cs"
"Assets/TinyGame/Scripts/ScriptDLL/GameLuaObjectDataComparer.cs"
"Assets/TinyGame/Scripts/ScriptDLL/GameLuaObjectDataComparer_3.cs"
"Assets/TinyGame/Scripts/ScriptDLL/GameLuaObjectData_3.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionAnimationCurve.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionAnimationCurveArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionAnimator.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionAnimatorArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionAudioClip.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionAudioClipArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionButton.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionButtonArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionDOTweenAnimation.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionDOTweenAnimationArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/Injectiondouble.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectiondoubleArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/Injectiondouble_3.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionDropdown.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionDropdownArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionGameLuaBehaviour.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionGameLuaBehaviourArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionGameObject.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionGameObjectArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/Injectionimage.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionimageArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionInputField.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionInputFieldArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/Injectionlong.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionlongArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/Injectionlong_3.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionMaterial.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionMaterialArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionRawImage.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionRawImageArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionSlider.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionSliderArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/Injectionsprite.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionspriteArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/Injectionstring.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionstringArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/Injectionstring_3.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionText.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionTextArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionTextAsset.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionTextAssetArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionTexture.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionTexture2D.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionTexture2DArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionTextureArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionToggle.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionToggleArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionTransform.cs"
"Assets/TinyGame/Scripts/ScriptDLL/InjectionTransformArray.cs"
"Assets/TinyGame/Scripts/ScriptDLL/Injection_object.cs"
"Assets/TinyGame/Scripts/ScriptDLL/OrganBaseComponent.cs"
"Assets/TinyGame/Scripts/ScriptDLL/OrganBaseXLuaGenConfig.cs"
"Assets/TinyGame/Scripts/ScriptDLL/OrganComponentData.cs"
"Assets/TinyGame/Scripts/ScriptDLL/PhysicsTriggerStay.cs"
"Assets/TinyGame/Scripts/ScriptDLL/Properties/AssemblyInfo.cs"
"Assets/UnityWebSocket/Demo/UnityWebSocketDemo.cs"
"Assets/XLua/Gen/ActionInfoListWrap.cs"
"Assets/XLua/Gen/AnimationCurvesWrap.cs"
"Assets/XLua/Gen/AnimEventWrap.cs"
"Assets/XLua/Gen/AssetManagerWrap.cs"
"Assets/XLua/Gen/bc_IFGameBridge.cs"
"Assets/XLua/Gen/bc_IFGameWrap.cs"
"Assets/XLua/Gen/bc_MiniGameBase_CollisionTriggerListenerWrap.cs"
"Assets/XLua/Gen/bc_MiniGameBase_GameObjectComsWrap.cs"
"Assets/XLua/Gen/bc_MiniGameBase_ILuaGameObjectBridge.cs"
"Assets/XLua/Gen/bc_MiniGameBase_ILuaGameObjectWrap.cs"
"Assets/XLua/Gen/bc_MiniGameBase_LuaContainerWrap.cs"
"Assets/XLua/Gen/bc_MiniGameBase_MiniVibrationWrap.cs"
"Assets/XLua/Gen/ButtonPressedWrap.cs"
"Assets/XLua/Gen/CCTableViewCellWrap.cs"
"Assets/XLua/Gen/CCTableViewControllerWrap.cs"
"Assets/XLua/Gen/CCTableViewWrap.cs"
"Assets/XLua/Gen/Coffee_UIEffects_UIShadowWrap.cs"
"Assets/XLua/Gen/ControlExpandWrap.cs"
"Assets/XLua/Gen/CoroutineRunnerWrap.cs"
"Assets/XLua/Gen/Coroutine_RunnerWrap.cs"
"Assets/XLua/Gen/DelegatesGensBridge.cs"
"Assets/XLua/Gen/DG_Tweening_Core_ABSSequentiableWrap.cs"
"Assets/XLua/Gen/DG_Tweening_Core_TweenerCore_3_UnityEngine_Vector3_DG_Tweening_Plugins_Core_PathCore_Path_DG_Tweening_Plugins_Options_PathOptions_Wrap.cs"
"Assets/XLua/Gen/DG_Tweening_Core_TweenerCore_3_UnityEngine_Vector3_UnityEngine_Vector3_DG_Tweening_Plugins_Options_VectorOptions_Wrap.cs"
"Assets/XLua/Gen/DG_Tweening_DOTweenAnimationWrap.cs"
"Assets/XLua/Gen/DG_Tweening_DOTweenModuleUIWrap.cs"
"Assets/XLua/Gen/DG_Tweening_DOTweenPathWrap.cs"
"Assets/XLua/Gen/DG_Tweening_DOTweenWrap.cs"
"Assets/XLua/Gen/DG_Tweening_DOVirtualWrap.cs"
"Assets/XLua/Gen/DG_Tweening_EaseFactoryWrap.cs"
"Assets/XLua/Gen/DG_Tweening_SequenceWrap.cs"
"Assets/XLua/Gen/DG_Tweening_ShortcutExtensions43Wrap.cs"
"Assets/XLua/Gen/DG_Tweening_ShortcutExtensions46Wrap.cs"
"Assets/XLua/Gen/DG_Tweening_ShortcutExtensions50Wrap.cs"
"Assets/XLua/Gen/DG_Tweening_ShortcutExtensionsWrap.cs"
"Assets/XLua/Gen/DG_Tweening_TweenerWrap.cs"
"Assets/XLua/Gen/DG_Tweening_TweenExtensionsWrap.cs"
"Assets/XLua/Gen/DG_Tweening_TweenParamsWrap.cs"
"Assets/XLua/Gen/DG_Tweening_TweenSettingsExtensionsWrap.cs"
"Assets/XLua/Gen/DG_Tweening_TweenWrap.cs"
"Assets/XLua/Gen/EnumWrap.cs"
"Assets/XLua/Gen/GameAniManagerWrap.cs"
"Assets/XLua/Gen/GameHelperWrap.cs"
"Assets/XLua/Gen/GameLuaBehaviour_NewWrap.cs"
"Assets/XLua/Gen/GameNetWork_Net_GameHttpWrap.cs"
"Assets/XLua/Gen/GameSdkManagerWrap.cs"
"Assets/XLua/Gen/HttpMonoWrap.cs"
"Assets/XLua/Gen/HyperlinkTextWrap.cs"
"Assets/XLua/Gen/LanguageManagerWrap.cs"
"Assets/XLua/Gen/LevelManagerWrap.cs"
"Assets/XLua/Gen/LinkImageTextWrap.cs"
"Assets/XLua/Gen/LogManWrap.cs"
"Assets/XLua/Gen/LuaDebugToolWrap.cs"
"Assets/XLua/Gen/LuaManagerWrap.cs"
"Assets/XLua/Gen/LuaPerfect_ObjectFormaterWrap.cs"
"Assets/XLua/Gen/LuaPerfect_ObjectItemWrap.cs"
"Assets/XLua/Gen/LuaPerfect_ObjectRefWrap.cs"
"Assets/XLua/Gen/LuaSdkHelperWrap.cs"
"Assets/XLua/Gen/MapTiledWrap.cs"
"Assets/XLua/Gen/MaterialSelectWrap.cs"
"Assets/XLua/Gen/MonoLinkLuaDataWrap.cs"
"Assets/XLua/Gen/Mosframe_TableViewCellWrap.cs"
"Assets/XLua/Gen/Mosframe_TableViewHWrap.cs"
"Assets/XLua/Gen/Mosframe_TableViewVWrap.cs"
"Assets/XLua/Gen/Mosframe_TableViewWrap.cs"
"Assets/XLua/Gen/NetworkEventMgrWrap.cs"
"Assets/XLua/Gen/OrganBaseComponentWrap.cs"
"Assets/XLua/Gen/OrganComponentDataWrap.cs"
"Assets/XLua/Gen/PackUnpack.cs"
"Assets/XLua/Gen/PageViewWrap.cs"
"Assets/XLua/Gen/PlayerPrefsExWrap.cs"
"Assets/XLua/Gen/PlayerPrefsWrap.cs"
"Assets/XLua/Gen/RijndaelWrap.cs"
"Assets/XLua/Gen/ScriptExtendWrap.cs"
"Assets/XLua/Gen/SDKLoginModuleWrap.cs"
"Assets/XLua/Gen/SortingLayerMonoWrap.cs"
"Assets/XLua/Gen/Spine_AnimationStateWrap.cs"
"Assets/XLua/Gen/Spine_Unity_SkeletonAnimationWrap.cs"
"Assets/XLua/Gen/Spine_Unity_SkeletonDataAssetWrap.cs"
"Assets/XLua/Gen/Spine_Unity_SkeletonGraphicWrap.cs"
"Assets/XLua/Gen/Spine_Unity_SkeletonRendererWrap.cs"
"Assets/XLua/Gen/StartGameWrap.cs"
"Assets/XLua/Gen/StorageManagerWrap.cs"
"Assets/XLua/Gen/System_Collections_Generic_Dictionary_2_System_String_System_String_Wrap.cs"
"Assets/XLua/Gen/System_Collections_Generic_Dictionary_2_System_String_UnityEngine_GameObject_Wrap.cs"
"Assets/XLua/Gen/System_Collections_Generic_List_1_System_Int32_Wrap.cs"
"Assets/XLua/Gen/System_Collections_IEnumeratorBridge.cs"
"Assets/XLua/Gen/System_GCWrap.cs"
"Assets/XLua/Gen/System_ObjectWrap.cs"
"Assets/XLua/Gen/TMPro_TextMeshProUGUIWrap.cs"
"Assets/XLua/Gen/TMPro_TextMeshProWrap.cs"
"Assets/XLua/Gen/TMPro_TMP_InputFieldWrap.cs"
"Assets/XLua/Gen/TopTriggerWrap.cs"
"Assets/XLua/Gen/TouchMonoWrap.cs"
"Assets/XLua/Gen/TutorialBlockWrap.cs"
"Assets/XLua/Gen/UICaptureWrap.cs"
"Assets/XLua/Gen/UIDragWrap.cs"
"Assets/XLua/Gen/UIDragXYDirWrap.cs"
"Assets/XLua/Gen/UIMaskWrap.cs"
"Assets/XLua/Gen/UIRootWrap.cs"
"Assets/XLua/Gen/UIScrollViewWrap.cs"
"Assets/XLua/Gen/UI_UGUIExtendMini_ScrollRectItemWrap.cs"
"Assets/XLua/Gen/UnityEngineObjectExtentionWrap.cs"
"Assets/XLua/Gen/UnityEngine_AnimationClipWrap.cs"
"Assets/XLua/Gen/UnityEngine_AnimationCurveWrap.cs"
"Assets/XLua/Gen/UnityEngine_AnimationStateWrap.cs"
"Assets/XLua/Gen/UnityEngine_AnimationWrap.cs"
"Assets/XLua/Gen/UnityEngine_AnimatorStateInfoWrap.cs"
"Assets/XLua/Gen/UnityEngine_AnimatorWrap.cs"
"Assets/XLua/Gen/UnityEngine_ApplicationWrap.cs"
"Assets/XLua/Gen/UnityEngine_AsyncOperationWrap.cs"
"Assets/XLua/Gen/UnityEngine_AudioSourceWrap.cs"
"Assets/XLua/Gen/UnityEngine_BehaviourWrap.cs"
"Assets/XLua/Gen/UnityEngine_BoundsWrap.cs"
"Assets/XLua/Gen/UnityEngine_BoxCollider2DWrap.cs"
"Assets/XLua/Gen/UnityEngine_BoxColliderWrap.cs"
"Assets/XLua/Gen/UnityEngine_CanvasGroupWrap.cs"
"Assets/XLua/Gen/UnityEngine_CanvasWrap.cs"
"Assets/XLua/Gen/UnityEngine_CapsuleCollider2DWrap.cs"
"Assets/XLua/Gen/UnityEngine_CapsuleColliderWrap.cs"
"Assets/XLua/Gen/UnityEngine_CircleCollider2DWrap.cs"
"Assets/XLua/Gen/UnityEngine_Collider2DWrap.cs"
"Assets/XLua/Gen/UnityEngine_ColliderWrap.cs"
"Assets/XLua/Gen/UnityEngine_Collision2DWrap.cs"
"Assets/XLua/Gen/UnityEngine_CollisionWrap.cs"
"Assets/XLua/Gen/UnityEngine_Color32Wrap.cs"
"Assets/XLua/Gen/UnityEngine_ColorWrap.cs"
"Assets/XLua/Gen/UnityEngine_ComponentWrap.cs"
"Assets/XLua/Gen/UnityEngine_DebugWrap.cs"
"Assets/XLua/Gen/UnityEngine_EventSystems_EventSystemWrap.cs"
"Assets/XLua/Gen/UnityEngine_EventSystems_EventTriggerWrap.cs"
"Assets/XLua/Gen/UnityEngine_EventSystems_EventTrigger_EntryWrap.cs"
"Assets/XLua/Gen/UnityEngine_Events_UnityEventWrap.cs"
"Assets/XLua/Gen/UnityEngine_GameObjectWrap.cs"
"Assets/XLua/Gen/UnityEngine_InputWrap.cs"
"Assets/XLua/Gen/UnityEngine_KeyframeWrap.cs"
"Assets/XLua/Gen/UnityEngine_LayerMaskWrap.cs"
"Assets/XLua/Gen/UnityEngine_LineRendererWrap.cs"
"Assets/XLua/Gen/UnityEngine_MaterialWrap.cs"
"Assets/XLua/Gen/UnityEngine_MathfWrap.cs"
"Assets/XLua/Gen/UnityEngine_MeshColliderWrap.cs"
"Assets/XLua/Gen/UnityEngine_MeshFilterWrap.cs"
"Assets/XLua/Gen/UnityEngine_MeshRendererWrap.cs"
"Assets/XLua/Gen/UnityEngine_MonoBehaviourWrap.cs"
"Assets/XLua/Gen/UnityEngine_ObjectWrap.cs"
"Assets/XLua/Gen/UnityEngine_ParticleSystemWrap.cs"
"Assets/XLua/Gen/UnityEngine_Physics2DWrap.cs"
"Assets/XLua/Gen/UnityEngine_PhysicsWrap.cs"
"Assets/XLua/Gen/UnityEngine_PlaneWrap.cs"
"Assets/XLua/Gen/UnityEngine_PolygonCollider2DWrap.cs"
"Assets/XLua/Gen/UnityEngine_QualitySettingsWrap.cs"
"Assets/XLua/Gen/UnityEngine_QuaternionWrap.cs"
"Assets/XLua/Gen/UnityEngine_RandomWrap.cs"
"Assets/XLua/Gen/UnityEngine_Ray2DWrap.cs"
"Assets/XLua/Gen/UnityEngine_RaycastHit2DWrap.cs"
"Assets/XLua/Gen/UnityEngine_RaycastHitWrap.cs"
"Assets/XLua/Gen/UnityEngine_RayWrap.cs"
"Assets/XLua/Gen/UnityEngine_RectOffsetWrap.cs"
"Assets/XLua/Gen/UnityEngine_RectTransformUtilityWrap.cs"
"Assets/XLua/Gen/UnityEngine_RectTransformWrap.cs"
"Assets/XLua/Gen/UnityEngine_RectWrap.cs"
"Assets/XLua/Gen/UnityEngine_RelativeJoint2DWrap.cs"
"Assets/XLua/Gen/UnityEngine_RendererWrap.cs"
"Assets/XLua/Gen/UnityEngine_RenderSettingsWrap.cs"
"Assets/XLua/Gen/UnityEngine_RenderTextureWrap.cs"
"Assets/XLua/Gen/UnityEngine_ResourceRequestWrap.cs"
"Assets/XLua/Gen/UnityEngine_ResourcesWrap.cs"
"Assets/XLua/Gen/UnityEngine_Rigidbody2DWrap.cs"
"Assets/XLua/Gen/UnityEngine_RigidbodyWrap.cs"
"Assets/XLua/Gen/UnityEngine_SceneManagement_SceneManagerWrap.cs"
"Assets/XLua/Gen/UnityEngine_ScreenWrap.cs"
"Assets/XLua/Gen/UnityEngine_SkinnedMeshRendererWrap.cs"
"Assets/XLua/Gen/UnityEngine_SphereColliderWrap.cs"
"Assets/XLua/Gen/UnityEngine_SpriteRendererWrap.cs"
"Assets/XLua/Gen/UnityEngine_SpriteWrap.cs"
"Assets/XLua/Gen/UnityEngine_SystemInfoWrap.cs"
"Assets/XLua/Gen/UnityEngine_TextAssetWrap.cs"
"Assets/XLua/Gen/UnityEngine_TimeWrap.cs"
"Assets/XLua/Gen/UnityEngine_TouchWrap.cs"
"Assets/XLua/Gen/UnityEngine_TrailRendererWrap.cs"
"Assets/XLua/Gen/UnityEngine_TransformWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_ButtonWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_Button_ButtonClickedEventWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_CanvasScalerWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_ContentSizeFitterWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_DropdownWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_Dropdown_DropdownEventWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_Dropdown_OptionDataListWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_Dropdown_OptionDataWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_GraphicRaycasterWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_GridLayoutGroupWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_HorizontalLayoutGroupWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_ImageWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_InputFieldWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_LayoutElementWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_LayoutRebuilderWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_RawImageWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_ScrollbarWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_ScrollRectWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_ScrollRect_ScrollRectEventWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_SliderWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_TextWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_ToggleGroupWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_ToggleWrap.cs"
"Assets/XLua/Gen/UnityEngine_UI_VerticalLayoutGroupWrap.cs"
"Assets/XLua/Gen/UnityEngine_Vector2Wrap.cs"
"Assets/XLua/Gen/UnityEngine_Vector3IntWrap.cs"
"Assets/XLua/Gen/UnityEngine_Vector3Wrap.cs"
"Assets/XLua/Gen/UnityEngine_Vector4Wrap.cs"
"Assets/XLua/Gen/UnityEngine_WaitForEndOfFrameWrap.cs"
"Assets/XLua/Gen/UnityEngine_WaitForFixedUpdateWrap.cs"
"Assets/XLua/Gen/UnityEngine_WaitForSecondsRealtimeWrap.cs"
"Assets/XLua/Gen/UnityEngine_WaitForSecondsWrap.cs"
"Assets/XLua/Gen/UnityEngine_WaitUntilWrap.cs"
"Assets/XLua/Gen/UnityEngine_WaitWhileWrap.cs"
"Assets/XLua/Gen/UserDataMonoWrap.cs"
"Assets/XLua/Gen/WrapPusher.cs"
"Assets/XLua/Gen/XLuaGenAutoRegister.cs"
"Assets/XLua/Src/CodeEmit.cs"
"Assets/XLua/Src/CopyByValue.cs"
"Assets/XLua/Src/DelegateBridge.cs"
"Assets/XLua/Src/GenAttributes.cs"
"Assets/XLua/Src/GenericDelegateBridge.cs"
"Assets/XLua/Src/InternalGlobals.cs"
"Assets/XLua/Src/LuaBase.cs"
"Assets/XLua/Src/LuaDebugTool.cs"
"Assets/XLua/Src/LuaDLL.cs"
"Assets/XLua/Src/LuaEnv.cs"
"Assets/XLua/Src/LuaException.cs"
"Assets/XLua/Src/LuaFunction.cs"
"Assets/XLua/Src/LuaTable.cs"
"Assets/XLua/Src/MethodWarpsCache.cs"
"Assets/XLua/Src/ObjectCasters.cs"
"Assets/XLua/Src/ObjectPool.cs"
"Assets/XLua/Src/ObjectTranslator.cs"
"Assets/XLua/Src/ObjectTranslatorPool.cs"
"Assets/XLua/Src/RawObject.cs"
"Assets/XLua/Src/SignatureLoader.cs"
"Assets/XLua/Src/StaticLuaCallbacks.cs"
"Assets/XLua/Src/TemplateEngine/TemplateEngine.cs"
"Assets/XLua/Src/TypeExtensions.cs"
"Assets/XLua/Src/Utils.cs"
-langversion:9.0
/unsafe+
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"