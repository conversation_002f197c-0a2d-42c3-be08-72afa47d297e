local UI_EquipmentDevelopView = Class(BaseView)
local SlideRect = require("UI.Common.SlideRect")
local ItemBase = require("UI.Common.BaseSlideItem")
local EquipmentItem = Class(ItemBase)
local SelectMaterialItem = Class(ItemBase)
local ReturnMaterialItem = Class(ItemBase)
local ResolveItem = Class(ItemBase)
local FallbackItem = Class(ItemBase)
local BagBase = require("UI.BagItem")
local BagItem = Class(BagBase)
local ItemShowNums = 8
local ItemColumns = 6
local HideColor = Color.New(1, 1, 1, 0)
local ShowColor = Color.New(1, 1, 1, 1)

local ClickType
local CurSelectEquip           -- 当前选中的装备
local RisingStarMaterial = {}  -- 升星选中的材料装备
local ResolveEquipment = {}    -- 分解选中的装备
local RisingStarCurNum = 0     -- 升星材料当前数量
local RisingStarTargetNum = 0  -- 升星材料目标数量
local RisingStarProgress = 0   -- 升星材料进度

local EquipmentStarMax = 5     -- 装备满星
local EquipmentResolveMax = 1000  -- 单次分解数量上限（大概 3000 就无法发送给后端，因为数据太长）

local BagItemType = {
    Equipment = 1,
    RisingStarMaterial = 2,
}

-- 升级按钮组类型
local LevelUpBtnType = {
    Strengthen = 1,
    StrengthenGrey = 2,
    Promote = 3,
    PromoteGrey = 4
}

local ScrollviewEquipment
local ScrollviewResolve
local ScrollviewFallback

local CurTargetID
local SortingOrder

function UI_EquipmentDevelopView:OnInit()

end

function UI_EquipmentDevelopView:OnCreate(param, equipment)
    SortingOrder = self:GetViewSortingOrder()
    ScrollviewEquipment = self.ui.m_scrollviewEquipment
    ScrollviewResolve = GetChild(self.ui.m_goResolve, "bg")
    ScrollviewFallback = GetChild(self.ui.m_goFallback, "mask")
    if equipment and equipment.target_id ~= 0 then
        CurTargetID = equipment.target_id
    end
    self.effectList = {}

    self.canStrengthen = true   -- 是否可强化
    self.canPromote = true      -- 是否可晋升
    self.canRisingStar = true   -- 是否可升星
    self.canResolve = true      -- 是否可分解
    self.canFallback = true     -- 是否可回退

    -- 升级/晋升按钮组
    self.levelUpBtns = {
        self.ui.m_btnStrengthen,
        self.ui.m_btnStrengthenGrey,
        self.ui.m_btnPromote,
        self.ui.m_btnPromoteGrey
    }

    -- 强化的装备
    self.strengthenEquipment = BagItem.new()
    self.strengthenEquipment:Create(self.ui.m_transStrengthenEquipment)
    self.strengthenEquipment:SetScale(0.84, 0.84)
    SetActive(self.strengthenEquipment.go, true)

    -- 晋升前的装备
    self.promoteEquipmentBefore = BagItem.new()
    self.promoteEquipmentBefore:Create(self.ui.m_transEquipmentBefore)
    self.promoteEquipmentBefore:SetScale(0.84, 0.84)
    SetActive(self.promoteEquipmentBefore.go, true)

    -- 晋升后的装备
    self.promoteEquipmentAfter = BagItem.new()
    self.promoteEquipmentAfter:Create(self.ui.m_transEquipmentAfter)
    self.promoteEquipmentAfter:SetScale(0.84, 0.84)
    SetActive(self.promoteEquipmentAfter.go, true)

    -- 升星的装备 左边
    self.risingStarEquipment1 = BagItem.new()
    self.risingStarEquipment1:Create(self.ui.m_transRisingStarEquipment1)
    self.risingStarEquipment1:SetScale(0.84, 0.84)
    SetActive(self.risingStarEquipment1.go, true)

    -- 升星的装备 右边
    self.risingStarEquipment2 = BagItem.new()
    self.risingStarEquipment2:Create(self.ui.m_transRisingStarEquipment2)
    self.risingStarEquipment2:SetScale(0.84, 0.84)
    SetActive(self.risingStarEquipment2.go, true)

    -- 回退的装备
    self.fallbackEquipment = BagItem.new()
    self.fallbackEquipment:Create(self.ui.m_transFallback)
    self.fallbackEquipment:SetScale(0.8, 0.8)
    SetActive(self.fallbackEquipment.go, true)

    -- 部位页签图片
    self.m_imgPartTab1 = GetComponent(self.ui.m_btnPartTab1, UEUI.Image)
    self.m_imgPartTab2 = GetComponent(self.ui.m_btnPartTab2, UEUI.Image)
    self.m_imgPartTab3 = GetComponent(self.ui.m_btnPartTab3, UEUI.Image)
    self.m_imgPartTab4 = GetComponent(self.ui.m_btnPartTab4, UEUI.Image)

    -- 分解的品质多选框
    SetActive(self.ui.m_imgResolveSelectTab1, false)
    SetActive(self.ui.m_imgResolveSelectTab2, false)
    SetActive(self.ui.m_imgResolveSelectTab3, false)
    SetActive(self.ui.m_imgResolveSelectTab4, false)

    -- 装备滚动视图
    self.rectScrollviewEquipment = GetComponent(self.ui.m_scrollviewEquipment, UE.RectTransform)

    self:InitEquipmentList()
    self:RefreshEquipInfo()
    self:OnClickTab(v2n(param) or EQUIPMENT_DEVELOP_VIEW_TYPE.STRENGTHEN)

    if equipment then
        self:OnClickPartTab(equipment.pos)
        self:SelectItemByID(equipment.id)
    else
        self:OnClickPartTab(EQUIPMENT_PART.WEAPON)
        if IsTableNotEmpty(self.equipSlider.datas) then
            self:SelectFirstItem()
        end
    end

    self.ui.m_txtStrengthenLevelMax.text = LangMgr:GetLang(70000203)
    self.ui.m_txtRisingStarMax.text = LangMgr:GetLang(70000204)

    EventMgr:Add(EventID.BAG_CHANGE, self.BagChange, self)
end

--- 初始化循环列表
--- @param type number 页签类型
function UI_EquipmentDevelopView:InitList(type)
    if type == EQUIPMENT_DEVELOP_VIEW_TYPE.STRENGTHEN then
        if not self.initStrengthen then
            self.initStrengthen = true
            -- self:InitEquipmentList()
        end
    elseif type == EQUIPMENT_DEVELOP_VIEW_TYPE.RISINGSTAR then
        if not self.initRisingStar then
            self.initRisingStar = true
            self:InitSelectMaterialList()
            self:InitReturnMaterialList()
        end
    elseif type == EQUIPMENT_DEVELOP_VIEW_TYPE.RESOLVE then
        if not self.initResolve then
            self.initResolve = true
            self:InitResolveList()
        end
    elseif type == EQUIPMENT_DEVELOP_VIEW_TYPE.FALLBACK then
        if not self.initFallback then
            self.initFallback = true
            self:InitFallbackList2()
        end
    end
end

function UI_EquipmentDevelopView:OnRefresh(type)
    if type == 1 then
        self:RefreshEquipInfo()
    elseif type == 2 then
        if self.clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.FALLBACK then
            return
        end
        if self.clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.RESOLVE then
            self:OnClickTab(EQUIPMENT_DEVELOP_VIEW_TYPE.RESOLVE)
            return
        end
        self:RefreshEquipmentListByPart()
    elseif type == 3 then
        self:RefreshResolveList()
    elseif type == 4 then
        self:RefreshEquipmentItems()
    elseif type == 5 then
        self:RefreshReturnMaterialList()
        self:RefreshRisingStarMaterial()
        self:RefreshSelectMaterialList()
    elseif type == 6 then
        for _, value in ipairs(self.effectList) do
            SetActive(value, false)
        end
    end
end

function UI_EquipmentDevelopView:BagChange(data)
    if self.clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.FALLBACK then
        return
    end
    self:RefreshEquipInfo()
end

function UI_EquipmentDevelopView:onDestroy()
    EventMgr:Remove(EventID.BAG_CHANGE, self.BagChange, self)
    Tween.Kill("AutoMoveFunc")
    if self.effectTimer then
        TimeMgr:DestroyTimer(UIDefine.UI_EquipmentDevelopView, self.effectTimer)
    end
    CurSelectEquip = nil
    RisingStarMaterial = {}
    ResolveEquipment = {}
    CurTargetID = nil
    self.initStrengthen = nil
    self.initRisingStar = nil
    self.initResolve = nil
    self.initFallback = nil
end

function UI_EquipmentDevelopView:onUIEventClick(go)
    local name = go.name
    -- 关闭按钮
    if name == "m_btnClose" then
        self:Close()
    -- 帮助按钮
    elseif name == "m_btnTip" then
        UI_SHOW(UIDefine.UI_EquipmentItemTipHelp, 70000211)
    -- 强化页签
    elseif name == "m_btnTabStrengthen" then
        self:OnClickTab(EQUIPMENT_DEVELOP_VIEW_TYPE.STRENGTHEN)
    -- 升星页签
    elseif name == "m_btnTabRisingStar" then
        self:OnClickTab(EQUIPMENT_DEVELOP_VIEW_TYPE.RISINGSTAR)
    -- 分解页签
    elseif name == "m_btnTabResolve" then
        self:OnClickTab(EQUIPMENT_DEVELOP_VIEW_TYPE.RESOLVE)
    -- 回退页签
    elseif name == "m_btnTabFallback" then
        self:OnClickTab(EQUIPMENT_DEVELOP_VIEW_TYPE.FALLBACK)
    -- 强化按钮
    elseif name == "m_btnStrengthen" then
        -- 防止连续点击
        if not self.canStrengthen then
            return
        end
        if IsTableNotEmpty(CurSelectEquip) then
            local equipment = CurSelectEquip.data
            local isEnough, notEnoughList, materials = self:GetStrengthenCost()

            if isEnough then
                self.canStrengthen = false
                EquipmentManager:EquipmentLevelUp(equipment.id, function (data)
                    if equipment.id == data.equip_id then
                        equipment.level = data.level
                    end
                    self.canStrengthen = true
                    AudioMgr:Play(122)
                    self:RefreshEquipStrengthenInfo(true)
                    EquipmentManager:TrackEventEquipment("EquipmentLevelUp", equipment, nil, materials)
                end)
            else
                if IsTableNotEmpty(notEnoughList) then
                    UI_SHOW(UIDefine.UI_SlgGetWay, notEnoughList)
                end
            end
        end
    -- 强化置灰按钮
    elseif name == "m_btnStrengthenGrey" then
        if IsTableNotEmpty(CurSelectEquip) then
            local equipment = CurSelectEquip.data
            local levelLimit = EquipmentManager:GetTableValue(equipment.code, "level_limit")

            -- 装备满级（包含晋升）
            if equipment.level >= levelLimit then
                local promoteLevel = v2n(EquipmentManager:GetTableValueByLevel(equipment.code, equipment.level, "promote_level")) or 0
                local promote = equipment.promotion_lv or 0
                -- 已完成最后一次晋升
                if promote >= promoteLevel then
                    return
                end
            end
        end
        local isEnough, notEnoughList = self:GetStrengthenCost()
        if not isEnough then
            if IsTableNotEmpty(notEnoughList) then
                UI_SHOW(UIDefine.UI_SlgGetWay, notEnoughList)
            end
        end
    -- 晋升按钮
    elseif name == "m_btnPromote" then
        -- 防止连续点击
        if not self.canPromote then
            return
        end
        if IsTableNotEmpty(CurSelectEquip) then
            local equipment = CurSelectEquip.data
            local isEnough, notEnoughList, materials = self:GetPromoteCost()

            if isEnough then
                self.canPromote = false
                local copyEquipment = deepcopy(equipment)
                -- 装备晋升
                EquipmentManager:EquipmentPromote(equipment.id, function (data)
                    if equipment.id == data.equip_id then
                        equipment.promotion_lv = data.last_promotion_lv
                    end
                    self.canPromote = true
                    AudioMgr:Play(122)

                    UI_SHOW(UIDefine.UI_EquipmentPromoteSuccess, copyEquipment,
                    EQUIPMENT_DEVELOP_VIEW_TYPE.STRENGTHEN, function ()
                        self:RefreshEquipStrengthenInfo()
                    end)

                    EquipmentManager:TrackEventEquipment("EquipmentPromote", equipment, nil, materials)
                end)
            else
                if IsTableNotEmpty(notEnoughList) then
                    UI_SHOW(UIDefine.UI_SlgGetWay, notEnoughList)
                end
            end
        end
    -- 晋升置灰按钮
    elseif name == "m_btnPromoteGrey" then
        local isEnough, notEnoughList = self:GetPromoteCost()
        if not isEnough then
            if IsTableNotEmpty(notEnoughList) then
                UI_SHOW(UIDefine.UI_SlgGetWay, notEnoughList)
            end
        end
    -- 升星按钮
    elseif name == "m_btnRisingStar" then
        if not self.canRisingStar then
            return
        end
        self:OnClickRisingStar()
    -- 一键选择升星材料装备
    elseif name == "m_btnRisingStarOneClick" then
        self:RisingStarOneClick()
    -- 分解按钮
    elseif name == "m_btnResolve" then
        if not self.canResolve then
            return
        end
        if IsTableNotEmpty(ResolveEquipment) then
            if UIMgr:GetOnly("ResolveEquipment") then
                self:OnClickResolve()
            else
                UI_SHOW(UIDefine.UI_TipsOnly, "ResolveEquipment", LangMgr:GetLang(70000079), function()
                    self:OnClickResolve()
                end)
            end
        end
    -- 一键选择分解
    elseif name == "m_btnResolveOneClick" then
        self.hasSelectGreen = true
        SetActive(self.ui.m_imgResolveSelectTab1, self.hasSelectGreen)
        self.hasSelectBlue = true
        SetActive(self.ui.m_imgResolveSelectTab2, self.hasSelectBlue)
        self.hasSelectPurple = true
        SetActive(self.ui.m_imgResolveSelectTab3, self.hasSelectPurple)
        self.hasSelectGold = true
        SetActive(self.ui.m_imgResolveSelectTab4, self.hasSelectGold)
        self:SelectEquipmentByQuality(ITEM_QUALITY.GREEN)
        self:SelectEquipmentByQuality(ITEM_QUALITY.BLUE)
        self:SelectEquipmentByQuality(ITEM_QUALITY.PURPLE)
        self:SelectEquipmentByQuality(ITEM_QUALITY.GOLD)
        self:RefreshEquipmentItems()
        self:RefreshEquipInfo()
        self:RefreshResolveList()
    -- 回退按钮
    elseif name == "m_btnFallback" then
        if not self.canFallback then
            return
        end
        self:OnClickFallback()
    -- 装备部位 1 按钮
    elseif name == "m_btnPartTab1" then
        self:OnClickPartTab(EQUIPMENT_PART.WEAPON)
    -- 装备部位 2 按钮
    elseif name == "m_btnPartTab2" then
        self:OnClickPartTab(EQUIPMENT_PART.ARMOUR)
    -- 装备部位 3 按钮
    elseif name == "m_btnPartTab3" then
        self:OnClickPartTab(EQUIPMENT_PART.CHIP)
    -- 装备部位 4 按钮
    elseif name == "m_btnPartTab4" then
        self:OnClickPartTab(EQUIPMENT_PART.RADAR)
    -- 分解选中绿色
    elseif name == "m_btnResolveSelectTab1" then
        self:OnClickSelectTab(ITEM_QUALITY.GREEN)
    -- 分解选中蓝色
    elseif name == "m_btnResolveSelectTab2" then
        self:OnClickSelectTab(ITEM_QUALITY.BLUE)
    -- 分解选中紫色
    elseif name == "m_btnResolveSelectTab3" then
        self:OnClickSelectTab(ITEM_QUALITY.PURPLE)
    -- 分解选中金色
    elseif name == "m_btnResolveSelectTab4" then
        self:OnClickSelectTab(ITEM_QUALITY.GOLD)
    -- 跳转到装备抽奖
    elseif name == "m_btnGotoLottery" then
        if UIMgr:ViewIsShow(UIDefine.UI_LotteryCenter) then
            self:Close()
        else
            UI_SHOW(UIDefine.UI_LotteryCenter, 2)
            self:Close()
        end
    end
end

--- 获取升级消耗
--- @return boolean isEnough 是否足够
--- @return table notEnoughList 不足的材料列表
--- @return table materials 消耗材料列表
function UI_EquipmentDevelopView:GetStrengthenCost()
    local isEnough = false
    local materials = {}
    local notEnoughList = {}
    local enoughCount = 0
    if IsTableNotEmpty(CurSelectEquip) then
        local equipment = CurSelectEquip.data
        local costLevelUp = EquipmentManager:GetTableValueByLevel(equipment.code, equipment.level, "cost")
        if costLevelUp then
            local costList = string.split(costLevelUp, ";")
            for _, value in ipairs(costList) do
                local costTable = string.split(value, "|")
                local id = v2n(costTable[1])
                local num = v2n(costTable[2])
                if id and num then
                    table.insert(materials, { id = id, num = num })
                end
            end
        end

        for _, value in ipairs(materials) do
            local curNum = BagManager:GetBagItemCount(value.id)
            if curNum >= value.num then
                enoughCount = enoughCount + 1
            else
                isEnough = false
                table.insert(notEnoughList, {
                    id = value.id,
                    needNum = value.num
                })
            end
        end

        if enoughCount == #materials then
            isEnough = true
        end
    end
    return isEnough, notEnoughList, materials
end

--- 获取晋升消耗
--- @return boolean isEnough 是否足够
--- @return table notEnoughList 不足的材料列表
--- @return table materials 消耗材料列表
function UI_EquipmentDevelopView:GetPromoteCost()
    local isEnough = false
    local materials = {}
    local notEnoughList = {}
    local enoughCount = 0
    if IsTableNotEmpty(CurSelectEquip) then
        local equipment = CurSelectEquip.data
        local costPromote = EquipmentManager:GetTableValueByLevel(equipment.code, equipment.level, "promote_cost")
        if costPromote then
            local costList = string.split(costPromote, ";")
            for _, value in ipairs(costList) do
                local costTable = string.split(value, "|")
                local id = v2n(costTable[1])
                local num = v2n(costTable[2])
                if id and num then
                    table.insert(materials, { id = id, num = num })
                end
            end
        end

        for _, value in ipairs(materials) do
            local curNum = BagManager:GetBagItemCount(value.id)
            if curNum >= value.num then
                enoughCount = enoughCount + 1
            else
                isEnough = false
                table.insert(notEnoughList, {
                    id = value.id,
                    needNum = value.num
                })
            end
        end

        if enoughCount == #materials then
            isEnough = true
        end
    end
    return isEnough, notEnoughList, materials
end

-- 点击升星
function UI_EquipmentDevelopView:OnClickRisingStar()
    if IsTableNotEmpty(CurSelectEquip) then
        if IsTableNotEmpty(RisingStarMaterial) then
            if RisingStarProgress >= 1 then
                local equipment = CurSelectEquip.data
                local copyEquipment = deepcopy(equipment)
                local consume = RisingStarMaterial
                local materials = deepcopy(self.returnMaterialSlider.datas)

                self.canRisingStar = false
                EquipmentManager:EquipmentStarUp(equipment.id, consume, function (data)
                    if equipment.id == data.equip_id then
                        equipment.star = data.star
                    end
                    AudioMgr:Play(121)
                    self:TrackEventRisingStar(equipment, materials)
                    EquipmentManager:EquipmentDelete(consume)
                    RisingStarMaterial = {}
                    self.canRisingStar = true
                    UI_SHOW(UIDefine.UI_EquipmentLevelUpSuccess, copyEquipment,
                        EQUIPMENT_DEVELOP_VIEW_TYPE.RISINGSTAR, materials, function ()
                        self:RefreshEquipRisingStarInfo()
                        self:RefreshEquipmentListByPart()
                    end)
                end)
            else
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000235))
            end
        else
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000235))
        end
    end
end

--- 升星埋点
--- @param equipment table 装备
--- @param materials table 返还材料列表
function UI_EquipmentDevelopView:TrackEventRisingStar(equipment, materials)
    -- 消耗的装备对象组
    local consumeEquipment = {}
    for _, value in ipairs(RisingStarMaterial) do
        local item = EquipmentManager:FindEquipmentByID(value)
        if item then
            table.insert(consumeEquipment, {
                id = item.code,
                num = 1,
                level = item.level,
                star = item.star
            })
        end
    end
    -- 获得的材料对象组
    local returnMaterial = {}
    for _, value in ipairs(materials) do
        local typeUse = ItemConfig:GetTypeUse(value.id)
        if typeUse == ItemUseType.BagHeroClothes then
            local hasSame = false
            for _, material in ipairs(returnMaterial) do
                if value.id == material.id then
                    material.num = material.num + 1
                    hasSame = true
                end
            end
            if not hasSame then
                table.insert(returnMaterial, {
                    id = value.id,
                    num = 1
                })
            end
        else
            table.insert(returnMaterial, {
                id = value.id,
                num = value.num
            })
        end
    end
    EquipmentManager:TrackEventEquipment("EquipmentStarUp", equipment, returnMaterial, consumeEquipment)
end

--- 点击分解
function UI_EquipmentDevelopView:OnClickResolve()
    self:ShowEquipmentEffect(true)
    self:ShowResolveEffect(true)
    self.canResolve = false
    EquipmentManager:EquipmentResolve(ResolveEquipment, function (data)
        self:TrackEventResolve(data.rewards)
        EquipmentManager:EquipmentDelete(ResolveEquipment)
        UI_UPDATE(UIDefine.UI_BagView, 1)
        local delay = 1
        TimeMgr:CreateTimer("EquipmentResolve", function()
            if self.ui then
                self.canResolve = true
                ResolveEquipment = {}
                self:ShowEquipmentEffect(false)
                self:ShowResolveEffect(false)
                self:RefreshEquipmentListByPart()
                self:CancelSelectEquipmentItems()
                self:OnClickTab(self.clickType)
            end
            if data and IsTableNotEmpty(data.rewards) then
                local result = self:CombineRewards(data.rewards)
                UI_SHOW(UIDefine.UI_EquipmentRecharge, result, function ()
                end)
            end
        end, delay, 1)
    end)
end

--- 分解埋点
--- @param rewards table 分解获得
function UI_EquipmentDevelopView:TrackEventResolve(rewards)
    -- 消耗的装备对象组
    local consumeEquipment = {}
    for _, value in ipairs(ResolveEquipment) do
        local item = EquipmentManager:FindEquipmentByID(value)
        if item then
            table.insert(consumeEquipment, {
                id = item.code,
                num = 1,
                level = item.level,
                star = item.star
            })
        end
    end
    local result = {}
    for _, rewardValue in ipairs(rewards) do
        local isAdd = true
        for _, resultValue in ipairs(result) do
            if rewardValue.code == resultValue.id then
                resultValue.num = resultValue.num + rewardValue.amount
                isAdd = false
                break
            end
        end
        if isAdd then
            local data = {
                id = rewardValue.code,
                num = rewardValue.amount
            }
            table.insert(result, data)
        end
    end
    EquipmentManager:TrackEventEquipment("EquipmentResolve", nil, result, consumeEquipment)
end

--- 点击回退
function UI_EquipmentDevelopView:OnClickFallback()
    SetActive(self.fallbackEquipment.effectYellow, true)
    self:ShowFallbackEffect(true)
    self.canFallback = false
    local equipment = CurSelectEquip.data
    local copyEquipment = deepcopy(equipment)
    EquipmentManager:EquipmentReset(equipment.id, function (data)
        if data and IsTableNotEmpty(data.rewards) then
            self:TrackEventFallback(copyEquipment, data.rewards, data)
        end
        if equipment.id == data.equip_id then
            equipment.level = data.equip.level
            equipment.star = data.equip.star
        end
        local delay = 1
        TimeMgr:CreateTimer("EquipmentFallback", function()
            if self.ui then
                self.canFallback = true
                SetActive(self.fallbackEquipment.effectYellow, false)
                self:ShowFallbackEffect(false)
                self:RefreshEquipmentListByPart()
                self:OnClickTab(self.clickType)
            end
            if data and IsTableNotEmpty(data.rewards) then
                local result = self:CombineRewards(data.rewards)
                table.insert(result, {
                    code = data.equip.code,
                    amount = data.equip.amount
                })
                UI_SHOW(UIDefine.UI_EquipmentRecharge, result, function ()
                end)
            end
        end, delay, 1)
    end)
end

--- 回退埋点
--- @param equipment table 装备
--- @param rewards table 回退获得
--- @param data table 回调数据
function UI_EquipmentDevelopView:TrackEventFallback(equipment, rewards, data)
    local result = {}
    table.insert(result, {
        id = data.equip.code,
        num = data.equip.amount
    })
    for _, rewardValue in ipairs(rewards) do
        local isAdd = true
        for _, resultValue in ipairs(result) do
            if rewardValue.code == resultValue.id then
                resultValue.num = resultValue.num + rewardValue.amount
                isAdd = false
                break
            end
        end
        if isAdd then
            local item = {
                id = rewardValue.code,
                num = rewardValue.amount
            }
            table.insert(result, item)
        end
    end
    EquipmentManager:TrackEventEquipment("EquipmentFallback", equipment, result, nil)
end

--- 合并奖励
--- @param rewards table 奖励列表
--- @return table result 合并后的奖励列表
function UI_EquipmentDevelopView:CombineRewards(rewards)
    local result = {}
    for _, rewardValue in ipairs(rewards) do
        local isAdd = true
        for _, resultValue in ipairs(result) do
            if rewardValue.code == resultValue.code then
                resultValue.amount = resultValue.amount + rewardValue.amount
                isAdd = false
                break
            end
        end
        if isAdd then
            local data = deepcopy(rewardValue)
            table.insert(result, data)
        end
    end
    return result
end

--- 一键选择升星材料装备
function UI_EquipmentDevelopView:RisingStarOneClick()
    if IsTableNotEmpty(CurSelectEquip) and RisingStarCurNum < RisingStarTargetNum then
        if #self.selectMaterialSlider.datas > 0 then
            RisingStarMaterial = {}
            RisingStarCurNum = 0

            if IsTableEmpty(CurSelectEquip) then return end
            local equipment = CurSelectEquip.data
            local config = EquipmentManager:GetSingleStarConfig(equipment.code, equipment.star)
            if config and config.cost then
                local costTable = string.split(config.cost, "|")
                local costID = v2n(costTable[1])
                local costNum = v2n(costTable[2])
                local data = {}

                for _, value in ipairs(self.selectMaterialSlider.datas) do
                    if RisingStarCurNum < costNum then
                        table.insert(RisingStarMaterial, value.id)

                        RisingStarCurNum = 0
                        self:ComputeRisingStarMaterial(data)
                    end
                end
            end
            self:RefreshReturnMaterialList()
            self:RefreshRisingStarMaterial()
            self:RefreshSelectMaterialList()
        end
    end
end

--- 点击侧边栏页签
--- @param type number 页签类型
function UI_EquipmentDevelopView:OnClickTab(type)
    local clickType = type or EQUIPMENT_DEVELOP_VIEW_TYPE.STRENGTHEN
    self:InitList(clickType)
    -- 页签高亮
    SetActive(self.ui.m_imgTabStrengthenLight, clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.STRENGTHEN)
    SetActive(self.ui.m_imgTabRisingStarLight, clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.RISINGSTAR)
    SetActive(self.ui.m_imgTabResolveLight, clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.RESOLVE)
    SetActive(self.ui.m_imgTabFallbackLight, clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.FALLBACK)

    local strengthenToRisingStar = (self.clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.STRENGTHEN
        and clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.RISINGSTAR)
    local risingStarToStrengthen = (self.clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.RISINGSTAR
        and clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.STRENGTHEN)

    local targetPartType
    -- 从强化到升星、从升星到强化，不改变选中的装备
    if strengthenToRisingStar or risingStarToStrengthen then
        targetPartType = self.clickPartType
    else
        CurSelectEquip = nil
    end

    self.clickType = clickType
    ClickType = clickType

    ResolveEquipment = {}
    self:CancelSelectEquipmentItems()
    SetActive(self.ui.m_goNotSelect, true)
    SetActive(self.ui.m_goNotSelectFallback, false)
    SetActive(self.ui.m_goStrengthen, false)
    SetActive(self.ui.m_goRisingStar, false)
    SetActive(self.ui.m_goResolve, false)
    SetActive(self.ui.m_goResolveSelector, false)
    SetActive(self.ui.m_goFallback, false)

    SetActive(self.ui.m_goPartTab, false)
    SetActive(self.ui.m_txtPartTip, false)

    self.hasSelectGreen = false
    SetActive(self.ui.m_imgResolveSelectTab1, self.hasSelectGreen)
    self.hasSelectBlue = false
    SetActive(self.ui.m_imgResolveSelectTab2, self.hasSelectBlue)
    self.hasSelectPurple = false
    SetActive(self.ui.m_imgResolveSelectTab3, self.hasSelectPurple)
    self.hasSelectGold = false
    SetActive(self.ui.m_imgResolveSelectTab4, self.hasSelectGold)

    self.rectScrollviewEquipment.offsetMax = Vector2.New(0, 0)
    self.rectScrollviewEquipment.offsetMin = Vector2.New(0, 0)
    self.equipSlider.viewportHeight = 708

    self.ui.m_txtNoEquipment.text = LangMgr:GetLang(70000190)
    self.ui.m_txtNotSelect.text = LangMgr:GetLang(70000218)
    self.ui.m_txtBtnGrey.text = LangMgr:GetLang(70000063)

    if clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.STRENGTHEN then
        self.ui.m_txtNotSelect.text = LangMgr:GetLang(70000218)
        self.ui.m_txtBtnGrey.text = LangMgr:GetLang(70000063)
        SetActive(self.ui.m_goPartTab, true)
        SetUISize(self.ui.m_rtransContainer, 927, 1220)
        SetUISize(self.ui.m_rtransList, 876, 508)
        self.equipSlider.viewportHeight = 392
        SetUIPos(self.ui.m_txtNotSelect, -9, -27)
    elseif clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.RISINGSTAR then
        self.ui.m_txtNotSelect.text = LangMgr:GetLang(70000219)
        self.ui.m_txtBtnGrey.text = LangMgr:GetLang(70000071)
        SetActive(self.ui.m_goPartTab, true)
        SetUISize(self.ui.m_rtransContainer, 927, 1253)
        SetUISize(self.ui.m_rtransList, 876, 367)
        self.equipSlider.viewportHeight = 253
        SetUIPos(self.ui.m_txtNotSelect, -9, -96)
    elseif clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.RESOLVE then
        self.ui.m_txtNotSelect.text = LangMgr:GetLang(70000075)
        self.ui.m_txtBtnGrey.text = LangMgr:GetLang(70000077)
        SetActive(self.ui.m_goResolveSelector, true)
        self.ui.m_txtResolveSelectNum.text = LangMgr:GetLang(70000076) .. #ResolveEquipment
        self.rectScrollviewEquipment.offsetMax = Vector2.New(0, 0)
        self.rectScrollviewEquipment.offsetMin = Vector2.New(0, 151)
        self.ui.m_txtPartTip.text = LangMgr:GetLang(70000074)
        SetActive(self.ui.m_txtPartTip, true)
        SetUISize(self.ui.m_rtransContainer, 927, 1253)
        SetUISize(self.ui.m_rtransList, 876, 707)
        self.equipSlider.viewportHeight = 442
        SetUIPos(self.ui.m_txtNotSelect, -9, 45)
    elseif clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.FALLBACK then
        self.ui.m_txtNotSelect.text = LangMgr:GetLang(70000081)
        self.ui.m_txtBtnGrey.text = LangMgr:GetLang(70000082)
        SetActive(self.ui.m_goNotSelect, false)
        SetActive(self.ui.m_goNotSelectFallback, true)
        self.ui.m_txtPartTip.text = LangMgr:GetLang(70000080)
        SetActive(self.ui.m_txtPartTip, true)
        SetUISize(self.ui.m_rtransContainer, 927, 1253)
        SetUISize(self.ui.m_rtransList, 876, 539)
        self.equipSlider.viewportHeight = 425
        SetUIPos(self.ui.m_txtNotSelect, -9, -14)
    end

    self:OnClickPartTab(targetPartType or self.clickPartType)
end

--- 点击装备部位页签
--- @param type number 页签类型
function UI_EquipmentDevelopView:OnClickPartTab(type)
    local clickPartType = type or EQUIPMENT_PART.WEAPON

    local isChangePart = self.clickPartType ~= clickPartType

    self.clickPartType = clickPartType

    self.m_imgPartTab1.color = ShowColor
    self.m_imgPartTab2.color = ShowColor
    self.m_imgPartTab3.color = ShowColor
    self.m_imgPartTab4.color = ShowColor

    if clickPartType == EQUIPMENT_PART.WEAPON then
        self.m_imgPartTab1.color = HideColor
    elseif clickPartType == EQUIPMENT_PART.ARMOUR then
        self.m_imgPartTab2.color = HideColor
    elseif clickPartType == EQUIPMENT_PART.CHIP then
        self.m_imgPartTab3.color = HideColor
    elseif clickPartType == EQUIPMENT_PART.RADAR then
        self.m_imgPartTab4.color = HideColor
    end

    self:RefreshEquipmentListByPart()

    -- 部位页签改变了，默认选中第一个
    if isChangePart then
        if CurTargetID then
            local equipment = EquipmentManager:FindEquipmentWearingWithPos(CurTargetID, self.clickPartType)
            if equipment then
                self:SelectItemByID(equipment.id)
            else
                self:SelectFirstItem()
            end
        else
            self:SelectFirstItem()
        end
    else
        if CurTargetID then
            local equipment = EquipmentManager:FindEquipmentWearingWithPos(CurTargetID, self.clickPartType)
            if equipment then
                self:SelectItemByID(equipment.id)
            else
                self:SelectFirstItem()
            end
        else
            -- 当前未选中装备，默认选中第一个
            if IsTableEmpty(CurSelectEquip) then
                self:SelectFirstItem()
            -- 已选择装备，刷新一下界面
            else
                UI_UPDATE(UIDefine.UI_EquipmentDevelopView, 1)
                UI_UPDATE(UIDefine.UI_EquipmentDevelopView, 4)
            end
        end
    end

    SetActive(self.ui.m_imgPartTabLight1, clickPartType == EQUIPMENT_PART.WEAPON)
    SetActive(self.ui.m_imgPartTabLight2, clickPartType == EQUIPMENT_PART.ARMOUR)
    SetActive(self.ui.m_imgPartTabLight3, clickPartType == EQUIPMENT_PART.CHIP)
    SetActive(self.ui.m_imgPartTabLight4, clickPartType == EQUIPMENT_PART.RADAR)

    for _, value in ipairs(self.effectList) do
        SetActive(value, false)
    end

    self:SetEquipmentMaskSize()
end

--- 点击分解选中按钮
--- @param type number 品质
function UI_EquipmentDevelopView:OnClickSelectTab(type)
    if type == ITEM_QUALITY.GREEN then
        self.hasSelectGreen = not self.hasSelectGreen
        SetActive(self.ui.m_imgResolveSelectTab1, self.hasSelectGreen)
        if self.hasSelectGreen then
            self:SelectEquipmentByQuality(type)
        else
            self:CancelSelectEquipmentByQuality(type)
        end
    elseif type == ITEM_QUALITY.BLUE then
        self.hasSelectBlue = not self.hasSelectBlue
        SetActive(self.ui.m_imgResolveSelectTab2, self.hasSelectBlue)
        if self.hasSelectBlue then
            self:SelectEquipmentByQuality(type)
        else
            self:CancelSelectEquipmentByQuality(type)
        end
    elseif type == ITEM_QUALITY.PURPLE then
        self.hasSelectPurple = not self.hasSelectPurple
        SetActive(self.ui.m_imgResolveSelectTab3, self.hasSelectPurple)
        if self.hasSelectPurple then
            self:SelectEquipmentByQuality(type)
        else
            self:CancelSelectEquipmentByQuality(type)
        end
    elseif type == ITEM_QUALITY.GOLD then
        self.hasSelectGold = not self.hasSelectGold
        SetActive(self.ui.m_imgResolveSelectTab4, self.hasSelectGold)
        if self.hasSelectGold then
            self:SelectEquipmentByQuality(type)
        else
            self:CancelSelectEquipmentByQuality(type)
        end
    end

    self:RefreshEquipmentItems()
    self:RefreshEquipInfo()
    self:RefreshResolveList()
end

--- 根据部位过滤
--- @param data table 装备列表
--- @param type number 部位类型
--- @return table result 过滤列表
function UI_EquipmentDevelopView:FilterByPart(data, type)
    local result = {}
    for _, value in ipairs(data) do
        if value.pos == type then
            table.insert(result, value)
        end
    end
    return result
end

--- 初始化装备列表
function UI_EquipmentDevelopView:InitEquipmentList()
    -- 初始化组件
    SetActive(self.ui.m_goEquipmentItem, false)
    self.equipSlider = SlideRect.new()
    self.equipSlider:Init(self.ui.m_scrollviewEquipment, 2)
    self.equipList = {}
    for i = 1, 7 do
        self.equipList[i] = EquipmentItem.new()
        self.equipList[i]:Init(UEGO.Instantiate(self.ui.m_goEquipmentItem.transform))
    end
    self.equipSlider:SetItems(self.equipList, 14, Vector2.New(0, 24))
end

--- 根据部位刷新装备列表数据
function UI_EquipmentDevelopView:RefreshEquipmentListByPart()
    self.data = EquipmentManager.equipmentList
    if self.clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.STRENGTHEN
    or self.clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.RISINGSTAR then
        Tween.Kill("AutoMoveFunc")
        local filter = self:FilterByPart(self.data, self.clickPartType)
        self:SortEquipmentList(filter)
        local result = self:SplitDataByColumns(filter)
        -- 强化后定位到选中的装备
        local targetPos = 1
        if CurSelectEquip then
            local equipment = CurSelectEquip.data
            local targetIndex = 1
            for index, value in ipairs(filter) do
                if equipment.id == value.id then
                    targetIndex = index
                end
            end
            targetPos = math.floor(targetIndex / 4)
        end
        self.equipSlider:SetData(result, targetPos)
    elseif self.clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.RESOLVE then
        local filter = self.data
        filter = self:ExcludeWearing(filter)
        self:SortEquipmentList(filter)
        local result = self:SplitDataByColumns(filter)
        self.equipSlider:SetData(result)
    elseif self.clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.FALLBACK then
        local filter = self.data
        filter = self:ExcludeWearing(filter)
        filter = self:ExcludeOrigin(filter)
        self:SortEquipmentList(filter)
        local result = self:SplitDataByColumns(filter)
        self.equipSlider:SetData(result)
    end

    if self.equipSlider.datas and #self.equipSlider.datas > 0 then
        SetActive(self.ui.m_goNoEquipment, false)
    else
        if #EquipmentManager.equipmentList > 0 then
            self.ui.m_txtNoEquipment.text = LangMgr:GetLang(70000190)
            SetActive(self.ui.m_btnGotoLottery, false)
            SetActive(self.ui.m_btnGotoLottery.transform.parent, false)
        else
            self.ui.m_txtNoEquipment.text = LangMgr:GetLang(70000188)
            SetActive(self.ui.m_btnGotoLottery, true)
            SetActive(self.ui.m_btnGotoLottery.transform.parent, true)
        end
        if self.clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.RISINGSTAR then
            SetActive(self.ui.m_btnGotoLottery, false)
            SetActive(self.ui.m_btnGotoLottery.transform.parent, false)
        end
        SetActive(self.ui.m_goNoEquipment, true)
        SetActive(self.ui.m_goResolveSelector, false)
    end
end

--- 排除已穿戴的装备
--- @param data table 装备列表
--- @return table result 筛选后的装备列表
function UI_EquipmentDevelopView:ExcludeWearing(data)
    local result = {}
    for _, value in ipairs(data) do
        if value.target_id == 0 then
            table.insert(result, value)
        end
    end
    return result
end

--- 排除原始装备
--- @param data table 装备列表
--- @return table result 筛选后的装备列表
function UI_EquipmentDevelopView:ExcludeOrigin(data)
    local result = {}
    for _, value in ipairs(data) do
        if value.level ~= 0 or value.star ~= 0 then
            table.insert(result, value)
        end
    end
    return result
end

--- 装备列表排序
--- @param data table 装备列表
function UI_EquipmentDevelopView:SortEquipmentList(data)
    if self.clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.RESOLVE then
        table.sort(data, function (a, b)
            if a.quality ~= b.quality then
                return a.quality < b.quality
            elseif a.star ~= b.star then
                return a.star < b.star
            elseif a.level ~= b.level then
                return a.level < b.level
            elseif a.pos ~= b.pos then
                return a.pos < b.pos
            elseif a.target_id ~= b.target_id then
                return a.target_id < b.target_id
            end
            return false
        end)
    else
        table.sort(data, function (a, b)
            if a.quality ~= b.quality then
                return a.quality > b.quality
            elseif a.star ~= b.star then
                return a.star > b.star
            elseif a.level ~= b.level then
                return a.level > b.level
            elseif a.pos ~= b.pos then
                return a.pos > b.pos
            elseif a.target_id ~= b.target_id then
                return a.target_id > b.target_id
            end
            return false
        end)
    end
end

--- 根据列数分割数据
--- @param data table 原始数据
--- @param column number|nil 列数
--- @return table result 分割数据
function UI_EquipmentDevelopView:SplitDataByColumns(data, column)
    local result = {}
    local group = {}
    local count = 0
    local curColumn = column or ItemColumns
    for index, value in ipairs(data) do
        table.insert(group, value)
        count = count + 1
        -- 当前分组已满 或 循环结束
        if count >= curColumn or index == #data then
            table.insert(result, group)
            -- 新分组
            group = {}
            count = 0
        end
    end
    return result
end

--- 刷新装备信息
function UI_EquipmentDevelopView:RefreshEquipInfo()
    if self.clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.STRENGTHEN then
        self:RefreshEquipStrengthenInfo()
    elseif self.clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.RISINGSTAR then
        self:RefreshEquipRisingStarInfo()
    elseif self.clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.RESOLVE then
        self:RefreshEquipResolveInfo()
    elseif self.clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.FALLBACK then
        self:RefreshEquipFallbackInfo()
    end
end

--- 刷新装备强化信息
function UI_EquipmentDevelopView:RefreshEquipStrengthenInfo(hasEffect)
    if IsTableNotEmpty(CurSelectEquip) then
        SetActive(self.ui.m_goStrengthen, true)
        SetActive(self.ui.m_goNotSelect, false)

        SetActive(self.ui.m_transStrengthenEquipment, true)
        SetActive(self.ui.m_goStrengthenLevel, true)
        SetActive(self.ui.m_goPromote, false)

        local equipment = CurSelectEquip.data
        local levelLimit = EquipmentManager:GetTableValue(equipment.code, "level_limit")

        -- 装备满级（包含晋升）
        if equipment.level >= levelLimit then

            local promoteLevel = v2n(EquipmentManager:GetTableValueByLevel(equipment.code, equipment.level, "promote_level")) or 0
            local promote = equipment.promotion_lv or 0
            -- 已完成最后一次晋升
            if promote >= promoteLevel then
                SetActive(self.ui.m_txtStrengthenLevelMax, true)
                SetActive(self.ui.m_goStrengthenMaterials, false)
                SetActive(self.ui.m_txtStrengthenLevelBefore, false)
                SetActive(self.ui.m_txtStrengthenLevelAfter, false)
                SetActive(self.ui.m_goStrengthenLevel, false)

                self:ShowLevelUpBtn(LevelUpBtnType.StrengthenGrey)
                self.ui.m_btnStrengthenGrey.interactable = false

                self.ui.m_btnStrengthen.transform.localScale = Vector3.New(1, 1, 1)
                local imgRisingStar = GetComponent(self.ui.m_btnStrengthen, UEUI.Image)
                imgRisingStar.color = Color.HexToRGB("ffffff")
            -- 满级了但还有晋升机会
            else
                SetActive(self.ui.m_txtStrengthenLevelMax, false)
                SetActive(self.ui.m_goStrengthenMaterials, true)
                SetActive(self.ui.m_txtStrengthenLevelBefore, true)
                SetActive(self.ui.m_txtStrengthenLevelAfter, true)

                SetActive(self.ui.m_transStrengthenEquipment, false)
                SetActive(self.ui.m_goStrengthenLevel, false)
                SetActive(self.ui.m_goPromote, true)

                local costPromote = EquipmentManager:GetTableValueByLevel(equipment.code, equipment.level, "promote_cost")

                -- 可晋升
                if not IsNilOrEmpty(costPromote) and equipment.level > promote then
                    local materials = {}
                    if costPromote then
                        local costList = string.split(costPromote, ";")
                        for _, value in ipairs(costList) do
                            local costTable = string.split(value, "|")
                            local id = v2n(costTable[1])
                            local num = v2n(costTable[2])
                            if id and num then
                                table.insert(materials, { id = id, num = num })
                            end
                        end
                    end
                    self:RefreshMaterial(self.ui.m_goStrengthenMaterials, materials)

                    -- 晋升材料是否足够
                    local isEnough = false
                    local enoughCount = 0
                    local notEnoughList = {}
                    for _, value in ipairs(materials) do
                        local curNum = BagManager:GetBagItemCount(value.id)
                        if curNum >= value.num then
                            enoughCount = enoughCount + 1
                        else
                            isEnough = false
                            table.insert(notEnoughList, {
                                id = value.id,
                                needNum = value.num
                            })
                        end
                    end

                    if enoughCount == #materials then
                        isEnough = true
                    end

                    -- 晋升材料足够
                    if isEnough then
                        self:ShowLevelUpBtn(LevelUpBtnType.Promote)
                    -- 晋升材料不足
                    else
                        self:ShowLevelUpBtn(LevelUpBtnType.PromoteGrey)
                    end
                end
            end
        -- 装备未满级
        else
            SetActive(self.ui.m_txtStrengthenLevelMax, false)
            SetActive(self.ui.m_goStrengthenLevel, true)
            SetActive(self.ui.m_goStrengthenMaterials, true)
            SetActive(self.ui.m_txtStrengthenLevelBefore, true)
            SetActive(self.ui.m_txtStrengthenLevelAfter, true)

            local costPromote = EquipmentManager:GetTableValueByLevel(equipment.code, equipment.level, "promote_cost")
            local promote = equipment.promotion_lv or 0

            -- 可晋升
            if not IsNilOrEmpty(costPromote) and equipment.level > promote then

                SetActive(self.ui.m_txtStrengthenLevelBefore, false)
                SetActive(self.ui.m_txtStrengthenLevelAfter, false)

                SetActive(self.ui.m_transStrengthenEquipment, false)
                SetActive(self.ui.m_goStrengthenLevel, false)
                SetActive(self.ui.m_goPromote, true)

                local materials = {}
                if costPromote then
                    local costList = string.split(costPromote, ";")
                    for _, value in ipairs(costList) do
                        local costTable = string.split(value, "|")
                        local id = v2n(costTable[1])
                        local num = v2n(costTable[2])
                        if id and num then
                            table.insert(materials, { id = id, num = num })
                        end
                    end
                end
                self:RefreshMaterial(self.ui.m_goStrengthenMaterials, materials)

                -- 晋升材料是否足够
                local isEnough = false
                local enoughCount = 0
                local notEnoughList = {}
                for _, value in ipairs(materials) do
                    local curNum = BagManager:GetBagItemCount(value.id)
                    if curNum >= value.num then
                        enoughCount = enoughCount + 1
                    else
                        isEnough = false
                        table.insert(notEnoughList, {
                            id = value.id,
                            needNum = value.num
                        })
                    end
                end

                if enoughCount == #materials then
                    isEnough = true
                end

                -- 晋升材料足够
                if isEnough then
                    self:ShowLevelUpBtn(LevelUpBtnType.Promote)
                -- 晋升材料不足
                else
                    self:ShowLevelUpBtn(LevelUpBtnType.PromoteGrey)
                end
            -- 可升级
            else
                local materials = {}
                local costLevelUp = EquipmentManager:GetTableValueByLevel(equipment.code, equipment.level, "cost")
                if costLevelUp then
                    local costList = string.split(costLevelUp, ";")
                    for _, value in ipairs(costList) do
                        local costTable = string.split(value, "|")
                        local id = v2n(costTable[1])
                        local num = v2n(costTable[2])
                        if id and num then
                            table.insert(materials, { id = id, num = num })
                        end
                    end
                end
                self:RefreshMaterial(self.ui.m_goStrengthenMaterials, materials)

                -- 升级材料是否足够
                local isEnough = false
                local enoughCount = 0
                local notEnoughList = {}
                for _, value in ipairs(materials) do
                    local curNum = BagManager:GetBagItemCount(value.id)
                    if curNum >= value.num then
                        enoughCount = enoughCount + 1
                    else
                        isEnough = false
                        table.insert(notEnoughList, {
                            id = value.id,
                            needNum = value.num
                        })
                    end
                end

                if enoughCount == #materials then
                    isEnough = true
                end

                -- 升级材料足够
                if isEnough then
                    self:ShowLevelUpBtn(LevelUpBtnType.Strengthen)
                -- 升级材料不足
                else
                    self:ShowLevelUpBtn(LevelUpBtnType.StrengthenGrey)
                    self.ui.m_btnStrengthenGrey.interactable = true
                end
            end
        end
        local bagItemMoudle = EquipmentManager:ConvertToBagItemModule(equipment)
        bagItemMoudle.equipmentData = equipment
        bagItemMoudle.equipmentShowHead = false
        self.strengthenEquipment:UpdateInfo(bagItemMoudle)

        self.promoteEquipmentBefore:UpdateInfo(bagItemMoudle)
        local promoteList = EquipmentManager:GetPromoteConfigByEquipmentID(equipment.code)
        local promote = equipment.promotion_lv or 0
        local promoteIndex = 0
        for index, value in ipairs(promoteList) do
            if promote >= value.promote_level then
                promoteIndex = index
            end
        end
        local nextIndex = math.min(promoteIndex + 1, #promoteList)
        local copyEquipment = deepcopy(equipment)
        if promoteList[nextIndex] then
            local nextPromoteLevel = promoteList[nextIndex].promote_level
            copyEquipment.promotion_lv = nextPromoteLevel
        end

        if copyEquipment.level >= levelLimit then
            local promoteLevel = v2n(EquipmentManager:GetTableValueByLevel(copyEquipment.code, copyEquipment.level, "promote_level"))
            local promote2 = copyEquipment.promotion_lv or 0
            -- 已完成最后一次晋升
            if promoteLevel and promote2 >= promoteLevel then
                copyEquipment.redQuality = 5
            end
        end
        local bagItemMoudleAfter = EquipmentManager:ConvertToBagItemModule(copyEquipment)
        bagItemMoudleAfter.equipmentData = copyEquipment
        bagItemMoudleAfter.equipmentShowHead = false
        self.promoteEquipmentAfter:UpdateInfo(bagItemMoudleAfter)

        self.ui.m_txtStrengthenLevelBefore.text = "Lv." .. equipment.level
        self.ui.m_txtStrengthenLevelAfter.text = "Lv." .. math.min(equipment.level + 1, levelLimit)
        local property = EquipmentManager:ConstructProperty(equipment)
        EquipmentManager:SortProperty(property)
        local propertyExtra = EquipmentManager:ConstructPropertyExtra(equipment)

        -- 叠加晋升属性到额外属性
        for index, promoteValue in ipairs(promoteList) do
            if promote >= promoteValue.promote_level then
                local promoteProperty = promoteValue.upgrade
                local promotePropertyTable = string.split(promoteProperty, "|")
                local promotePropertyName = promotePropertyTable[1]
                local promotePropertyType = v2n(promotePropertyTable[2])
                local promotePropertyValue = v2n(promotePropertyTable[3])
                local extraProperty = propertyExtra[index]
                if extraProperty then
                    if extraProperty.property == promotePropertyName
                    and extraProperty.type == promotePropertyType then
                        extraProperty.value = extraProperty.value + promotePropertyValue
                    end
                end
            end
        end

        local promoteLevel = v2n(EquipmentManager:GetTableValueByLevel(equipment.code, equipment.level, "promote_level"))
        local firstPromote = promoteList[1]
        local firstPromoteLevel = 0
        if firstPromote then
            firstPromoteLevel = firstPromote.promote_level
        end

        if equipment.level >= firstPromoteLevel then
            local initValue
            local lastPromote = promoteList[#promoteList]
            local lastPromoteLevel = 0
            if lastPromote then
                lastPromoteLevel = lastPromote.promote_level
            end

            -- 正在进行最后一次晋升
            if promoteLevel == lastPromoteLevel and promote < lastPromoteLevel then
                initValue = 0
            end

            for _, promoteValue in ipairs(promoteList) do
                if lastPromoteLevel == promoteValue.promote_level then
                    local promoteProperty = promoteValue.upgrade
                    local promotePropertyTable = string.split(promoteProperty, "|")
                    local promotePropertyName = promotePropertyTable[1]
                    local promotePropertyType = v2n(promotePropertyTable[2])
                    local promotePropertyValue = initValue or v2n(promotePropertyTable[3])
                    local mythProperty = {
                        name = GetAttrName(v2n(promotePropertyName)),
                        percentName = GetAttrPercentName(v2n(promotePropertyName)),
                        property = promotePropertyName,
                        type = promotePropertyType,
                        value = promotePropertyValue,
                    }
                    table.insert(propertyExtra, mythProperty)
                end
            end
        end

        self:RefreshPropertyGroup(self.ui.m_goPropertyStrengthenList2, property,
            EQUIPMENT_DEVELOP_VIEW_TYPE.STRENGTHEN, false, hasEffect)
        self:RefreshPropertyGroup(self.ui.m_goExtraPropertyStrengthenList, propertyExtra,
            EQUIPMENT_DEVELOP_VIEW_TYPE.STRENGTHEN, true, hasEffect)

        if hasEffect then
            local duration = 2
            if self.effectTimer then
                TimeMgr:DestroyTimer(UIDefine.UI_EquipmentDevelopView, self.effectTimer)
            end
            self.effectTimer = TimeMgr:CreateTimer(UIDefine.UI_EquipmentDevelopView, function()
                if not self.ui then return end
                for _, value in ipairs(self.effectList) do
                    SetActive(value, false)
                end
                self.effectList = {}
            end, duration, 1)
        end

        local hasExtra = #propertyExtra > 0
        SetActive(self.ui.m_goExtraPropertyStrengthenList, hasExtra)
        SetActive(self.ui.m_goNoExtraProperty, not hasExtra)
    else
        SetActive(self.ui.m_goStrengthen, false)
        SetActive(self.ui.m_goNotSelect, true)
    end
end

--- 显示升级按钮
--- @param type number 按钮类型
function UI_EquipmentDevelopView:ShowLevelUpBtn(type)
    for index, value in ipairs(self.levelUpBtns) do
        SetActive(value, index == type)
    end
end

--- 刷新装备升星信息
function UI_EquipmentDevelopView:RefreshEquipRisingStarInfo()
    if IsTableNotEmpty(CurSelectEquip) then
        SetActive(self.ui.m_goRisingStar, true)
        SetActive(self.ui.m_goNotSelect, false)
        local equipment = CurSelectEquip.data
        local bagItemMoudle1 = EquipmentManager:ConvertToBagItemModule(equipment)
        bagItemMoudle1.equipmentData = equipment
        bagItemMoudle1.equipmentShowHead = false
        local bagItemMoudle2 = EquipmentManager:ConvertToBagItemModule(equipment)
        local copyEquipment = deepcopy(equipment)
        copyEquipment.star = math.min(equipment.star + 1, EquipmentStarMax)
        bagItemMoudle2.equipmentData = copyEquipment
        bagItemMoudle2.equipmentShowHead = false
        self.risingStarEquipment1:UpdateInfo(bagItemMoudle1)
        self.risingStarEquipment2:UpdateInfo(bagItemMoudle2)
        local property = EquipmentManager:ConstructPropertyStarUp(equipment)
        self:RefreshPropertyGroup(self.ui.m_goPropertyRisingStarList, property,
            EQUIPMENT_DEVELOP_VIEW_TYPE.RISINGSTAR, false, false)

        -- 设置列表数据
        local data = {}
        if IsTableNotEmpty(CurSelectEquip) then
            for _, value in ipairs(EquipmentManager.equipmentList) do
                if value.pos == CurSelectEquip.data.pos
                    and value.quality == CurSelectEquip.data.quality
                    and value.id ~= CurSelectEquip.data.id
                    and value.target_id == 0 then
                    table.insert(data, value)
                end
            end
        end
        table.sort(data, function (a, b)
            if a.star ~= b.star then
                return a.star < b.star
            elseif a.level ~= b.level then
                return a.level < b.level
            end
            return false
        end)
        RisingStarMaterial = {}
        local addEquipment = EquipmentModule.new({
            equip_id = "",
            code = equipment.code,
            amount = 1,
            level = 0,
            star = 0,
            target_id = 0
        })
        local addBtn = {
            flag = 1,
            equipment = addEquipment
        }
        table.insert(data, addBtn)
        self.selectMaterialSlider:SetData(data)

        local count = #self.selectMaterialSlider.datas
        SetUISize(self.ui.m_transSelectMaterials, 138 * count, 136)

        local isStarNotMax = equipment.star < EquipmentStarMax
        SetActive(self.ui.m_imgProBack, isStarNotMax)
        SetActive(self.ui.m_scrollviewSelectMaterials, isStarNotMax)
        SetActive(self.ui.m_btnRisingStar, isStarNotMax)
        SetActive(self.ui.m_btnRisingStarOneClick, isStarNotMax)
        SetActive(self.ui.m_txtRisingStarMax, not isStarNotMax)
        SetActive(self.ui.m_btnRisingStarGrey, not isStarNotMax)

        if not isStarNotMax then
            self.ui.m_btnRisingStar.transform.localScale = Vector3.New(1, 1, 1)
            self.ui.m_btnRisingStarOneClick.transform.localScale = Vector3.New(1, 1, 1)

            local imgRisingStar = GetComponent(self.ui.m_btnRisingStar, UEUI.Image)
            imgRisingStar.color = Color.HexToRGB("ffffff")
            local imgRisingStarOneClick = GetComponent(self.ui.m_btnRisingStarOneClick, UEUI.Image)
            imgRisingStarOneClick.color = Color.HexToRGB("ffffff")
        end

        self:RefreshReturnMaterialList()
        self:RefreshRisingStarMaterial()
        self:RefreshSelectMaterialList()
    else
        SetActive(self.ui.m_goRisingStar, false)
        SetActive(self.ui.m_goNotSelect, true)
    end
end

--- 刷新装备分解信息
function UI_EquipmentDevelopView:RefreshEquipResolveInfo()
    if IsTableNotEmpty(ResolveEquipment) then
        SetActive(self.ui.m_goResolve, true)
        SetActive(self.ui.m_goNotSelect, false)
    else
        SetActive(self.ui.m_goResolve, false)
        SetActive(self.ui.m_goNotSelect, true)
    end

    self.ui.m_txtResolveSelectNum.text = LangMgr:GetLang(70000076) .. #ResolveEquipment
end

--- 刷新装备回退信息
function UI_EquipmentDevelopView:RefreshEquipFallbackInfo()
    if IsTableNotEmpty(CurSelectEquip) then
        SetActive(self.ui.m_goFallback, true)
        SetActive(self.ui.m_goNotSelect, false)
        SetActive(self.ui.m_goNotSelectFallback, false)
        local equipment = CurSelectEquip.data
        local bagItemMoudle = EquipmentManager:ConvertToBagItemModule(equipment)
        bagItemMoudle.equipmentData = equipment
        bagItemMoudle.equipmentShowHead = false
        self.fallbackEquipment:UpdateInfo(bagItemMoudle)
        self:RefreshFallbackList()
    else
        SetActive(self.ui.m_goFallback, false)
        SetActive(self.ui.m_goNotSelect, false)
        SetActive(self.ui.m_goNotSelectFallback, true)
    end
end

--- 刷新属性组
--- @param parent any 父节点
--- @param property table 属性列表
--- @param type number 类型
--- @param isExtra boolean 是否额外属性
--- @param hasEffect boolean 是否有特效
function UI_EquipmentDevelopView:RefreshPropertyGroup(parent, property, type, isExtra, hasEffect)
    local childCount = parent.transform.childCount - 1
    for i = 1, childCount, 1 do
        local item = parent.transform:GetChild(i)
        SetActive(item, false)
    end

    local equipment = CurSelectEquip.data
    for key, value in ipairs(property) do
        local item
        if key <= childCount then
            item = parent.transform:GetChild(key)
        else
            local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "EquipmentPropertyItem")
            local itemPrefab = ResMgr:LoadAssetSync(assetPath, AssetDefine.LoadType.Instant)
            item = CreateGameObjectWithParent(itemPrefab, parent)
        end

        local bg1 = GetChild(item, "bg1")
        local bg2 = GetChild(item, "bg2")
        local bg3 = GetChild(item, "bg3")
        local line = GetChild(item, "line")
        local horizontalGroup = GetComponent(item, UEUI.HorizontalLayoutGroup)
        SetActive(bg1, false)
        SetActive(bg2, false)
        SetActive(bg3, false)
        local showLine = key + 2 <= #property
        SetActive(line, false)

        local icon = GetChild(item, "iconParent/icon", UEUI.Image)
        local extraIcon = GetChild(item, "extraIcon", UEUI.Image)
        local propertyIcon = EquipmentManager:GetPropertyIcon(value.property)
        if propertyIcon then
            SetUIImage(icon, propertyIcon, false)
            SetUIImage(extraIcon, propertyIcon, false)
        end

        local extra = GetChild(item, "iconParent/extra", UEUI.Image)
        SetUIPos(extra, 0, -2)
        local extraNum = GetChild(item, "iconParent/extra/num", UEUI.Text)
        extraNum.text = value.level

        local gemSlot = GetChild(item, "iconParent/gemSlot")
        local imgGem = GetChild(item, "iconParent/gemSlot/gem", UEUI.Image)

        local txtName = GetChild(item, "name", UEUI.Text)
        txtName.text = value.name
        txtName.fontSize = 30
        txtName.resizeTextMaxSize = 30
        local txtValue = GetChild(item, "valueParent/value", UEUI.Text)
        if value.type and value.type == 2 then
            txtName.text = value.percentName
            txtValue.text = (v2n(value.value) / 100) .. "%"
            local property2 = value.property .. "-2"
            local propertyIcon2 = EquipmentManager:GetPropertyIcon(property2)
            if propertyIcon2 then
                SetUIImage(icon, propertyIcon2, false)
                SetUIImage(extraIcon, propertyIcon2, false)
            end
        else
            txtValue.text = value.value
        end
        txtValue.fontSize = 28

        local valueChange = GetChild(item, "valueChange")
        local valueBefore = GetChild(item, "valueChange/valueBefore", UEUI.Text)
        local valueAfter = GetChild(item, "valueChange/valueAfter", UEUI.Text)

        local effect = GetChild(item, "valueChange/effect")

        if hasEffect then
            SetActive(effect, true)
            local particle = GetChild(item, "valueChange/effect/battle_shuzishuaguang1/Particle System", UE.ParticleSystem)
            local particleChild = GetChild(item, "valueChange/effect/battle_shuzishuaguang1/Particle System/Particle System (1)", UE.ParticleSystem)

            local render = particle.transform:GetComponentInChildren(typeof(UE.Renderer))
            local renderChild = particleChild.transform:GetComponentInChildren(typeof(UE.Renderer))
            SetRendererOrder(render, SortingLayerInGame.Default, self:GetViewSortingOrder())
            SetRendererOrder(renderChild, SortingLayerInGame.Default, self:GetViewSortingOrder())
            particle:Play()

            table.insert(self.effectList, effect)
        end

        local outline = txtName:GetComponent(typeof(UEUI.Outline))
        local shadow = txtName:GetComponent(typeof(CS.Coffee.UIEffects.UIShadow))
        outline.enabled = true
        shadow.enabled = true

        -- 当前阶段数据
        local curData
        if type == EQUIPMENT_DEVELOP_VIEW_TYPE.STRENGTHEN then
            curData = EquipmentManager:GetSingleLevelConfig(equipment.code, equipment.level)
        elseif type == EQUIPMENT_DEVELOP_VIEW_TYPE.RISINGSTAR then
            curData = EquipmentManager:GetSingleStarConfig(equipment.code, equipment.star)
        end

        if curData then
            if curData[value.property] then
                valueBefore.text = curData[value.property]
            else
                local field = curData[value.field]
                if not IsNilOrEmpty(field) then
                    local curValue = EquipmentManager:GetPropertyValue(field, value)
                    valueBefore.text = curValue
                end
            end
        end

        -- 下个阶段数据
        local nextData
        if type == EQUIPMENT_DEVELOP_VIEW_TYPE.STRENGTHEN then
            nextData = EquipmentManager:GetSingleLevelConfig(equipment.code, equipment.level + 1)
        elseif type == EQUIPMENT_DEVELOP_VIEW_TYPE.RISINGSTAR then
            nextData = EquipmentManager:GetSingleStarConfig(equipment.code, equipment.star + 1)
        end

        if nextData then
            if nextData[value.property] then
                valueAfter.text = nextData[value.property]
            else
                local field = nextData[value.field]
                if not IsNilOrEmpty(field) then
                    local afterValue = EquipmentManager:GetPropertyValue(field, value)
                    valueAfter.text = afterValue
                end
            end
        elseif curData then
            if curData[value.property] then
                valueAfter.text = curData[value.property]
            else
                local field = curData[value.field]
                if not IsNilOrEmpty(field) then
                    local afterValue = EquipmentManager:GetPropertyValue(field, value)
                    valueAfter.text = afterValue
                end
            end
        end

        -- 额外属性
        if isExtra then
            local isActive
            if value.level then
                -- 额外属性是否已激活
                isActive = equipment.level >= value.level
            else
                isActive = true
            end
            if isActive then
                txtName.color = GetColorByHex("#86FF65")
                txtValue.color = GetColorByHex("#86FF65")
                SetUIImage(extra, "Sprite/ui_slg_beibao/zb_shuxing_textbg1.png", false)
                UnifyOutline(txtName, "#004B20")
                UnifyOutline(txtValue, "#004B20")
            else
                txtName.color = GetColorByHex("C4C4C4")
                txtValue.color = GetColorByHex("C4C4C4")
                SetUIImage(extra, "Sprite/ui_slg_beibao/zb_shuxing_textbg2.png", false)
                UnifyOutline(txtName, "#415D70")
                UnifyOutline(txtValue, "#415D70")
            end
            SetActive(bg3, true)
            SetActive(icon, false)
            SetActive(extra, true)
            SetActive(txtValue, true)
            SetActive(valueChange, false)
            SetActive(extraIcon, true)
            SetUISize(txtName, 189, 80)
            horizontalGroup.padding.left = 12
            horizontalGroup.spacing = 7

            -- 晋升宝石槽位
            local promoteLevel = v2n(EquipmentManager:GetTableValueByLevel(equipment.code, equipment.level, "promote_level"))
            local promote = equipment.promotion_lv or 0
            SetActive(gemSlot, promote > 0 or promoteLevel ~= nil)
            SetActive(extra, not gemSlot.gameObject.activeSelf)
            SetActive(imgGem, false)
            local promoteList = EquipmentManager:GetPromoteConfigByEquipmentID(equipment.code)
            local promoteIndex = 0
            for index, promoteValue in ipairs(promoteList) do
                if promote >= promoteValue.promote_level then
                    promoteIndex = index
                end
            end

            local costPromote = EquipmentManager:GetTableValueByLevel(equipment.code, equipment.level, "promote_cost")
            local promote = equipment.promotion_lv or 0

            if key <= promoteIndex then
                SetActive(imgGem, true)
                SetUIImage(imgGem, EquipmentManager:GetPromoteGemIcon(key), false)
                txtValue.color = GetColorByHex("22fffd")
                UnifyOutline(txtValue, "2e5397")
                txtName.color = GetColorByHex("22fffd")
                UnifyOutline(txtName, "2e5397")
            elseif equipment.level >= 40 and key == promoteIndex + 1 then
                -- 可晋升
                if not IsNilOrEmpty(costPromote) and equipment.level > promote then
                    SetActive(txtValue, false)
                    SetActive(valueChange, true)
                    SetActive(effect, false)

                    for _, promoteValue in ipairs(promoteList) do
                        if promote < promoteValue.promote_level then
                            local promoteProperty = promoteValue.upgrade
                            local promotePropertyTable = string.split(promoteProperty, "|")
                            local promotePropertyName = promotePropertyTable[1]
                            local promotePropertyType = v2n(promotePropertyTable[2])
                            local promotePropertyValue = v2n(promotePropertyTable[3])
                            local extraProperty = value
                            if extraProperty then
                                if extraProperty.property == promotePropertyName
                                and extraProperty.type == promotePropertyType then
                                    extraProperty.nextValue = extraProperty.value + promotePropertyValue
                                    break
                                end
                            end
                        end
                    end

                    if value.type and value.type == 2 then
                        if value.nextValue then
                            valueAfter.text = (v2n(value.nextValue) / 100) .. "%"
                        else
                            valueAfter.text = txtValue.text
                        end
                    else
                        valueAfter.text = value.nextValue or txtValue.text
                    end

                    valueBefore.text = txtValue.text

                    valueBefore.color = GetColorByHex("86FF65")
                    UnifyOutline(valueBefore, "#004B20")
                    valueAfter.color = GetColorByHex("22fffd")
                    UnifyOutline(valueAfter, "2e5397")

                    SetUIPos(valueChange, -36, 0)
                end
            end

            local lastPromote = promoteList[#promoteList]
            local lastPromoteLevel = 0
            if lastPromote then
                lastPromoteLevel = lastPromote.promote_level
            end

            if key == #promoteList and promote < lastPromoteLevel then
                txtName.color = GetColorByHex("C4C4C4")
                txtValue.color = GetColorByHex("C4C4C4")
                valueBefore.color = GetColorByHex("C4C4C4")
                UnifyOutline(txtName, "#415D70")
                UnifyOutline(txtValue, "#415D70")
            end
        -- 基础属性
        else
            txtName.color = GetColorByHex("FFFFFF")
            valueAfter.color = GetColorByHex("86FF65")
            SetActive(icon, true)
            SetActive(extra, false)
            SetActive(txtValue, false)
            SetActive(valueChange, true)
            SetActive(extraIcon, false)
            SetUISize(txtName, 170, 80)

            local levelLimit = EquipmentManager:GetTableValue(equipment.code, "level_limit")
            if (self.clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.STRENGTHEN and equipment.level >= levelLimit)
            or (self.clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.RISINGSTAR and equipment.star >= 5) then
                SetUISize(txtName, 288, 80)
                SetActive(txtValue, true)
                SetActive(valueChange, false)
            end

            if self.clickType == EQUIPMENT_DEVELOP_VIEW_TYPE.STRENGTHEN then
                local costPromote = EquipmentManager:GetTableValueByLevel(equipment.code, equipment.level, "promote_cost")
                local promote = equipment.promotion_lv or 0
                if not IsNilOrEmpty(costPromote) and equipment.level > promote then
                    SetUISize(txtName, 288, 80)
                    SetActive(txtValue, true)
                    SetActive(valueChange, false)
                end
            end

            SetActive(gemSlot, false)
        end
        SetActive(item, true)
    end
end

--- 刷新材料
--- @param parent any 父节点
--- @param data table 材料数据
function UI_EquipmentDevelopView:RefreshMaterial(parent, data)
    local childCount = parent.transform.childCount
    for i = 1, childCount, 1 do
        local child = parent.transform:GetChild(i - 1)
        SetActive(child, false)
    end

    for index, value in ipairs(data) do
        local item
        if index <= childCount then
            item = parent.transform:GetChild(index - 1)
        else
            item = CreateGameObjectWithParent(self.ui.m_goMaterialItem, parent)
        end
        local icon = GetChild(item, "icon", UEUI.Image)
        SetUIImage(icon, ItemConfig:GetIcon(value.id), false)
        local txtNum = GetComponent(item, UEUI.Text)
        local curNum = BagManager:GetBagItemCount(value.id)
        if curNum >= value.num then
            txtNum.text = string.format("<color=%s>%s</color>/%s", "#6FFB2D", curNum, value.num)
            SetActive(self.ui.m_goStrengthenRedPoint, true)
            SetActive(self.ui.m_goPromoteRedPoint, true)
        else
            txtNum.text = string.format("<color=%s>%s</color>/%s", "#FF5252", curNum, value.num)
            SetActive(self.ui.m_goStrengthenRedPoint, false)
            SetActive(self.ui.m_goPromoteRedPoint, false)
        end
        SetActive(item, true)
    end
end

--- 获取升星材料目标最大值
--- @return number|nil costID 消耗 ID
--- @return number|nil costNum 消耗数量
function UI_EquipmentDevelopView:GetRisingStarMaterialMax()
    if IsTableEmpty(CurSelectEquip) then return end
    local equipment = CurSelectEquip.data
    local config = EquipmentManager:GetSingleStarConfig(equipment.code, equipment.star)
    if config and config.cost then
        local costTable = string.split(config.cost, "|")
        local costID = v2n(costTable[1])
        local costNum = v2n(costTable[2])
        return costID, costNum
    end
end

--- 刷新升星材料
function UI_EquipmentDevelopView:RefreshRisingStarMaterial()
    if IsTableEmpty(CurSelectEquip) then return end
    local equipment = CurSelectEquip.data
    local config = EquipmentManager:GetSingleStarConfig(equipment.code, equipment.star)
    if config and config.cost then
        local costTable = string.split(config.cost, "|")
        local costID = v2n(costTable[1])
        local costNum = v2n(costTable[2])
        RisingStarTargetNum = costNum

        local redColor = "#ff5555"
        if RisingStarCurNum >= costNum then
            redColor = "#ffffff"
        end
        self.ui.m_txtPro.text = string.format("<color=%s>%s</color>/<color=#ffffff>%s</color>",
            redColor, RisingStarCurNum, costNum)
        self.ui.m_sliderPro.value = RisingStarCurNum / costNum

        RisingStarProgress = RisingStarCurNum / costNum
    end
end

--- 选中第一个装备
function UI_EquipmentDevelopView:SelectFirstItem()
    if #self.equipSlider.datas > 0 then
        if self.equipList then
            for i = 1, #self.equipList, 1 do
                if self.equipList[i].index == 1 then
                    self.equipList[i]:SelectFirstItem()
                end
            end
        end
    end
end

--- 根据装备 ID 选中装备
--- @param id string 装备 ID
function UI_EquipmentDevelopView:SelectItemByID(id)
    if IsTableEmpty(self.equipSlider.datas) then
        return
    end

    local index = 1
    local itemIndex = 1
    local equipment
    for key, value in ipairs(self.equipSlider.datas) do
        for itemKey, itemValue in ipairs(value) do
            if itemValue.id == id then
                index = key
                itemIndex = itemKey
                equipment = itemValue
            end
        end
    end

    if equipment then
        CurSelectEquip = {
            data = equipment,
        }
        UI_UPDATE(UIDefine.UI_EquipmentDevelopView, 1)
        UI_UPDATE(UIDefine.UI_EquipmentDevelopView, 4)
    end

    if index > 1 then
        self.equipSlider:MoveToIndex(index, 1, function ()
            -- 选中前，检查选中的装备 ID
            if equipment and CurSelectEquip then
                if equipment.id ~= CurSelectEquip.data.id then
                    return
                end
            end
            if self.equipList then
                for i = 1, #self.equipList, 1 do
                    if self.equipList[i].index == index then
                        self.equipList[i]:SelectItemByIndex(itemIndex)
                    end
                end
            end
        end)
    elseif index == 1 then
        if self.equipList then
            for i = 1, #self.equipList, 1 do
                if self.equipList[i].index == index then
                    self.equipList[i]:SelectItemByIndex(itemIndex)
                end
            end
        end
    end
end

--region ----------------------------------------- 装备 -----------------------------------------

--- 刷新装备 item
function UI_EquipmentDevelopView:RefreshEquipmentItems()
    if self.equipList then
        for i = 1, #self.equipList, 1 do
            self.equipList[i]:Refresh()
        end
    end
end

--- 取消选中装备 item
function UI_EquipmentDevelopView:CancelSelectEquipmentItems()
    if self.equipList then
        for i = 1, #self.equipList, 1 do
            self.equipList[i]:CancelSelect()
        end
    end
end

--- 根据品质选择要分解的装备
--- @param quality number 品质
function UI_EquipmentDevelopView:SelectEquipmentByQuality(quality)
    for _, value in ipairs(self.equipSlider.datas) do
        for _, equipment in ipairs(value) do
            if #ResolveEquipment >= EquipmentResolveMax then return end
            if equipment.quality == quality
                and not self:IsSelectEquipment(equipment.id) then
                table.insert(ResolveEquipment, equipment.id)
            end
        end
    end
end

--- 根据品质取消选择要分解的装备
--- @param quality number 品质
function UI_EquipmentDevelopView:CancelSelectEquipmentByQuality(quality)
    for _, value in ipairs(self.equipSlider.datas) do
        for _, equipment in ipairs(value) do
            if equipment.quality == quality then
                local isSelect, index = self:IsSelectEquipment(equipment.id)
                if isSelect and index then
                    table.remove(ResolveEquipment, index)
                end
            end
        end
    end
end

--- 装备是否被选中分解
--- @param id string 装备唯一 ID
--- @return boolean isSelect 是否被选中
--- @return integer|nil index 索引位置
function UI_EquipmentDevelopView:IsSelectEquipment(id)
    for index, value in ipairs(ResolveEquipment) do
        if value == id then
            return true, index
        end
    end
    return false
end

--- 显示装备特效
function UI_EquipmentDevelopView:ShowEquipmentEffect(isShow)
    if self.equipList then
        for i = 1, #self.equipList, 1 do
            self.equipList[i]:ShowEffect(isShow)
        end
    end
end

--- 设置装备框选特效的显示范围
function UI_EquipmentDevelopView:SetEquipmentMaskSize()
    if self.equipList then
        for i = 1, #self.equipList, 1 do
            self.equipList[i]:SetMaskSize()
        end
    end
end

function BagItem:ClickItem()
    if self.clickFunc then self.clickFunc() end

    if self.type == BagItemType.Equipment then
        UI_UPDATE(UIDefine.UI_EquipmentDevelopView, 1)
        UI_UPDATE(UIDefine.UI_EquipmentDevelopView, 4)
        UI_UPDATE(UIDefine.UI_EquipmentDevelopView, 6)
    elseif self.type == BagItemType.RisingStarMaterial then
        UI_UPDATE(UIDefine.UI_EquipmentDevelopView, 5)
    end

    if ClickType == EQUIPMENT_DEVELOP_VIEW_TYPE.RESOLVE
    and self.type == BagItemType.Equipment then
        local select = GetChild(self.go, "clothesInfo/select")
        local selectBg = GetChild(self.go, "selectBg")
        local isSelect = false
        local dataIndex
        local equipment = self.bagItemMoudle.equipmentData
        for key, value in pairs(ResolveEquipment) do
            if value == equipment.id then
                isSelect = true
                dataIndex = key
                break
            end
        end
        if isSelect and dataIndex then
            table.remove(ResolveEquipment, dataIndex)
            SetActive(select, false)
            SetActive(selectBg, false)
        else
            if #ResolveEquipment < EquipmentResolveMax then
                table.insert(ResolveEquipment, equipment.id)
                SetActive(select, true)
                SetActive(selectBg, true)
            end
        end
        UI_UPDATE(UIDefine.UI_EquipmentDevelopView, 3)
        UI_UPDATE(UIDefine.UI_EquipmentDevelopView, 1)
    end
end

function BagItem:SetType(type)
    self.type = type
end

function BagItem:SetClickFunc(func)
    self.clickFunc = func
end

function EquipmentItem:OnInit(transform)
    self.group = GetChild(transform, "group")
    self.item = GetChild(transform, "item")
    SetActive(self.item, false)

    self.itemList = {}
    local path = "battle_zhuangbeixuanzhong/battle_beibaokabaiguang1/kongxingguang"
    -- 每行生成 ItemColumns 个 item
    for i = 1, ItemColumns, 1 do
        local bagItem = BagItem.new()
        bagItem:Create(self.group)
        bagItem:SetType(BagItemType.Equipment)
        bagItem:SetScale(0.79, 0.79)
        local effectSelect = GetChild(bagItem.go, "selectBg/effectSelect")
        CollectCardManager:SetMaskSize(effectSelect, ScrollviewEquipment)

        local curSortingOrder = SortingOrder + 1
        for j = 1, 5, 1 do
            local particle = GetChild(effectSelect, path .. j, UE.ParticleSystem)
            if particle and particle.gameObject.activeSelf then
                local render = particle.transform:GetComponentInChildren(typeof(UE.Renderer))
                SetRendererOrder(render, SortingLayerInGame.Default, curSortingOrder)
                curSortingOrder = curSortingOrder + 1
            end
        end

        table.insert(self.itemList, bagItem)
    end
end

function EquipmentItem:UpdateData(data, index)
    if not data then return end
    self.data = data
    self.index = index
    for itemIndex, item in ipairs(self.itemList) do
        if self.data[itemIndex] then
            local select = GetChild(item.go, "clothesInfo/select")
            local selectBg = GetChild(item.go, "selectBg")

            SetActive(select, false)
            SetActive(selectBg, false)

            if ClickType == EQUIPMENT_DEVELOP_VIEW_TYPE.RESOLVE then
                if IsTableNotEmpty(ResolveEquipment) then
                    for _, value in ipairs(ResolveEquipment) do
                        if value == data[itemIndex].id then
                            SetActive(select, true)
                            SetActive(selectBg, true)
                        end
                    end
                end
            else
                if IsTableNotEmpty(CurSelectEquip) then
                    if CurSelectEquip.data.id == data[itemIndex].id then
                        SetActive(selectBg, true)
                    end
                end
            end

            local bagItemMoudle = EquipmentManager:ConvertToBagItemModule(self.data[itemIndex])
            bagItemMoudle.equipmentData = self.data[itemIndex]
            item:UpdateInfo(bagItemMoudle)
            item:SetClickFunc(function ()
                CurSelectEquip = {
                    data = self.data[itemIndex]
                }
                if CurSelectEquip.data.target_id ~= 0 then
                    CurTargetID = CurSelectEquip.data.target_id
                end
            end)
            SetActive(item.go, true)
        else
            SetActive(item.go, false)
        end
    end
end

function EquipmentItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function EquipmentItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function EquipmentItem:Refresh()
    self:UpdateData(self.data, self.index)
end

function EquipmentItem:CancelSelect()
    for _, item in ipairs(self.itemList) do
        local select = GetChild(item.go, "clothesInfo/select")
        local selectBg = GetChild(item.go, "selectBg")
        SetActive(select, false)
        SetActive(selectBg, false)
    end
end

function EquipmentItem:SelectFirstItem()
    local index = 1
    CurSelectEquip = {
        data = self.data[index],
    }
    UI_UPDATE(UIDefine.UI_EquipmentDevelopView, 1)
    UI_UPDATE(UIDefine.UI_EquipmentDevelopView, 4)
end

function EquipmentItem:SelectItemByIndex(index)
    CurSelectEquip = {
        data = self.data[index],
    }
    UI_UPDATE(UIDefine.UI_EquipmentDevelopView, 1)
    UI_UPDATE(UIDefine.UI_EquipmentDevelopView, 4)
end

function EquipmentItem:ShowEffect(isShow)
    for _, item in ipairs(self.itemList) do
        if isShow then
            CollectCardManager:SetMaskSize(item.effectYellow, ScrollviewEquipment)
        end
        local select = GetChild(item.go, "clothesInfo/select")
        local isSelect = select.gameObject.activeInHierarchy
        SetActive(item.effectYellow, isShow and isSelect)
    end
end

function EquipmentItem:SetMaskSize()
    for _, item in ipairs(self.itemList) do
        local effectSelect = GetChild(item.go, "selectBg/effectSelect")
        CollectCardManager:SetMaskSize(effectSelect, ScrollviewEquipment)
    end
end

--endregion -------------------------------------- 装备 -----------------------------------------

--region ----------------------------------------- 材料 -----------------------------------------

--- 初始化选择材料列表
function UI_EquipmentDevelopView:InitSelectMaterialList()
    -- 初始化组件
    SetActive(self.ui.m_goSelectMaterialItem, false)
    self.selectMaterialSlider = SlideRect.new()
    self.selectMaterialSlider:Init(self.ui.m_scrollviewSelectMaterials, 1)
    self.selectMaterialList = {}
    for i = 1, ItemShowNums do
        self.selectMaterialList[i] = SelectMaterialItem.new()
        self.selectMaterialList[i]:Init(UEGO.Instantiate(self.ui.m_goSelectMaterialItem.transform))
    end
    self.selectMaterialSlider:SetItems(self.selectMaterialList, 20, Vector2.New(10, 0))
end

--- 刷新选择材料列表
function UI_EquipmentDevelopView:RefreshSelectMaterialList()
    if self.selectMaterialList then
        for i = 1, #self.selectMaterialList do
            self.selectMaterialList[i]:Refresh()
        end
    end
end

function SelectMaterialItem:OnInit(transform)
    self.bagItem = BagItem.new()
    self.bagItem:Create(transform)
    self.bagItem:SetType(BagItemType.RisingStarMaterial)
    self.bagItem:SetScale(0.8, 0.8)
    SetActive(self.bagItem.go, true)
    self.select = GetChild(self.bagItem.go, "clothesInfo/select")
    self.selectBg = GetChild(self.bagItem.go, "selectBg")
    self.quality = GetChild(self.bagItem.go, "quality", UEUI.Image)
    self.icon = GetChild(self.bagItem.go, "icon", UEUI.Image)

    self.btnMask = GetChild(transform, "Mask", UEUI.Button)
    self.btnMask.onClick:AddListener(function ()
        local notEnoughList = {}
        table.insert(notEnoughList, {
            id = self.data.equipment.code,
            needNum = RisingStarTargetNum - RisingStarCurNum
        })
        UI_SHOW(UIDefine.UI_SlgGetWay, notEnoughList)
    end)
    SetUILastSibling(self.btnMask)
end

function SelectMaterialItem:UpdateData(data, index)
    if not data then return end
    self.data = data
    self.index = index

    if self.data.flag then
        local equipment = self.data.equipment
        local bagItemMoudle = EquipmentManager:ConvertToBagItemModule(equipment)
        bagItemMoudle.equipmentData = equipment
        self.bagItem:UpdateInfo(bagItemMoudle)
        SetActive(self.btnMask, true)
        SetActive(self.select, false)
        SetActive(self.selectBg, false)
        SetUIImageGray(self.quality, false)
        SetUIImageGray(self.icon, false)
        return
    end

    SetActive(self.btnMask, false)

    local bagItemMoudle = EquipmentManager:ConvertToBagItemModule(self.data)
    bagItemMoudle.equipmentData = self.data
    self.bagItem:UpdateInfo(bagItemMoudle)

    local curSelect = false
    for _, value in pairs(RisingStarMaterial) do
        if value == self.data.id then
            curSelect = true
            break
        end
    end
    SetActive(self.select, curSelect)
    SetActive(self.selectBg, false)

    if not curSelect and RisingStarCurNum >= RisingStarTargetNum then
        SetUIImageGray(self.quality, true)
        SetUIImageGray(self.icon, true)
    else
        SetUIImageGray(self.quality, false)
        SetUIImageGray(self.icon, false)
    end

    self.bagItem:SetClickFunc(function ()
        local isSelect = false
        local dataIndex
        for key, value in pairs(RisingStarMaterial) do
            if value == self.data.id then
                isSelect = true
                dataIndex = key
                break
            end
        end
        if isSelect and dataIndex then
            table.remove(RisingStarMaterial, dataIndex)
            SetActive(self.select, false)
            SetActive(self.selectBg, false)
        else
            if RisingStarProgress >= 1 then return end
            table.insert(RisingStarMaterial, self.data.id)
            SetActive(self.select, true)
            SetActive(self.selectBg, false)
        end
    end)
end

function SelectMaterialItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function SelectMaterialItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function SelectMaterialItem:Refresh()
    self:UpdateData(self.data, self.index)
end

--- 初始化选择材料列表
function UI_EquipmentDevelopView:InitReturnMaterialList()
    -- 初始化组件
    SetActive(self.ui.m_goReturnMaterialItem, false)
    self.returnMaterialSlider = SlideRect.new()
    self.returnMaterialSlider:Init(self.ui.m_scrollviewReturnMaterials, 1)
    self.returnMaterialList = {}
    for i = 1, ItemShowNums do
        self.returnMaterialList[i] = ReturnMaterialItem.new()
        self.returnMaterialList[i]:Init(UEGO.Instantiate(self.ui.m_goReturnMaterialItem.transform))
    end
    self.returnMaterialSlider:SetItems(self.returnMaterialList, 20, Vector2.New(10, 0))
end

--- 刷新返还材料列表
function UI_EquipmentDevelopView:RefreshReturnMaterialList()
    if IsTableEmpty(RisingStarMaterial) then
        RisingStarCurNum = 0
        self.returnMaterialSlider:SetData({})
        return
    end
    local data = {}
    RisingStarCurNum = 0
    self:ComputeRisingStarMaterial(data)

    table.sort(data, function (a, b)
        if a.id ~= b.id then
            return a.id > b.id
        elseif a.quality ~= b.quality then
            return a.quality > b.quality
        end
        return false
    end)

    self.returnMaterialSlider:SetData(data)

    local count = #self.returnMaterialSlider.datas
    SetUISize(self.ui.m_transReturnMaterials, 136 * count, 136)
end

--- 计算升星材料数量
--- @param data table 返还的材料
function UI_EquipmentDevelopView:ComputeRisingStarMaterial(data)
    for _, value in ipairs(RisingStarMaterial) do
        local equipment = EquipmentManager:FindEquipmentByID(value)
        if equipment then
            RisingStarCurNum = RisingStarCurNum + 1
            if equipment.level >= 1 then
                for i = equipment.level - 1, 0, -1 do
                    local curData = EquipmentManager:GetSingleLevelConfig(equipment.code, i)
                    local nextData = EquipmentManager:GetSingleLevelConfig(equipment.code, i + 1)
                    local promoteCost
                    if nextData.promote_level and nextData.promote_level <= equipment.promotion_lv then
                        promoteCost = nextData.promote_cost
                    end
                    self:ComputeReturnMaterialReward(data, curData.cost, promoteCost)
                end
            end

            if equipment.star >= 1 then
                for i = equipment.star - 1, 0, -1 do
                    local curData = EquipmentManager:GetSingleStarConfig(equipment.code, i)
                    self:ComputeReturnMaterialReward(data, curData.cost)
                end
            end
        end
    end
end

--- 计算升星装备返还材料奖励
--- @param rewardList table 奖励列表
--- @param rewardStr string 奖励字符串
function UI_EquipmentDevelopView:ComputeReturnMaterialReward(rewardList, rewardStr, promoteCost)
    local costID, costNum = self:GetRisingStarMaterialMax()
    local breakRewardList = string.split(rewardStr, ";")
    for _, reward in ipairs(breakRewardList) do
        local itemTable = string.split(reward, "|")
        local itemID = v2n(itemTable[1])
        local itemNum = v2n(itemTable[2])

        local typeUse = ItemConfig:GetTypeUse(itemID)
        if typeUse == ItemUseType.BagHeroClothes then
            for i = 1, itemNum, 1 do
                if RisingStarCurNum < costNum then
                    RisingStarCurNum = RisingStarCurNum + 1
                else
                    local bagItemMoudle = BagItemModule.new({
                        id = itemID,
                        num = itemNum
                    })
                    bagItemMoudle.equipmentData = EquipmentModule.new({
                        equip_id = "",
                        code = itemID,
                        amount = 1,
                        level = 0,
                        star = 0,
                        target_id = 0
                    })
                    table.insert(rewardList, bagItemMoudle)
                    RisingStarCurNum = RisingStarCurNum + 1
                end
            end
        else
            self:AddRewardList(rewardList, itemID, itemNum)
        end
    end

    if not IsNilOrEmpty(promoteCost) then
        local promoteList = string.split(promoteCost, ";")
        for _, reward in ipairs(promoteList) do
            local itemTable = string.split(reward, "|")
            local itemID = v2n(itemTable[1])
            local itemNum = v2n(itemTable[2])
            local typeUse = ItemConfig:GetTypeUse(itemID)
            if typeUse == ItemUseType.BagHeroClothes then
                for i = 1, itemNum, 1 do
                    local bagItemMoudle = BagItemModule.new({
                        id = itemID,
                        num = itemNum
                    })
                    bagItemMoudle.equipmentData = EquipmentModule.new({
                        equip_id = "",
                        code = itemID,
                        amount = 1,
                        level = 0,
                        star = 0,
                        target_id = 0
                    })
                    table.insert(rewardList, bagItemMoudle)
                end
            else
                self:AddRewardList(rewardList, itemID, itemNum)
            end
        end
    end
end

function ReturnMaterialItem:OnInit(transform)
    self.bagItem = BagItem.new()
    self.bagItem:Create(transform)
    self.bagItem:SetScale(0.77, 0.77)
    SetActive(self.bagItem.go, true)
end

function ReturnMaterialItem:UpdateData(data, index)
    if not data then return end
    self.data = data
    self.index = index
    self.bagItem:UpdateInfo(self.data)
end

function ReturnMaterialItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function ReturnMaterialItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function ReturnMaterialItem:Refresh()
    self:UpdateData(self.data, self.index)
end

--- 初始化分解获得列表
function UI_EquipmentDevelopView:InitResolveList()
    -- 初始化组件
    SetActive(self.ui.m_goResolveItem, false)
    self.resolveSlider = SlideRect.new()
    self.resolveSlider:Init(self.ui.m_scrollviewResolve, 2)
    self.resolveList = {}
    for i = 1, 6 do
        self.resolveList[i] = ResolveItem.new()
        self.resolveList[i]:Init(UEGO.Instantiate(self.ui.m_goResolveItem.transform))
    end
    self.resolveSlider:SetItems(self.resolveList, 12, Vector2.New(0, 64))
end

--- 刷新分解获得列表
function UI_EquipmentDevelopView:RefreshResolveList()
    if IsTableEmpty(ResolveEquipment) then
        self.resolveSlider:SetData({})
        return
    end
    local data = {}
    for _, value in ipairs(ResolveEquipment) do
        local equipment = EquipmentManager:FindEquipmentByID(value)
        if equipment then
            local breakReward = EquipmentManager:GetTableValue(equipment.code, "break")
            self:ComputeResolveReward(data, breakReward)
            if equipment.level >= 1 then
                for i = equipment.level - 1, 0, -1 do
                    local curData = EquipmentManager:GetSingleLevelConfig(equipment.code, i)
                    local nextData = EquipmentManager:GetSingleLevelConfig(equipment.code, i + 1)
                    local promoteCost
                    if nextData.promote_level and nextData.promote_level <= equipment.promotion_lv then
                        promoteCost = nextData.promote_cost
                    end
                    self:ComputeResolveReward(data, curData.cost, promoteCost)
                end
            end

            if equipment.star >= 1 then
                for i = equipment.star - 1, 0, -1 do
                    local curData = EquipmentManager:GetSingleStarConfig(equipment.code, i)
                    self:ComputeResolveReward(data, curData.cost)
                end
            end
        end
    end
    table.sort(data, function (a, b)
        if a.quality ~= b.quality then
            return a.quality > b.quality
        end
        return false
    end)
    local result = self:SplitDataByColumns(data, 7)
    self.resolveSlider:SetData(result)
end

--- 计算分解装备获得材料奖励
--- @param rewardList table 奖励列表
--- @param rewardStr string 奖励字符串
function UI_EquipmentDevelopView:ComputeResolveReward(rewardList, rewardStr, promoteCost)
    local breakRewardList = string.split(rewardStr, ";")
    for _, reward in ipairs(breakRewardList) do
        local itemTable = string.split(reward, "|")
        local itemID = v2n(itemTable[1])
        local itemNum = v2n(itemTable[2])

        local typeUse = ItemConfig:GetTypeUse(itemID)
        if typeUse == ItemUseType.BagHeroClothes then
            local config = EquipmentManager:GetEquipmentConfigByID(itemID)
            if config then
                local breakTable = string.split(config["break"], "|")
                local breakItemID = v2n(breakTable[1])
                local breakItemNum = v2n(breakTable[2]) * itemNum
                self:AddRewardList(rewardList, breakItemID, breakItemNum)
            end
        else
            self:AddRewardList(rewardList, itemID, itemNum)
        end
    end

    if not IsNilOrEmpty(promoteCost) then
        local promoteList = string.split(promoteCost, ";")
        for _, reward in ipairs(promoteList) do
            local itemTable = string.split(reward, "|")
            local itemID = v2n(itemTable[1])
            local itemNum = v2n(itemTable[2])
            local typeUse = ItemConfig:GetTypeUse(itemID)
            if typeUse == ItemUseType.BagHeroClothes then
                for i = 1, itemNum, 1 do
                    local bagItemMoudle = BagItemModule.new({
                        id = itemID,
                        num = itemNum
                    })
                    bagItemMoudle.equipmentData = EquipmentModule.new({
                        equip_id = "",
                        code = itemID,
                        amount = 1,
                        level = 0,
                        star = 0,
                        target_id = 0
                    })
                    table.insert(rewardList, bagItemMoudle)
                end
            else
                self:AddRewardList(rewardList, itemID, itemNum)
            end
        end
    end
end

--- 添加到奖励列表
--- @param rewardList table 奖励列表
--- @param itemID number 物品 ID
--- @param itemNum number 物品数量
function UI_EquipmentDevelopView:AddRewardList(rewardList, itemID, itemNum)
    local itemIndex
    for index, bagItem in ipairs(rewardList) do
        if bagItem.id == itemID then
            itemIndex = index
        end
    end
    if itemIndex then
        rewardList[itemIndex].num = rewardList[itemIndex].num + itemNum
    else
        local bagItemMoudle = BagItemModule.new({
            id = itemID,
            num = itemNum
        })
        table.insert(rewardList, bagItemMoudle)
    end
end

--- 显示分解材料特效
function UI_EquipmentDevelopView:ShowResolveEffect(isShow)
    if self.resolveList then
        for i = 1, #self.resolveList, 1 do
            self.resolveList[i]:ShowEffect(isShow)
        end
    end
end

function ResolveItem:OnInit(transform)
    self.group = GetChild(transform, "group")
    self.itemList = {}
    for i = 1, 6, 1 do
        local bagItem = BagItem.new()
        bagItem:Create(self.group)
        bagItem:SetScale(0.84, 0.84)
        SetActive(bagItem.go, true)
        table.insert(self.itemList, bagItem)
    end
end

function ResolveItem:UpdateData(data, index)
    if not data then return end
    self.data = data
    self.index = index
    for itemIndex, item in ipairs(self.itemList) do
        if self.data[itemIndex] then
            item:UpdateInfo(self.data[itemIndex])
            SetActive(item.go, true)
        else
            SetActive(item.go, false)
        end
    end
end

function ResolveItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function ResolveItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function ResolveItem:Refresh()
    self:UpdateData(self.data, self.index)
end

function ResolveItem:ShowEffect(isShow)
    for _, item in ipairs(self.itemList) do
        if isShow then
            CollectCardManager:SetMaskSize(item.effectBlue, ScrollviewResolve)
        end
        SetActive(item.effectBlue, isShow)
    end
end

--- 初始化回退获得列表
function UI_EquipmentDevelopView:InitFallbackList()
    -- 初始化组件
    SetActive(self.ui.m_goFallbackItem, false)
    self.fallbackSlider = SlideRect.new()
    self.fallbackSlider:Init(self.ui.m_scrollviewFallback, 2)
    self.fallbackList = {}
    for i = 1, 4 do
        self.fallbackList[i] = FallbackItem.new()
        self.fallbackList[i]:Init(UEGO.Instantiate(self.ui.m_goFallbackItem.transform))
    end
    self.fallbackSlider:SetItems(self.fallbackList, 4, Vector2.New(0, 0))

    local data = {}
    local result = self:SplitDataByColumns(data)
    self.fallbackSlider:SetData(result)
end

function UI_EquipmentDevelopView:InitFallbackList2()
    -- 初始化组件
    SetActive(self.ui.m_goFallbackItem2, false)
    self.fallbackSlider2 = SlideRect.new()
    self.fallbackSlider2:Init(self.ui.m_scrollviewFallback, 1)
    self.fallbackList2 = {}
    for i = 1, ItemShowNums do
        self.fallbackList2[i] = ReturnMaterialItem.new()
        self.fallbackList2[i]:Init(UEGO.Instantiate(self.ui.m_goFallbackItem2.transform))
    end
    self.fallbackSlider2:SetItems(self.fallbackList2, 20, Vector2.New(10, 0))
end

--- 刷新回退获得列表
function UI_EquipmentDevelopView:RefreshFallbackList()
    if IsTableEmpty(CurSelectEquip) then
        self.fallbackSlider2:SetData({})
        return
    end
    local data = {}
    local equipment = EquipmentManager:FindEquipmentByID(CurSelectEquip.data.id)
    if equipment then
        local bagItemMoudle = BagItemModule.new({
            id = CurSelectEquip.data.code,
            num = 1
        })
        bagItemMoudle.equipmentData = EquipmentModule.new({
            equip_id = "",
            code = CurSelectEquip.data.code,
            amount = 1,
            level = 0,
            star = 0,
            target_id = 0
        })
        table.insert(data, bagItemMoudle)
        if equipment.level >= 1 then
            for i = equipment.level - 1, 0, -1 do
                local curData = EquipmentManager:GetSingleLevelConfig(equipment.code, i)
                local nextData = EquipmentManager:GetSingleLevelConfig(equipment.code, i + 1)
                local promoteCost
                if nextData.promote_level and nextData.promote_level <= equipment.promotion_lv then
                    promoteCost = nextData.promote_cost
                end
                self:ComputeFallbackReward(data, curData.cost, promoteCost)
            end
        end

        if equipment.star >= 1 then
            for i = equipment.star - 1, 0, -1 do
                local curData = EquipmentManager:GetSingleStarConfig(equipment.code, i)
                self:ComputeFallbackReward(data, curData.cost)
            end
        end
    end
    table.sort(data, function (a, b)
        if a.id ~= b.id then
            return a.id > b.id
        elseif a.quality ~= b.quality then
            return a.quality > b.quality
        end
        return false
    end)
    -- local result = self:SplitDataByColumns(data, 7)
    self.fallbackSlider2:SetData(data)
    local count = #self.fallbackSlider2.datas
    SetUISize(self.ui.m_transFallback2, 135 * count, 136)
end

--- 计算回退装备获得材料奖励
--- @param rewardList table 奖励列表
--- @param rewardStr string 奖励字符串
function UI_EquipmentDevelopView:ComputeFallbackReward(rewardList, rewardStr, promoteCost)
    local breakRewardList = string.split(rewardStr, ";")
    for _, reward in ipairs(breakRewardList) do
        local itemTable = string.split(reward, "|")
        local itemID = v2n(itemTable[1])
        local itemNum = v2n(itemTable[2])

        local typeUse = ItemConfig:GetTypeUse(itemID)
        if typeUse == ItemUseType.BagHeroClothes then
            for i = 1, itemNum, 1 do
                local bagItemMoudle = BagItemModule.new({
                    id = itemID,
                    num = itemNum
                })
                bagItemMoudle.equipmentData = EquipmentModule.new({
                    equip_id = "",
                    code = itemID,
                    amount = 1,
                    level = 0,
                    star = 0,
                    target_id = 0
                })
                table.insert(rewardList, bagItemMoudle)
            end
        else
            self:AddRewardList(rewardList, itemID, itemNum)
        end
    end

    if not IsNilOrEmpty(promoteCost) then
        local promoteList = string.split(promoteCost, ";")
        for _, reward in ipairs(promoteList) do
            local itemTable = string.split(reward, "|")
            local itemID = v2n(itemTable[1])
            local itemNum = v2n(itemTable[2])
            local typeUse = ItemConfig:GetTypeUse(itemID)
            if typeUse == ItemUseType.BagHeroClothes then
                for i = 1, itemNum, 1 do
                    local bagItemMoudle = BagItemModule.new({
                        id = itemID,
                        num = itemNum
                    })
                    bagItemMoudle.equipmentData = EquipmentModule.new({
                        equip_id = "",
                        code = itemID,
                        amount = 1,
                        level = 0,
                        star = 0,
                        target_id = 0
                    })
                    table.insert(rewardList, bagItemMoudle)
                end
            else
                self:AddRewardList(rewardList, itemID, itemNum)
            end
        end
    end
end

--- 显示回退材料特效
function UI_EquipmentDevelopView:ShowFallbackEffect(isShow)
    if self.fallbackList then
        for i = 1, #self.fallbackList, 1 do
            self.fallbackList[i]:ShowEffect(isShow)
        end
    end
end

function FallbackItem:OnInit(transform)
    self.group = GetChild(transform, "group")
    self.itemList = {}
    for i = 1, 6, 1 do
        local bagItem = BagItem.new()
        bagItem:Create(self.group)
        bagItem:SetScale(0.78, 0.78)
        SetActive(bagItem.go, true)
        table.insert(self.itemList, bagItem)
    end
end

function FallbackItem:UpdateData(data, index)
    if not data then return end
    self.data = data
    self.index = index
    for itemIndex, item in ipairs(self.itemList) do
        if self.data[itemIndex] then
            item:UpdateInfo(self.data[itemIndex])
            SetActive(item.go, true)
        else
            SetActive(item.go, false)
        end
    end
end

function FallbackItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function FallbackItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function FallbackItem:Refresh()
    self:UpdateData(self.data, self.index)
end

function FallbackItem:ShowEffect(isShow)
    for _, item in ipairs(self.itemList) do
        if isShow then
            CollectCardManager:SetMaskSize(item.effectBlue, ScrollviewFallback)
        end
        SetActive(item.effectBlue, isShow)
    end
end

--endregion -------------------------------------- 材料 -----------------------------------------

return UI_EquipmentDevelopView