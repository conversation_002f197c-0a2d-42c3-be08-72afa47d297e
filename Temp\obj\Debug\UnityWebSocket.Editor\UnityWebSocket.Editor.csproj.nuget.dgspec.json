{"format": 1, "restore": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Editor.csproj": {}}, "projects": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj", "projectName": "UnityEditor.TestRunner", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\UnityEditor.TestRunner\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj", "projectName": "UnityEditor.UI", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\UnityEditor.UI\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj", "projectName": "UnityEngine.TestRunner", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\UnityEngine.TestRunner\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj", "projectName": "UnityEngine.UI", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\UnityEngine.UI\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Editor.csproj", "projectName": "UnityWebSocket.Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\UnityWebSocket.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj", "projectName": "UnityWebSocket.Runtime", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\UnityWebSocket.Runtime\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}}}