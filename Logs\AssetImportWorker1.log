Using pre-set license
Built from '2022.3/china_unity/release' branch; Version is '2022.3.62f1c1 (b0109b07edb8) revision 11538587'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'zh' Physical Memory: 32596 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/Project_Merge_Minigame/Client/Project_PT
-logFile
Logs/AssetImportWorker1.log
-srvPort
13446
Successfully changed project path to: D:/Project_Merge_Minigame/Client/Project_PT
D:/Project_Merge_Minigame/Client/Project_PT
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [273056]  Target information:

Player connection [273056]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3246998946 [EditorId] 3246998946 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-ORSLCSS) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [273056] Host joined multi-casting on [***********:54997]...
Player connection [273056] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
Refreshing native plugins compatible for Editor in 16.88 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.62f1c1 (b0109b07edb8)
[Subsystems] Discovering subsystems at path D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Project_Merge_Minigame/Client/Project_PT/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 (ID=0x2808)
    Vendor:   NVIDIA
    VRAM:     7957 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56632
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Register platform support module: D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002227 seconds.
- Loaded All Assemblies, in  0.178 seconds
Native extension for WindowsStandalone target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 590 ms
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.743 seconds
Domain Reload Profiling: 921ms
	BeginReloadAssembly (50ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (5ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (78ms)
		LoadAssemblies (50ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (76ms)
			TypeCache.Refresh (75ms)
				TypeCache.ScanAssembly (68ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (743ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (719ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (636ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1ms)
			ProcessInitializeOnLoadAttributes (57ms)
			ProcessInitializeOnLoadMethodAttributes (22ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.348 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.184 seconds
Domain Reload Profiling: 1532ms
	BeginReloadAssembly (74ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (13ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (225ms)
		LoadAssemblies (168ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (98ms)
			TypeCache.Refresh (86ms)
				TypeCache.ScanAssembly (74ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (1184ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1104ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (25ms)
			ProcessInitializeOnLoadAttributes (1035ms)
			ProcessInitializeOnLoadMethodAttributes (27ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (3ms)
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=632fcf66245364441b473efee6eebaa9): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 7.78 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4368 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1888 unused Assets / (6.3 MB). Loaded Objects now: 3740.
Memory consumption went from 0.84 GB to 0.84 GB.
Total: 17.783700 ms (FindLiveObjects: 0.253000 ms CreateObjectMapping: 0.072600 ms MarkObjects: 13.162100 ms  DeleteObjects: 4.294900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.260 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.205 seconds
Domain Reload Profiling: 1465ms
	BeginReloadAssembly (92ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (125ms)
		LoadAssemblies (158ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (10ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1205ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1043ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (32ms)
			ProcessInitializeOnLoadAttributes (975ms)
			ProcessInitializeOnLoadMethodAttributes (19ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=07b965b2ff81dac418a7e4f80f0f33d3): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 9.37 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Release of invalid GC handle. The handle is from a previous domain. The release operation is skipped.
Unloading 4068 Unused Serialized files (Serialized files now loaded: 0)
Release of invalid GC handle native callstack: 
0x00007ff7aef5ff2d (Unity) StackWalker::GetCurrentCallstack
0x00007ff7aef65009 (Unity) StackWalker::ShowCallstack
0x00007ff7aff23ec1 (Unity) GetStacktrace
0x00007ff7aee5b01d (Unity) ScriptingGCHandle::ReleaseAndClear
0x00007ff7af41c8bc (Unity) UploadHandlerRaw::`vector deleting destructor'
0x00007ff7af41d46d (Unity) UploadHandler::Release
0x00007ff7af419d0d (Unity) UnityWebRequestProto<UnityWebRequestTransport,AtomicRefCounter,RedirectHelper,ResponseHelper,DownloadHandler,UploadHandler,CertificateHandler,HeaderHelper,AsyncOperation>::~UnityWebRequestProto<UnityWebRequestTransport,AtomicRefCounter,RedirectHelper,ResponseHelper,DownloadHandler,UploadHandler,CertificateHandler,HeaderHelper,AsyncOperation>
0x00007ff7af41a006 (Unity) UnityWebRequest::`vector deleting destructor'
0x00007ff7ae112627 (Unity) UnityWebRequestProto<UnityWebRequestTransport,AtomicRefCounter,RedirectHelper,ResponseHelper,DownloadHandler,UploadHandler,CertificateHandler,HeaderHelper,AsyncOperation>::Release
0x00007ff7af41ba35 (Unity) UnityWebRequestAsyncOperation::InvokeCoroutine
0x00007ff7af41bc11 (Unity) UnityWebRequestProto<UnityWebRequestTransport,AtomicRefCounter,RedirectHelper,ResponseHelper,DownloadHandler,UploadHandler,CertificateHandler,HeaderHelper,AsyncOperation>::Job_InvokeCoroutine
0x00007ff7aea896ea (Unity) BackgroundJobQueue::ExecuteMainThreadJobs
0x00007ff7aeb3e43f (Unity) PreloadManager::WaitForAllAsyncOperationsToComplete
0x00007ff7b02c3996 (Unity) <lambda_a8cef15c13bd9ac3f7eff79c3cefd4ad>::operator()
0x00007ff7b02feba2 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff7b02eab4e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff7b02ec7b4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff7b02fcf4c (Unity) IOService::Run
0x00007ff7b02d038f (Unity) AssetImportWorkerClient::Run
0x00007ff7b029bb77 (Unity) RunAssetImportWorkerClientV2
0x00007ff7b029bbfb (Unity) RunAssetImporterV2
0x00007ff7afab35ee (Unity) Application::InitializeProject
0x00007ff7aff2eb75 (Unity) WinMain
0x00007ff7b12f214e (Unity) __scrt_common_main_seh
0x00007ffbfa89e8d7 (KERNEL32) BaseThreadInitThunk
0x00007ffbfcb1c34c (ntdll) RtlUserThreadStart
Unloading 1883 unused Assets / (6.3 MB). Loaded Objects now: 3743.
Memory consumption went from 0.78 GB to 0.78 GB.
Total: 17.392200 ms (FindLiveObjects: 0.312400 ms CreateObjectMapping: 0.094000 ms MarkObjects: 12.448100 ms  DeleteObjects: 4.536800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.385 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
System.IO.IOException: Sharing violation on path D:\Project_Merge_Minigame\Client\Project_PT\ProjectSettings\GvhProjectSettings.xml
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.IO.FileOptions options) [0x00000] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,int,System.IO.FileOptions)
  at System.IO.StreamReader..ctor (System.String path, System.Text.Encoding encoding, System.Boolean detectEncodingFromByteOrderMarks, System.Int32 bufferSize) [0x00055] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at System.IO.StreamReader..ctor (System.String path, System.Boolean detectEncodingFromByteOrderMarks) [0x00007] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at System.IO.StreamReader..ctor (System.String path) [0x00000] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at (wrapper remoting-invoke-with-check) System.IO.StreamReader..ctor(string)
  at Google.XmlUtilities.ParseXmlTextFileElements (System.String filename, Google.Logger logger, Google.XmlUtilities+ParseElement parseElement) [0x0000f] in /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/XmlUtilities.cs:104 
  at Google.ProjectSettings.Load () [0x00012] in /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:777 
  at Google.ProjectSettings.LoadIfEmpty () [0x0001b] in /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:558 
  at Google.ProjectSettings.GetBool (System.String name, System.Boolean defaultValue, Google.SettingsLocation location) [0x0002b] in /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:606 
  at Google.ProjectSettings.GetBool (System.String name, System.Boolean defaultValue) [0x00000] in /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:619 
  at Google.IOSResolver.get_VerboseLoggingEnabled () [0x00000] in /Users/<USER>/Workspace/git/unity-jar-resolver/source/IOSResolver/src/IOSResolver.cs:996 
  at Google.IOSResolver..cctor () [0x001ec] in /Users/<USER>/Workspace/git/unity-jar-resolver/source/IOSResolver/src/IOSResolver.cs:692 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.500 seconds
Domain Reload Profiling: 1884ms
	BeginReloadAssembly (88ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (253ms)
		LoadAssemblies (285ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (10ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (1500ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1355ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (25ms)
			ProcessInitializeOnLoadAttributes (1288ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=34dbd3429df86eb4bab39259af1eb15e): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 12.60 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4068 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1883 unused Assets / (6.3 MB). Loaded Objects now: 3746.
Memory consumption went from 0.78 GB to 0.78 GB.
Total: 24.518800 ms (FindLiveObjects: 0.416500 ms CreateObjectMapping: 0.211800 ms MarkObjects: 16.686800 ms  DeleteObjects: 7.202300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.850 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.313 seconds
Domain Reload Profiling: 9163ms
	BeginReloadAssembly (89ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (2720ms)
		LoadAssemblies (2751ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (10ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (6314ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2796ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (25ms)
			ProcessInitializeOnLoadAttributes (2739ms)
			ProcessInitializeOnLoadMethodAttributes (15ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (3ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=43c768991cfcc6d4ba18bae328e5dbb7): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 9.22 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4068 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1883 unused Assets / (6.3 MB). Loaded Objects now: 3749.
Memory consumption went from 0.78 GB to 0.78 GB.
Total: 16.602300 ms (FindLiveObjects: 0.218500 ms CreateObjectMapping: 0.069100 ms MarkObjects: 11.800700 ms  DeleteObjects: 4.513100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.251 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.226 seconds
Domain Reload Profiling: 1477ms
	BeginReloadAssembly (82ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (20ms)
	RebuildCommonClasses (17ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (125ms)
		LoadAssemblies (155ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (10ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (1226ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1079ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (24ms)
			ProcessInitializeOnLoadAttributes (1020ms)
			ProcessInitializeOnLoadMethodAttributes (19ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=697b42197570c284b831e4088237ca4b): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 9.48 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4068 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1883 unused Assets / (6.3 MB). Loaded Objects now: 3752.
Memory consumption went from 0.78 GB to 0.78 GB.
Total: 106.888800 ms (FindLiveObjects: 0.227500 ms CreateObjectMapping: 0.141700 ms MarkObjects: 12.313500 ms  DeleteObjects: 94.205100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
========================================================================
Received Import Request.
  Time since last request: 347811.622328 seconds.
  path: Assets/ResPackage/Sprite/ui_maincamp/activity_cup_1_m.png
  artifactKey: Guid(3462cad0c6328f54d94d7d112435cd81) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_maincamp/activity_cup_1_m.png using Guid(3462cad0c6328f54d94d7d112435cd81) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '06ae9b9b3f7d79442810992e517d815f') in 0.081819 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.136508 seconds.
  path: Assets/ResPackage/Sprite/ui_maincamp/activity_cup_3_m.png
  artifactKey: Guid(def07d704badca54fa627367b1640907) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_maincamp/activity_cup_3_m.png using Guid(def07d704badca54fa627367b1640907) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '93b128764be27adee7a8f3057d109880') in 0.012291 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.201803 seconds.
  path: Assets/ResPackage/Sprite/ui_maincamp/activity_cup_6_m.png
  artifactKey: Guid(b4d78e4c508392640b97889dd8b33dbe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_maincamp/activity_cup_6_m.png using Guid(b4d78e4c508392640b97889dd8b33dbe) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'fbe1c96a6cca9745643f4e376a3e6ce7') in 0.010996 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.183739 seconds.
  path: Assets/ResPackage/Sprite/ui_maincamp/arrow.png
  artifactKey: Guid(9fad82cd511219d4ba24d6511f0a8b66) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_maincamp/arrow.png using Guid(9fad82cd511219d4ba24d6511f0a8b66) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '377c95c6c74f830927164bbb074149c0') in 0.012417 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.182667 seconds.
  path: Assets/ResPackage/Sprite/ui_maincamp/balloon7.png
  artifactKey: Guid(2126c7710bd9e0b469eb995b088e7faa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_maincamp/balloon7.png using Guid(2126c7710bd9e0b469eb995b088e7faa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '4ebd354404290c5616c6842f1bda63df') in 0.012910 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.519508 seconds.
  path: Assets/ResPackage/Sprite/ui_huodongjingsai_baolingqiu/baolingqiu_paihangbang_di1.png
  artifactKey: Guid(2612f1a8fd5654a4f90715a2f304e3b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_huodongjingsai_baolingqiu/baolingqiu_paihangbang_di1.png using Guid(2612f1a8fd5654a4f90715a2f304e3b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '71ba1779c5c1ce7edae57297c1e74a78') in 0.013761 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/ResPackage/Sprite/ui_maincamp/balloon5.png
  artifactKey: Guid(822bfcd986a60c44cbf0dca37aa2e6c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_maincamp/balloon5.png using Guid(822bfcd986a60c44cbf0dca37aa2e6c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'df6435425bc616dbcc15d400280e6615') in 0.010888 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/ResPackage/Sprite/ui_huodongzhongxin/bg_add.png
  artifactKey: Guid(fa83db668e6ec3b41ba8181d356ad679) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_huodongzhongxin/bg_add.png using Guid(fa83db668e6ec3b41ba8181d356ad679) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '0380732cc8373d078a55c624237e12f8') in 0.010956 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/ResPackage/Sprite/ui_huodongjingsai_baolingqiu/baolingqiu_qieye2.png
  artifactKey: Guid(ae3c4512c81931d449bdf0a55bffc489) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_huodongjingsai_baolingqiu/baolingqiu_qieye2.png using Guid(ae3c4512c81931d449bdf0a55bffc489) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '6b6b839dad2a2ba16737fc486ec5fef8') in 0.010930 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/ResPackage/Sprite/ui_huodongjingsai_baolingqiu/baolingqiu_paihangbang_di3_1.png
  artifactKey: Guid(b2d15982f018f7349a19e4bd057ed388) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_huodongjingsai_baolingqiu/baolingqiu_paihangbang_di3_1.png using Guid(b2d15982f018f7349a19e4bd057ed388) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '60d42db817b41cedb14c9940abee37e4') in 0.009358 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/ResPackage/Sprite/ui_maincamp/balloon6.png
  artifactKey: Guid(35c5d266b30698840afe51457b4d93a8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_maincamp/balloon6.png using Guid(35c5d266b30698840afe51457b4d93a8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '7fa26521d9ee8260550fc3ccbf0a0510') in 0.008923 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/ResPackage/Sprite/ui_huodongjingsai_baolingqiu/baolingqiu_paihangbang_di3_2.png
  artifactKey: Guid(08b22f4ed00f2dc4794847b9c9b77c58) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_huodongjingsai_baolingqiu/baolingqiu_paihangbang_di3_2.png using Guid(08b22f4ed00f2dc4794847b9c9b77c58) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '019fea5d2080687b531e6c4577fe5d43') in 0.008763 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/ResPackage/Sprite/ui_huodongjingsai_baolingqiu/baolingqiu_paihangbang_di3.png
  artifactKey: Guid(ea0694c0a3a09d5468d7e3e6ed78302c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_huodongjingsai_baolingqiu/baolingqiu_paihangbang_di3.png using Guid(ea0694c0a3a09d5468d7e3e6ed78302c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '7f77f599a521b958c990be4c406ce264') in 0.013470 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/ResPackage/Sprite/ui_huodongjingsai_baolingqiu/baolingqiu_paihangbang_di2.png
  artifactKey: Guid(be1eb49c3d9c33b44aa9163ed4876f60) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_huodongjingsai_baolingqiu/baolingqiu_paihangbang_di2.png using Guid(be1eb49c3d9c33b44aa9163ed4876f60) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'cb46e987bf811e0288592daafac812f8') in 0.010570 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/ResPackage/Sprite/ui_huodongjingsai_baolingqiu/baolingqiu_kuang_biaoti.png
  artifactKey: Guid(4f21bafb62868d64d97ba12b1ae1d7f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_huodongjingsai_baolingqiu/baolingqiu_kuang_biaoti.png using Guid(4f21bafb62868d64d97ba12b1ae1d7f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'ff814ec3ccd146fa5b6f2d73a6483a2a') in 0.020268 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/ResPackage/Sprite/ui_huodongjingsai_baolingqiu/baolingqiu_qieye1.png
  artifactKey: Guid(f1341ee1a4be7d147a61a1d9955ae607) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_huodongjingsai_baolingqiu/baolingqiu_qieye1.png using Guid(f1341ee1a4be7d147a61a1d9955ae607) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '9a4be7a4d5be0b7ead04c84b2d987cc9') in 0.014330 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/ResPackage/Sprite/ui_maincamp/activity_cup_5_m.png
  artifactKey: Guid(c7649c3cff1b12549ba14c40a5640d3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_maincamp/activity_cup_5_m.png using Guid(c7649c3cff1b12549ba14c40a5640d3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'f4e76e90bd48ae3919e5f58de6cddf35') in 0.014091 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/ResPackage/Sprite/ui_huodongjingsai_baolingqiu/baolingqiu_guize_di.png
  artifactKey: Guid(1c60d1d0ff4b884439bf9ed65d7cf5ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_huodongjingsai_baolingqiu/baolingqiu_guize_di.png using Guid(1c60d1d0ff4b884439bf9ed65d7cf5ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '8f76827c431b152d6ba4e49714c3f8cb') in 0.015575 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/ResPackage/Sprite/ui_maincamp/balloon3.png
  artifactKey: Guid(0de51ff18fb327744ad5a4409498d925) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_maincamp/balloon3.png using Guid(0de51ff18fb327744ad5a4409498d925) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '6f842d063625da4556433bda071452bd') in 0.013375 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/ResPackage/Sprite/ui_lianmeng/biangji_button.png
  artifactKey: Guid(de7e15ee70135d54ba7e0cfd1c4a0595) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_lianmeng/biangji_button.png using Guid(de7e15ee70135d54ba7e0cfd1c4a0595) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'f86c24548ed3bd748e7805cec456917a') in 0.014282 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.130051 seconds.
  path: Assets/ResPackage/Sprite/ui_slg_zhandou_pve/chuzhan_icon_gou.png
  artifactKey: Guid(220142b0d815ef142ac186cb278b39d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_slg_zhandou_pve/chuzhan_icon_gou.png using Guid(220142b0d815ef142ac186cb278b39d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'ac76ebc59efd0a604f6e5662b286e07d') in 0.014210 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/ResPackage/Sprite/ui_mainface/icon_rw_shangdiangoumai.png
  artifactKey: Guid(304e4134f88bd6d4abb31bc8bc585746) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_mainface/icon_rw_shangdiangoumai.png using Guid(304e4134f88bd6d4abb31bc8bc585746) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '76c97b216d93d0478531bd8f6e6dd487') in 0.012596 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/ResPackage/Sprite/ui_tinygame/icon_dagou.png
  artifactKey: Guid(d8b76f3983f6266439f4a4b03b81cce1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_tinygame/icon_dagou.png using Guid(d8b76f3983f6266439f4a4b03b81cce1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '109afab97273892a5703a0cdc40911d2') in 0.015018 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_yeudusaiji2/yuedu_win_goumaibg.png
  artifactKey: Guid(cb47f32787ed98641937574d42a42622) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_yeudusaiji2/yuedu_win_goumaibg.png using Guid(cb47f32787ed98641937574d42a42622) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'a0abdcf1b88439083cca5acabca9334c') in 0.011860 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/yaoshizhigou_win1.png
  artifactKey: Guid(9e7754022834adb4a9b479f3a2691b75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/yaoshizhigou_win1.png using Guid(9e7754022834adb4a9b479f3a2691b75) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'aa910352f703e7037a6b77bf64580c53') in 0.020264 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_meiguihuayuan/RoseGarden_jifengoumai_tubiaobox.png
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_meiguihuayuan/RoseGarden_jifengoumai_tubiaobox.png using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '5bfd72cb0bd3b15574f147b2f77df898') in 0.018957 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_yeudusaiji2/yuedu_win3_goumai3.png
  artifactKey: Guid(e373358ae76550c47a816db198b99bc6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_yeudusaiji2/yuedu_win3_goumai3.png using Guid(e373358ae76550c47a816db198b99bc6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '5b268a6a598319452d40e0c6c12fb124') in 0.012428 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_meiguihuayuan/RoseGarden_jifengoumai_win_colse.png
  artifactKey: Guid(a34a9675f3e50324cad7610e2e5ac238) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_meiguihuayuan/RoseGarden_jifengoumai_win_colse.png using Guid(a34a9675f3e50324cad7610e2e5ac238) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '81ba7e15f791f58a206f191f8a292503') in 0.013233 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/ResPackage/Sprite/ui_slg_zhengbasai/zhengbasai_danlun_tanchuang_danlun_gouxuan1.png
  artifactKey: Guid(b20bc6802c50181408f88c6869269678) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_slg_zhengbasai/zhengbasai_danlun_tanchuang_danlun_gouxuan1.png using Guid(b20bc6802c50181408f88c6869269678) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '757a5eb964cd59399ac4b2054f77cb43') in 0.012161 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/ResPackage/Sprite/ui_slg_jingjisai/jingji_goumai_anniu_bg.png
  artifactKey: Guid(705ffd13fef572443a89823c4d212d3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_slg_jingjisai/jingji_goumai_anniu_bg.png using Guid(705ffd13fef572443a89823c4d212d3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '4c601688dc4f10c944f7138b8630252c') in 0.011470 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/ResPackage/Sprite/ui_lianmeng/duiyuanquanxian_dagou.png
  artifactKey: Guid(fa77f86211798484585f3866d982b69c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_lianmeng/duiyuanquanxian_dagou.png using Guid(fa77f86211798484585f3866d982b69c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'de7d4cc3a5a0e83884e24a4c0849423d') in 0.010524 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_yeudusaiji2/yuedu_win3_goumai1.png
  artifactKey: Guid(17acd974f9d718e40a176c3311efea4b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_yeudusaiji2/yuedu_win3_goumai1.png using Guid(17acd974f9d718e40a176c3311efea4b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '0345093f8183dedb3dfd6337f4641c80') in 0.011421 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_shangoulibao/shangoulibao_biaoti.png
  artifactKey: Guid(4da1984f5a1152348aad9a8811af04ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_shangoulibao/shangoulibao_biaoti.png using Guid(4da1984f5a1152348aad9a8811af04ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'd6bd843dbe7f837bfc16a24d3aff9f7d') in 0.012187 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/ResPackage/Sprite/ui_maincamp/zhigoudikuai_winclose.png
  artifactKey: Guid(612f92a1a5302844889df9fe8485a89b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_maincamp/zhigoudikuai_winclose.png using Guid(612f92a1a5302844889df9fe8485a89b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '5281bd0df5ada88498c968b5506a487a') in 0.010869 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_1V1duijue/txz_gouxuan.png
  artifactKey: Guid(354d7bbf98a5cc844b0a115637a87572) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_1V1duijue/txz_gouxuan.png using Guid(354d7bbf98a5cc844b0a115637a87572) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'fe9e8d9a44be54476d26998381cddb4b') in 0.012162 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_shangoulibao/shangoulibao_jiantou.png
  artifactKey: Guid(1f5659a8de717ea43aae432c4774ca4e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_shangoulibao/shangoulibao_jiantou.png using Guid(1f5659a8de717ea43aae432c4774ca4e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '6f6a1d6b31bba1b87f88b96733127c36') in 0.011235 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/yaoshizhigou_icon_yaoshi.png
  artifactKey: Guid(cf488ee2a8df36042b090a91209a0cdf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/yaoshizhigou_icon_yaoshi.png using Guid(cf488ee2a8df36042b090a91209a0cdf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'b0d7d5a181ef331cec14cec55bb1dd04') in 0.010605 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/ResPackage/Sprite/ui_zhigoudikuai/zhigoudikuai_dongwuyuan_win.png
  artifactKey: Guid(fb603c4db3275c44eaefcb355443bc3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_zhigoudikuai/zhigoudikuai_dongwuyuan_win.png using Guid(fb603c4db3275c44eaefcb355443bc3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '8d67f3ef9e070ac186d9137960a75578') in 0.014734 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_shangoulibao/shangoulibao_windows1.png
  artifactKey: Guid(d918f4f962e4b4b42a4397ffee9b0b39) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_shangoulibao/shangoulibao_windows1.png using Guid(d918f4f962e4b4b42a4397ffee9b0b39) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'ef45197e0942b6f4cdac02444d284c2c') in 0.012314 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_yeudusaiji2/yuedu_win3_goumai4.png
  artifactKey: Guid(ce805f65c12ac67409f284e4cbca1de0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_yeudusaiji2/yuedu_win3_goumai4.png using Guid(ce805f65c12ac67409f284e4cbca1de0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '2f1da29524eed06527325dabe5b20c6c') in 0.011648 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_meiguihuayuan/RoseGarden_jifengoumai_win.png
  artifactKey: Guid(b13b3367cc26d1942a2d4b54a1f465da) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_meiguihuayuan/RoseGarden_jifengoumai_win.png using Guid(b13b3367cc26d1942a2d4b54a1f465da) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '52c8b9f2d71755d43de9d401cec87421') in 0.012151 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_shangoulibao/shangoulibao_jia.png
  artifactKey: Guid(7e275f7ed5b22a04cb18553901892597) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_shangoulibao/shangoulibao_jia.png using Guid(7e275f7ed5b22a04cb18553901892597) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '7aa1011dd5fee6f9f9714993d6292c16') in 0.012560 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/ResPackage/Sprite/ui_zhigoudikuai/zhigoudikuai_zhuyingdi_huaxian.png
  artifactKey: Guid(90dabf2e549bdc449a999a98e86be4a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_zhigoudikuai/zhigoudikuai_zhuyingdi_huaxian.png using Guid(90dabf2e549bdc449a999a98e86be4a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '5c03de2027a46aac7dac7d33c5ee8eb6') in 0.012160 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/ResPackage/Texture/UI/ui_maincamp/zhigoudikuai_win.png
  artifactKey: Guid(a2a3d18823a34074192cc37b5a37bc76) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Texture/UI/ui_maincamp/zhigoudikuai_win.png using Guid(a2a3d18823a34074192cc37b5a37bc76) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '40197fb2f3a67ba0018adb56945703cb') in 0.012557 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_shangoulibao/shangoulibao_windows2.png
  artifactKey: Guid(f4523ac11e09ae048b4da44b058c2928) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_shangoulibao/shangoulibao_windows2.png using Guid(f4523ac11e09ae048b4da44b058c2928) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '556fc8c825306013033c86309510e749') in 0.012367 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/yaoshizhigou_biaoqian.png
  artifactKey: Guid(eba4c1eafe9eafc4388bb73eccf81d36) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/yaoshizhigou_biaoqian.png using Guid(eba4c1eafe9eafc4388bb73eccf81d36) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '0d08c1df26a70af31abcf5fadaea3bbe') in 0.012213 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/ResPackage/Sprite/ui_activity/yaoshizhigou_win3.png
  artifactKey: Guid(38cf669d72414a2409d9253bf3d4192d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity/yaoshizhigou_win3.png using Guid(38cf669d72414a2409d9253bf3d4192d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'cb71b651564893aa8687dda58d5fe902') in 0.011095 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/ResPackage/Texture/UI/ui_zoo/zhigoudikuai_image4.png
  artifactKey: Guid(b7a6d6defe7a2d34b9b97f2bf43416c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Texture/UI/ui_zoo/zhigoudikuai_image4.png using Guid(b7a6d6defe7a2d34b9b97f2bf43416c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'd7cf78aee834d91edc77c0b840ac8243') in 0.015226 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/ResPackage/Sprite/ui_zhigoudikuai/zhigoudikuai_tongyong_dazhe.png
  artifactKey: Guid(7a24db790be91b247a0c12578b9c13da) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_zhigoudikuai/zhigoudikuai_tongyong_dazhe.png using Guid(7a24db790be91b247a0c12578b9c13da) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '0ab1cbe2188a64486823c5dbe062e764') in 0.012181 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
TcpMessagingSession - receive error
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0