{"version": 3, "targets": {".NETStandard,Version=v2.1": {"AppleAuth/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/AppleAuth.dll": {}}, "runtime": {"bin/placeholder/AppleAuth.dll": {}}}, "AppleAuth.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AppleAuth": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/AppleAuth.Editor.dll": {}}, "runtime": {"bin/placeholder/AppleAuth.Editor.dll": {}}}, "CasualGame.Dreamteck.Splines/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"CasualGame.Dreamteck.Utilities": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/CasualGame.Dreamteck.Splines.dll": {}}, "runtime": {"bin/placeholder/CasualGame.Dreamteck.Splines.dll": {}}}, "CasualGame.Dreamteck.Splines.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"CasualGame.Dreamteck.Splines": "1.0.0", "CasualGame.Dreamteck.Utilities": "1.0.0", "CasualGame.Dreamteck.Utilities.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/CasualGame.Dreamteck.Splines.Editor.dll": {}}, "runtime": {"bin/placeholder/CasualGame.Dreamteck.Splines.Editor.dll": {}}}, "CasualGame.Dreamteck.Utilities/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/CasualGame.Dreamteck.Utilities.dll": {}}, "runtime": {"bin/placeholder/CasualGame.Dreamteck.Utilities.dll": {}}}, "CasualGame.Dreamteck.Utilities.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/CasualGame.Dreamteck.Utilities.Editor.dll": {}}, "runtime": {"bin/placeholder/CasualGame.Dreamteck.Utilities.Editor.dll": {}}}, "FancyScrollView/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/FancyScrollView.dll": {}}, "runtime": {"bin/placeholder/FancyScrollView.dll": {}}}, "FancyScrollView.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"FancyScrollView": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/FancyScrollView.Editor.dll": {}}, "runtime": {"bin/placeholder/FancyScrollView.Editor.dll": {}}}, "IngameDebugConsole.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"IngameDebugConsole.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/IngameDebugConsole.Editor.dll": {}}, "runtime": {"bin/placeholder/IngameDebugConsole.Editor.dll": {}}}, "IngameDebugConsole.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/IngameDebugConsole.Runtime.dll": {}}, "runtime": {"bin/placeholder/IngameDebugConsole.Runtime.dll": {}}}, "Lofelt.NiceVibrations/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Lofelt.NiceVibrations.dll": {}}, "runtime": {"bin/placeholder/Lofelt.NiceVibrations.dll": {}}}, "Lofelt.NiceVibrations.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Lofelt.NiceVibrations": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Lofelt.NiceVibrations.Editor.dll": {}}, "runtime": {"bin/placeholder/Lofelt.NiceVibrations.Editor.dll": {}}}, "Sirenix.OdinInspector.CompatibilityLayer/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Sirenix.OdinInspector.CompatibilityLayer.dll": {}}, "runtime": {"bin/placeholder/Sirenix.OdinInspector.CompatibilityLayer.dll": {}}}, "Sirenix.OdinInspector.CompatibilityLayer.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Sirenix.OdinInspector.CompatibilityLayer": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll": {}}, "runtime": {"bin/placeholder/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll": {}}}, "Sirenix.OdinInspector.Modules.UnityMathematics/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Mathematics": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Sirenix.OdinInspector.Modules.UnityMathematics.dll": {}}, "runtime": {"bin/placeholder/Sirenix.OdinInspector.Modules.UnityMathematics.dll": {}}}, "UIEffect/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UIEffect.dll": {}}, "runtime": {"bin/placeholder/UIEffect.dll": {}}}, "Unity.2D.Sprite.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Sprite.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Sprite.Editor.dll": {}}}, "Unity.AI.Navigation/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.dll": {}}}, "Unity.AI.Navigation.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AI.Navigation": "1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.Editor.dll": {}}}, "Unity.AI.Navigation.Editor.ConversionSystem/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.Editor.ConversionSystem.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.Editor.ConversionSystem.dll": {}}}, "Unity.AI.Navigation.Updater/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AI.Navigation": "1.0.0", "Unity.AI.Navigation.Editor": "1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.Updater.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.Updater.dll": {}}}, "Unity.Mathematics/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.dll": {}}}, "Unity.Mathematics.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Mathematics": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.Editor.dll": {}}}, "Unity.PlasticSCM.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.PlasticSCM.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.PlasticSCM.Editor.dll": {}}}, "Unity.Rider.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rider.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Rider.Editor.dll": {}}}, "Unity.TextMeshPro/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TextMeshPro.dll": {}}, "runtime": {"bin/placeholder/Unity.TextMeshPro.dll": {}}}, "Unity.TextMeshPro.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.TextMeshPro": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TextMeshPro.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.TextMeshPro.Editor.dll": {}}}, "Unity.Timeline/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Timeline.dll": {}}, "runtime": {"bin/placeholder/Unity.Timeline.dll": {}}}, "Unity.Timeline.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Timeline": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Timeline.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Timeline.Editor.dll": {}}}, "Unity.VisualStudio.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualStudio.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualStudio.Editor.dll": {}}}, "Unity.VSCode.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VSCode.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VSCode.Editor.dll": {}}}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEngine.TestRunner": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}}, "UnityEditor.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.UI.dll": {}}}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}}, "UnityEngine.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.UI.dll": {}}}, "UnityWebSocket.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "UnityWebSocket.Runtime": "1.0.0"}, "compile": {"bin/placeholder/UnityWebSocket.Editor.dll": {}}, "runtime": {"bin/placeholder/UnityWebSocket.Editor.dll": {}}}, "UnityWebSocket.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityWebSocket.Runtime.dll": {}}, "runtime": {"bin/placeholder/UnityWebSocket.Runtime.dll": {}}}, "Wx/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Wx.dll": {}}, "runtime": {"bin/placeholder/Wx.dll": {}}}, "WxEditor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "Wx": "1.0.0"}, "compile": {"bin/placeholder/WxEditor.dll": {}}, "runtime": {"bin/placeholder/WxEditor.dll": {}}}}}, "libraries": {"AppleAuth/1.0.0": {"type": "project", "path": "AppleAuth.csproj", "msbuildProject": "AppleAuth.csproj"}, "AppleAuth.Editor/1.0.0": {"type": "project", "path": "AppleAuth.Editor.csproj", "msbuildProject": "AppleAuth.Editor.csproj"}, "CasualGame.Dreamteck.Splines/1.0.0": {"type": "project", "path": "CasualGame.Dreamteck.Splines.csproj", "msbuildProject": "CasualGame.Dreamteck.Splines.csproj"}, "CasualGame.Dreamteck.Splines.Editor/1.0.0": {"type": "project", "path": "CasualGame.Dreamteck.Splines.Editor.csproj", "msbuildProject": "CasualGame.Dreamteck.Splines.Editor.csproj"}, "CasualGame.Dreamteck.Utilities/1.0.0": {"type": "project", "path": "CasualGame.Dreamteck.Utilities.csproj", "msbuildProject": "CasualGame.Dreamteck.Utilities.csproj"}, "CasualGame.Dreamteck.Utilities.Editor/1.0.0": {"type": "project", "path": "CasualGame.Dreamteck.Utilities.Editor.csproj", "msbuildProject": "CasualGame.Dreamteck.Utilities.Editor.csproj"}, "FancyScrollView/1.0.0": {"type": "project", "path": "FancyScrollView.csproj", "msbuildProject": "FancyScrollView.csproj"}, "FancyScrollView.Editor/1.0.0": {"type": "project", "path": "FancyScrollView.Editor.csproj", "msbuildProject": "FancyScrollView.Editor.csproj"}, "IngameDebugConsole.Editor/1.0.0": {"type": "project", "path": "IngameDebugConsole.Editor.csproj", "msbuildProject": "IngameDebugConsole.Editor.csproj"}, "IngameDebugConsole.Runtime/1.0.0": {"type": "project", "path": "IngameDebugConsole.Runtime.csproj", "msbuildProject": "IngameDebugConsole.Runtime.csproj"}, "Lofelt.NiceVibrations/1.0.0": {"type": "project", "path": "Lofelt.NiceVibrations.csproj", "msbuildProject": "Lofelt.NiceVibrations.csproj"}, "Lofelt.NiceVibrations.Editor/1.0.0": {"type": "project", "path": "Lofelt.NiceVibrations.Editor.csproj", "msbuildProject": "Lofelt.NiceVibrations.Editor.csproj"}, "Sirenix.OdinInspector.CompatibilityLayer/1.0.0": {"type": "project", "path": "Sirenix.OdinInspector.CompatibilityLayer.csproj", "msbuildProject": "Sirenix.OdinInspector.CompatibilityLayer.csproj"}, "Sirenix.OdinInspector.CompatibilityLayer.Editor/1.0.0": {"type": "project", "path": "Sirenix.OdinInspector.CompatibilityLayer.Editor.csproj", "msbuildProject": "Sirenix.OdinInspector.CompatibilityLayer.Editor.csproj"}, "Sirenix.OdinInspector.Modules.UnityMathematics/1.0.0": {"type": "project", "path": "Sirenix.OdinInspector.Modules.UnityMathematics.csproj", "msbuildProject": "Sirenix.OdinInspector.Modules.UnityMathematics.csproj"}, "UIEffect/1.0.0": {"type": "project", "path": "UIEffect.csproj", "msbuildProject": "UIEffect.csproj"}, "Unity.2D.Sprite.Editor/1.0.0": {"type": "project", "path": "Unity.2D.Sprite.Editor.csproj", "msbuildProject": "Unity.2D.Sprite.Editor.csproj"}, "Unity.AI.Navigation/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.csproj", "msbuildProject": "Unity.AI.Navigation.csproj"}, "Unity.AI.Navigation.Editor/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.Editor.csproj", "msbuildProject": "Unity.AI.Navigation.Editor.csproj"}, "Unity.AI.Navigation.Editor.ConversionSystem/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.Editor.ConversionSystem.csproj", "msbuildProject": "Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "Unity.AI.Navigation.Updater/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.Updater.csproj", "msbuildProject": "Unity.AI.Navigation.Updater.csproj"}, "Unity.Mathematics/1.0.0": {"type": "project", "path": "Unity.Mathematics.csproj", "msbuildProject": "Unity.Mathematics.csproj"}, "Unity.Mathematics.Editor/1.0.0": {"type": "project", "path": "Unity.Mathematics.Editor.csproj", "msbuildProject": "Unity.Mathematics.Editor.csproj"}, "Unity.PlasticSCM.Editor/1.0.0": {"type": "project", "path": "Unity.PlasticSCM.Editor.csproj", "msbuildProject": "Unity.PlasticSCM.Editor.csproj"}, "Unity.Rider.Editor/1.0.0": {"type": "project", "path": "Unity.Rider.Editor.csproj", "msbuildProject": "Unity.Rider.Editor.csproj"}, "Unity.TextMeshPro/1.0.0": {"type": "project", "path": "Unity.TextMeshPro.csproj", "msbuildProject": "Unity.TextMeshPro.csproj"}, "Unity.TextMeshPro.Editor/1.0.0": {"type": "project", "path": "Unity.TextMeshPro.Editor.csproj", "msbuildProject": "Unity.TextMeshPro.Editor.csproj"}, "Unity.Timeline/1.0.0": {"type": "project", "path": "Unity.Timeline.csproj", "msbuildProject": "Unity.Timeline.csproj"}, "Unity.Timeline.Editor/1.0.0": {"type": "project", "path": "Unity.Timeline.Editor.csproj", "msbuildProject": "Unity.Timeline.Editor.csproj"}, "Unity.VisualStudio.Editor/1.0.0": {"type": "project", "path": "Unity.VisualStudio.Editor.csproj", "msbuildProject": "Unity.VisualStudio.Editor.csproj"}, "Unity.VSCode.Editor/1.0.0": {"type": "project", "path": "Unity.VSCode.Editor.csproj", "msbuildProject": "Unity.VSCode.Editor.csproj"}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "path": "UnityEditor.TestRunner.csproj", "msbuildProject": "UnityEditor.TestRunner.csproj"}, "UnityEditor.UI/1.0.0": {"type": "project", "path": "UnityEditor.UI.csproj", "msbuildProject": "UnityEditor.UI.csproj"}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "path": "UnityEngine.TestRunner.csproj", "msbuildProject": "UnityEngine.TestRunner.csproj"}, "UnityEngine.UI/1.0.0": {"type": "project", "path": "UnityEngine.UI.csproj", "msbuildProject": "UnityEngine.UI.csproj"}, "UnityWebSocket.Editor/1.0.0": {"type": "project", "path": "UnityWebSocket.Editor.csproj", "msbuildProject": "UnityWebSocket.Editor.csproj"}, "UnityWebSocket.Runtime/1.0.0": {"type": "project", "path": "UnityWebSocket.Runtime.csproj", "msbuildProject": "UnityWebSocket.Runtime.csproj"}, "Wx/1.0.0": {"type": "project", "path": "Wx.csproj", "msbuildProject": "Wx.csproj"}, "WxEditor/1.0.0": {"type": "project", "path": "WxEditor.csproj", "msbuildProject": "WxEditor.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["AppleAuth >= 1.0.0", "AppleAuth.Editor >= 1.0.0", "CasualGame.Dreamteck.Splines >= 1.0.0", "CasualGame.Dreamteck.Splines.Editor >= 1.0.0", "CasualGame.Dreamteck.Utilities >= 1.0.0", "CasualGame.Dreamteck.Utilities.Editor >= 1.0.0", "FancyScrollView >= 1.0.0", "FancyScrollView.Editor >= 1.0.0", "IngameDebugConsole.Editor >= 1.0.0", "IngameDebugConsole.Runtime >= 1.0.0", "Lofelt.NiceVibrations >= 1.0.0", "Lofelt.NiceVibrations.Editor >= 1.0.0", "Sirenix.OdinInspector.CompatibilityLayer >= 1.0.0", "Sirenix.OdinInspector.CompatibilityLayer.Editor >= 1.0.0", "Sirenix.OdinInspector.Modules.UnityMathematics >= 1.0.0", "UIEffect >= 1.0.0", "Unity.2D.Sprite.Editor >= 1.0.0", "Unity.AI.Navigation >= 1.0.0", "Unity.AI.Navigation.Editor >= 1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem >= 1.0.0", "Unity.AI.Navigation.Updater >= 1.0.0", "Unity.Mathematics >= 1.0.0", "Unity.Mathematics.Editor >= 1.0.0", "Unity.PlasticSCM.Editor >= 1.0.0", "Unity.Rider.Editor >= 1.0.0", "Unity.TextMeshPro >= 1.0.0", "Unity.TextMeshPro.Editor >= 1.0.0", "Unity.Timeline >= 1.0.0", "Unity.Timeline.Editor >= 1.0.0", "Unity.VSCode.Editor >= 1.0.0", "Unity.VisualStudio.Editor >= 1.0.0", "UnityEditor.UI >= 1.0.0", "UnityEngine.UI >= 1.0.0", "UnityWebSocket.Editor >= 1.0.0", "UnityWebSocket.Runtime >= 1.0.0", "Wx >= 1.0.0", "WxEditor >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-firstpass.csproj", "projectName": "Assembly-CSharp-firstpass", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-firstpass.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Assembly-CSharp-firstpass\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Runtime.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Runtime.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UIEffect.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UIEffect.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.2D.Sprite.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.2D.Sprite.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Updater.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Updater.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.PlasticSCM.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Rider.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Rider.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VisualStudio.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VisualStudio.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VSCode.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VSCode.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Wx.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Wx.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\WxEditor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\WxEditor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}}