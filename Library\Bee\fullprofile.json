{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 82792, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 82792, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 82792, "tid": 16, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 82792, "tid": 16, "ts": 1757033363703807, "dur": 339, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 82792, "tid": 16, "ts": 1757033363706426, "dur": 565, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 82792, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361106570, "dur": 15857, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361122428, "dur": 2574530, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361122438, "dur": 14, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361122455, "dur": 28821, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361151285, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361151288, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361151315, "dur": 5, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361151321, "dur": 1252, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152579, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152581, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152619, "dur": 1, "ph": "X", "name": "ProcessMessages 1444", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152621, "dur": 21, "ph": "X", "name": "ReadAsync 1444", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152645, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152647, "dur": 23, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152672, "dur": 1, "ph": "X", "name": "ProcessMessages 1164", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152674, "dur": 20, "ph": "X", "name": "ReadAsync 1164", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152697, "dur": 20, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152719, "dur": 13, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152733, "dur": 31, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152767, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152784, "dur": 14, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152800, "dur": 14, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152816, "dur": 15, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152833, "dur": 14, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152849, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152851, "dur": 23, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152874, "dur": 2, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152877, "dur": 15, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152894, "dur": 91, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152987, "dur": 1, "ph": "X", "name": "ProcessMessages 2281", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361152988, "dur": 11, "ph": "X", "name": "ReadAsync 2281", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153001, "dur": 12, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153015, "dur": 20, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153037, "dur": 21, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153061, "dur": 28, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153092, "dur": 16, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153109, "dur": 13, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153126, "dur": 11, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153139, "dur": 10, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153151, "dur": 10, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153163, "dur": 10, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153175, "dur": 10, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153187, "dur": 11, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153199, "dur": 12, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153213, "dur": 12, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153226, "dur": 16, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153245, "dur": 12, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153258, "dur": 11, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153271, "dur": 12, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153284, "dur": 11, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153297, "dur": 26, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153325, "dur": 70, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153396, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153417, "dur": 1, "ph": "X", "name": "ProcessMessages 2215", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153419, "dur": 16, "ph": "X", "name": "ReadAsync 2215", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153437, "dur": 22, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153460, "dur": 15, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153479, "dur": 12, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153493, "dur": 11, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153505, "dur": 11, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153518, "dur": 11, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153531, "dur": 13, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153545, "dur": 9, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153556, "dur": 14, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153572, "dur": 10, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153584, "dur": 13, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153599, "dur": 17, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153617, "dur": 13, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153631, "dur": 11, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153644, "dur": 10, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153655, "dur": 34, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153691, "dur": 35, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153728, "dur": 10, "ph": "X", "name": "ReadAsync 1362", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153739, "dur": 11, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153751, "dur": 10, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153763, "dur": 10, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153774, "dur": 9, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153784, "dur": 9, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153795, "dur": 16, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153812, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153823, "dur": 9, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153834, "dur": 12, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153848, "dur": 11, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153860, "dur": 13, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153875, "dur": 11, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153887, "dur": 10, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153899, "dur": 12, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153912, "dur": 15, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153929, "dur": 15, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153945, "dur": 25, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153972, "dur": 14, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361153987, "dur": 12, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154000, "dur": 9, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154011, "dur": 27, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154040, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154051, "dur": 11, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154063, "dur": 10, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154076, "dur": 10, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154088, "dur": 34, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154124, "dur": 10, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154135, "dur": 10, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154146, "dur": 8, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154155, "dur": 10, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154167, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154178, "dur": 11, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154191, "dur": 10, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154202, "dur": 10, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154214, "dur": 10, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154226, "dur": 10, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154237, "dur": 43, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154282, "dur": 11, "ph": "X", "name": "ReadAsync 1665", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154295, "dur": 9, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154306, "dur": 10, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154317, "dur": 19, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154337, "dur": 12, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154351, "dur": 11, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154364, "dur": 25, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154390, "dur": 11, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154403, "dur": 9, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154413, "dur": 10, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154424, "dur": 11, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154436, "dur": 12, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154450, "dur": 10, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154461, "dur": 10, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154472, "dur": 8, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154482, "dur": 14, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154497, "dur": 31, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154529, "dur": 10, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154542, "dur": 15, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154558, "dur": 10, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154569, "dur": 12, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154582, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154605, "dur": 10, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154616, "dur": 12, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154630, "dur": 10, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154642, "dur": 13, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154656, "dur": 10, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154668, "dur": 10, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154679, "dur": 10, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154691, "dur": 17, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154709, "dur": 11, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154721, "dur": 13, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154736, "dur": 9, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154746, "dur": 15, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154763, "dur": 14, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154779, "dur": 15, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154796, "dur": 15, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154812, "dur": 39, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154853, "dur": 81, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154935, "dur": 1, "ph": "X", "name": "ProcessMessages 1854", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154937, "dur": 13, "ph": "X", "name": "ReadAsync 1854", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154951, "dur": 10, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154962, "dur": 12, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154977, "dur": 11, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361154990, "dur": 9, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155000, "dur": 10, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155011, "dur": 10, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155023, "dur": 10, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155034, "dur": 9, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155045, "dur": 10, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155056, "dur": 10, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155068, "dur": 9, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155080, "dur": 47, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155129, "dur": 10, "ph": "X", "name": "ReadAsync 1032", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155141, "dur": 16, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155158, "dur": 10, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155169, "dur": 10, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155180, "dur": 10, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155192, "dur": 9, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155202, "dur": 20, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155223, "dur": 12, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155237, "dur": 11, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155249, "dur": 9, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155260, "dur": 10, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155271, "dur": 10, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155282, "dur": 10, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155294, "dur": 11, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155306, "dur": 12, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155320, "dur": 11, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155333, "dur": 8, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155342, "dur": 11, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155354, "dur": 9, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155364, "dur": 11, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155377, "dur": 15, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155393, "dur": 21, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155416, "dur": 11, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155428, "dur": 10, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155440, "dur": 10, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155451, "dur": 9, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155462, "dur": 11, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155474, "dur": 9, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155485, "dur": 8, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155494, "dur": 11, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155506, "dur": 10, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155518, "dur": 9, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155528, "dur": 12, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155542, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155553, "dur": 10, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155565, "dur": 16, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155583, "dur": 10, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155594, "dur": 10, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155605, "dur": 10, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155619, "dur": 12, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155633, "dur": 11, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155645, "dur": 11, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155658, "dur": 10, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155669, "dur": 10, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155681, "dur": 10, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155693, "dur": 25, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155719, "dur": 10, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155731, "dur": 22, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155754, "dur": 9, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155765, "dur": 12, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155779, "dur": 10, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155791, "dur": 11, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155803, "dur": 10, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155814, "dur": 9, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155825, "dur": 11, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155837, "dur": 10, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155848, "dur": 10, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155859, "dur": 10, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155871, "dur": 10, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155882, "dur": 10, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155894, "dur": 10, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155905, "dur": 9, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155915, "dur": 11, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155928, "dur": 10, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155939, "dur": 54, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361155995, "dur": 10, "ph": "X", "name": "ReadAsync 1842", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156007, "dur": 10, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156018, "dur": 9, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156029, "dur": 11, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156041, "dur": 11, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156053, "dur": 10, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156065, "dur": 11, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156078, "dur": 9, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156087, "dur": 11, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156100, "dur": 10, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156111, "dur": 11, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156123, "dur": 12, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156137, "dur": 11, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156149, "dur": 9, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156160, "dur": 8, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156170, "dur": 13, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156184, "dur": 35, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156221, "dur": 11, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156233, "dur": 10, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156244, "dur": 10, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156255, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156266, "dur": 14, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156281, "dur": 11, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156293, "dur": 10, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156305, "dur": 9, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156316, "dur": 10, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156327, "dur": 13, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156342, "dur": 9, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156353, "dur": 9, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156363, "dur": 9, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156373, "dur": 9, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156385, "dur": 11, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156397, "dur": 12, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156410, "dur": 9, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156421, "dur": 9, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156431, "dur": 9, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156441, "dur": 10, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156452, "dur": 12, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156465, "dur": 15, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156481, "dur": 34, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156517, "dur": 50, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156569, "dur": 14, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156585, "dur": 11, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156598, "dur": 13, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156612, "dur": 14, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156628, "dur": 14, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156644, "dur": 12, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156658, "dur": 9, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156669, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156681, "dur": 13, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156696, "dur": 11, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156709, "dur": 12, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156727, "dur": 12, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156740, "dur": 8, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156752, "dur": 16, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156770, "dur": 11, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156782, "dur": 12, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156796, "dur": 10, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156808, "dur": 12, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156821, "dur": 9, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156832, "dur": 9, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156842, "dur": 9, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156852, "dur": 9, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156863, "dur": 9, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156873, "dur": 11, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156885, "dur": 10, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156897, "dur": 11, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156909, "dur": 10, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156920, "dur": 9, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156931, "dur": 11, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156943, "dur": 10, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156954, "dur": 11, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156966, "dur": 10, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156978, "dur": 11, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361156990, "dur": 13, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157004, "dur": 10, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157015, "dur": 41, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157059, "dur": 23, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157084, "dur": 17, "ph": "X", "name": "ReadAsync 1508", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157103, "dur": 11, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157115, "dur": 11, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157129, "dur": 20, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157151, "dur": 12, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157165, "dur": 11, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157177, "dur": 11, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157190, "dur": 12, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157205, "dur": 15, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157221, "dur": 53, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157277, "dur": 10, "ph": "X", "name": "ReadAsync 2036", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157288, "dur": 13, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157302, "dur": 9, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157312, "dur": 11, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157324, "dur": 15, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157341, "dur": 16, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157359, "dur": 10, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157370, "dur": 14, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157385, "dur": 9, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157396, "dur": 14, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157412, "dur": 10, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157423, "dur": 16, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157441, "dur": 11, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157453, "dur": 10, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157465, "dur": 9, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157475, "dur": 39, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157517, "dur": 1, "ph": "X", "name": "ProcessMessages 953", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157519, "dur": 15, "ph": "X", "name": "ReadAsync 953", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157535, "dur": 13, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157550, "dur": 43, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157596, "dur": 14, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157613, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157660, "dur": 31, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157693, "dur": 1, "ph": "X", "name": "ProcessMessages 1676", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157694, "dur": 18, "ph": "X", "name": "ReadAsync 1676", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157715, "dur": 13, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157729, "dur": 31, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157764, "dur": 18, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157784, "dur": 15, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157801, "dur": 15, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157816, "dur": 13, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157830, "dur": 12, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157843, "dur": 14, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157860, "dur": 12, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157882, "dur": 16, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157899, "dur": 12, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157913, "dur": 20, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157935, "dur": 11, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157948, "dur": 11, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157961, "dur": 16, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157979, "dur": 17, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361157998, "dur": 17, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158018, "dur": 13, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158033, "dur": 11, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158045, "dur": 15, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158062, "dur": 15, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158078, "dur": 11, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158090, "dur": 36, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158128, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158140, "dur": 11, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158152, "dur": 12, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158166, "dur": 18, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158185, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158187, "dur": 12, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158201, "dur": 11, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158213, "dur": 11, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158225, "dur": 139, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158366, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158381, "dur": 11, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158395, "dur": 11, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158408, "dur": 10, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158419, "dur": 27, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158447, "dur": 9, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158459, "dur": 9, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158469, "dur": 9, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158480, "dur": 372, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361158855, "dur": 768, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361159624, "dur": 2, "ph": "X", "name": "ProcessMessages 1204", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361159627, "dur": 147, "ph": "X", "name": "ReadAsync 1204", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361159777, "dur": 7, "ph": "X", "name": "ProcessMessages 3136", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361159785, "dur": 1610, "ph": "X", "name": "ReadAsync 3136", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361161398, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361161418, "dur": 18, "ph": "X", "name": "ProcessMessages 114", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361161437, "dur": 845, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361162286, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361162303, "dur": 123, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361162430, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361162449, "dur": 9, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361162460, "dur": 276, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361162738, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361162843, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361162867, "dur": 198, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361163068, "dur": 2106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165178, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165227, "dur": 5, "ph": "X", "name": "ProcessMessages 2164", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165233, "dur": 14, "ph": "X", "name": "ReadAsync 2164", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165249, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165261, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165287, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165341, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165414, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165427, "dur": 71, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165499, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165517, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165558, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165616, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165628, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165644, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165655, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165665, "dur": 144, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165810, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165826, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165867, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165880, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165891, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165924, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361165935, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361166079, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361166095, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361166156, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361166193, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361166232, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361166244, "dur": 10, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361166256, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361166265, "dur": 132, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361166398, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361166415, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361166452, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361166468, "dur": 171, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361166641, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361166652, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361166653, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361166664, "dur": 3411, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361170076, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361170088, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033361170089, "dur": 1773630, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033362943726, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033362943729, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033362943784, "dur": 848, "ph": "X", "name": "ProcessMessages 9761", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033362944633, "dur": 3156, "ph": "X", "name": "ReadAsync 9761", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033362947793, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033362947810, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033362947812, "dur": 1286, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033362949102, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033362949119, "dur": 9, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033362949129, "dur": 23101, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033362972238, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033362972241, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033362972254, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033362972256, "dur": 442974, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363415236, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363415238, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363415249, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363415251, "dur": 1388, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363416642, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363416644, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363416653, "dur": 12, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363416666, "dur": 53689, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363470363, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363470368, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363470387, "dur": 394, "ph": "X", "name": "ProcessMessages 4348", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363470782, "dur": 1223, "ph": "X", "name": "ReadAsync 4348", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363472009, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363472020, "dur": 342, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363472366, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363472388, "dur": 6, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363472396, "dur": 219891, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363692294, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363692297, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363692349, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363692352, "dur": 725, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363693081, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363693156, "dur": 13, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363693170, "dur": 437, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363693610, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 21474836480, "ts": 1757033363693627, "dur": 3326, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 82792, "tid": 16, "ts": 1757033363706994, "dur": 378, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 82792, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 82792, "tid": 17179869184, "ts": 1757033361106487, "dur": 27, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 82792, "tid": 17179869184, "ts": 1757033361106515, "dur": 15912, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 82792, "tid": 17179869184, "ts": 1757033361122428, "dur": 18, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 82792, "tid": 16, "ts": 1757033363707373, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 82792, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 82792, "tid": 1, "ts": 1757033354977950, "dur": 22428, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 82792, "tid": 1, "ts": 1757033355000382, "dur": 16907, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 82792, "tid": 1, "ts": 1757033355017294, "dur": 40223, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 82792, "tid": 16, "ts": 1757033363707377, "dur": 2, "ph": "X", "name": "", "args": {}}, {"pid": 82792, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033354976666, "dur": 2670, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033354979337, "dur": 4365330, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033354979923, "dur": 1762, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033354981688, "dur": 314, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033354982004, "dur": 4985, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033354986997, "dur": 136, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033354987135, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033354987164, "dur": 375, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033354987541, "dur": 14578, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002123, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002125, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002156, "dur": 329, "ph": "X", "name": "ProcessMessages 1230", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002487, "dur": 77, "ph": "X", "name": "ReadAsync 1230", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002567, "dur": 4, "ph": "X", "name": "ProcessMessages 11131", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002572, "dur": 25, "ph": "X", "name": "ReadAsync 11131", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002600, "dur": 12, "ph": "X", "name": "ReadAsync 1044", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002613, "dur": 13, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002629, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002630, "dur": 33, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002665, "dur": 13, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002680, "dur": 12, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002693, "dur": 13, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002708, "dur": 9, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002719, "dur": 11, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002731, "dur": 12, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002745, "dur": 46, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002793, "dur": 31, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002826, "dur": 10, "ph": "X", "name": "ReadAsync 1713", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002837, "dur": 11, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002848, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002851, "dur": 32, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002885, "dur": 11, "ph": "X", "name": "ReadAsync 1318", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002897, "dur": 9, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002907, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002933, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002955, "dur": 12, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002969, "dur": 10, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002981, "dur": 12, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355002995, "dur": 11, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003007, "dur": 11, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003019, "dur": 11, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003032, "dur": 11, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003044, "dur": 11, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003057, "dur": 11, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003070, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003093, "dur": 14, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003108, "dur": 10, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003120, "dur": 11, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003133, "dur": 10, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003145, "dur": 11, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003158, "dur": 15, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003175, "dur": 13, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003189, "dur": 17, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003207, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003228, "dur": 12, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003241, "dur": 13, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003256, "dur": 11, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003269, "dur": 11, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003281, "dur": 10, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003293, "dur": 10, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003304, "dur": 8, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003314, "dur": 168, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003484, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003502, "dur": 28, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003532, "dur": 14, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003547, "dur": 11, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003560, "dur": 14, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003576, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003588, "dur": 12, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003602, "dur": 14, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003618, "dur": 11, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003630, "dur": 16, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003648, "dur": 10, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003659, "dur": 12, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003673, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003703, "dur": 11, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003716, "dur": 14, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003732, "dur": 21, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003755, "dur": 42, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355003799, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006162, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006165, "dur": 136, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006301, "dur": 10, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006313, "dur": 17, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006332, "dur": 14, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006347, "dur": 10, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006359, "dur": 11, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006372, "dur": 11, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006384, "dur": 12, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006398, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006410, "dur": 254, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006665, "dur": 33, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006699, "dur": 1, "ph": "X", "name": "ProcessMessages 3680", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006701, "dur": 11, "ph": "X", "name": "ReadAsync 3680", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006714, "dur": 11, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006726, "dur": 11, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006738, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006739, "dur": 11, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006752, "dur": 11, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006765, "dur": 14, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006781, "dur": 10, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006792, "dur": 16, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006810, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006904, "dur": 15, "ph": "X", "name": "ReadAsync 2039", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006922, "dur": 14, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006937, "dur": 10, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006949, "dur": 10, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006961, "dur": 10, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006972, "dur": 10, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006983, "dur": 9, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355006993, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007091, "dur": 1, "ph": "X", "name": "ProcessMessages 2774", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007093, "dur": 10, "ph": "X", "name": "ReadAsync 2774", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007104, "dur": 23, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007129, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007140, "dur": 10, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007152, "dur": 10, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007163, "dur": 11, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007175, "dur": 116, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007293, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007325, "dur": 10, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007337, "dur": 14, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007353, "dur": 12, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007366, "dur": 10, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007378, "dur": 161, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007542, "dur": 313, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007858, "dur": 1, "ph": "X", "name": "ProcessMessages 3042", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007865, "dur": 118, "ph": "X", "name": "ReadAsync 3042", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007985, "dur": 1, "ph": "X", "name": "ProcessMessages 2778", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355007987, "dur": 36, "ph": "X", "name": "ReadAsync 2778", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008026, "dur": 2, "ph": "X", "name": "ProcessMessages 2146", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008029, "dur": 70, "ph": "X", "name": "ReadAsync 2146", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008101, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008133, "dur": 12, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008147, "dur": 11, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008160, "dur": 34, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008195, "dur": 29, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008227, "dur": 1, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008229, "dur": 15, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008245, "dur": 11, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008258, "dur": 10, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008270, "dur": 18, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008290, "dur": 9, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008300, "dur": 11, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008313, "dur": 9, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008323, "dur": 10, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008334, "dur": 12, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008348, "dur": 10, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008360, "dur": 20, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008383, "dur": 17, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008402, "dur": 11, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008415, "dur": 10, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008426, "dur": 10, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008438, "dur": 113, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008554, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008572, "dur": 13, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008586, "dur": 16, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008605, "dur": 12, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008619, "dur": 13, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008633, "dur": 9, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008644, "dur": 10, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008656, "dur": 11, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008668, "dur": 10, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008680, "dur": 11, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008693, "dur": 11, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008705, "dur": 10, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008717, "dur": 14, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008733, "dur": 10, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008745, "dur": 9, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008756, "dur": 11, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008768, "dur": 11, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008781, "dur": 11, "ph": "X", "name": "ReadAsync 9", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008793, "dur": 12, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008807, "dur": 11, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008820, "dur": 18, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008839, "dur": 23, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008864, "dur": 11, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008878, "dur": 12, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008891, "dur": 11, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008904, "dur": 10, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008916, "dur": 12, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008930, "dur": 11, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008942, "dur": 10, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008954, "dur": 10, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008966, "dur": 11, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008979, "dur": 17, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355008998, "dur": 18, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009018, "dur": 164, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009183, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009194, "dur": 11, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009207, "dur": 12, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009220, "dur": 13, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009235, "dur": 10, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009246, "dur": 10, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009258, "dur": 10, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009269, "dur": 11, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009282, "dur": 12, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009296, "dur": 15, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009314, "dur": 12, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009328, "dur": 11, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009341, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009365, "dur": 13, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009379, "dur": 9, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009390, "dur": 11, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009404, "dur": 11, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009417, "dur": 11, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009429, "dur": 9, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009440, "dur": 16, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009458, "dur": 10, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009469, "dur": 94, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009564, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009576, "dur": 11, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009588, "dur": 10, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009600, "dur": 16, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009618, "dur": 10, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009630, "dur": 24, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009656, "dur": 10, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009668, "dur": 11, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009681, "dur": 10, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009692, "dur": 11, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009704, "dur": 12, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009718, "dur": 10, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009730, "dur": 10, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009742, "dur": 9, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009752, "dur": 11, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009764, "dur": 11, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009777, "dur": 14, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009794, "dur": 12, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009807, "dur": 11, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009820, "dur": 11, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009833, "dur": 12, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009846, "dur": 107, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009955, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009974, "dur": 21, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355009997, "dur": 12, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010011, "dur": 60, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010072, "dur": 11, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010085, "dur": 12, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010100, "dur": 11, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010113, "dur": 11, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010126, "dur": 14, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010141, "dur": 11, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010154, "dur": 10, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010166, "dur": 11, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010178, "dur": 11, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010191, "dur": 12, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010204, "dur": 89, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010295, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010312, "dur": 11, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010324, "dur": 11, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010337, "dur": 10, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010348, "dur": 10, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010360, "dur": 15, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010377, "dur": 16, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010395, "dur": 13, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010411, "dur": 240, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010653, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010672, "dur": 12, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010686, "dur": 11, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010699, "dur": 11, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010712, "dur": 12, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010726, "dur": 16, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010743, "dur": 11, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010756, "dur": 11, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010770, "dur": 9, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010780, "dur": 43, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010826, "dur": 12, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010840, "dur": 9, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355010850, "dur": 13, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355011992, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355011994, "dur": 255, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355012252, "dur": 1, "ph": "X", "name": "ProcessMessages 1277", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355012287, "dur": 164, "ph": "X", "name": "ReadAsync 1277", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355012453, "dur": 142, "ph": "X", "name": "ProcessMessages 11439", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355012597, "dur": 37, "ph": "X", "name": "ReadAsync 11439", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355012635, "dur": 2, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355012638, "dur": 38, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355012678, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355012712, "dur": 122, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355012837, "dur": 1009, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355013850, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355013851, "dur": 44, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355013896, "dur": 4, "ph": "X", "name": "ProcessMessages 2080", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355013902, "dur": 1886, "ph": "X", "name": "ReadAsync 2080", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355015794, "dur": 151, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355015948, "dur": 313, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355016262, "dur": 1558, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355017825, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355017858, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355017860, "dur": 2097, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355019963, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355019964, "dur": 409, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355020376, "dur": 2206, "ph": "X", "name": "ProcessMessages 1380", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355022585, "dur": 142, "ph": "X", "name": "ReadAsync 1380", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355022734, "dur": 52, "ph": "X", "name": "ProcessMessages 1734", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355022788, "dur": 293, "ph": "X", "name": "ReadAsync 1734", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355023086, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355023136, "dur": 14, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355023152, "dur": 258, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355023413, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355023415, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355023450, "dur": 11, "ph": "X", "name": "ProcessMessages 178", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355023462, "dur": 14, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355023478, "dur": 258, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355023741, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355023782, "dur": 13, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355023797, "dur": 38, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355023837, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355023839, "dur": 80, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355023923, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355023993, "dur": 14, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355024008, "dur": 101, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355024115, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355024153, "dur": 8, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355024163, "dur": 197, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355024363, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355024365, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355024423, "dur": 12, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355024436, "dur": 250, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355024689, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355024693, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355024731, "dur": 16, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355024748, "dur": 120, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355024873, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355024940, "dur": 12, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355024953, "dur": 1977, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355026939, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355026942, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355027027, "dur": 60, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355027091, "dur": 257, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355027352, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355027354, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355027377, "dur": 10, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355027388, "dur": 28, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355027420, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355027443, "dur": 5, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355027450, "dur": 18, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355027469, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355027494, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355027509, "dur": 8, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355027518, "dur": 1318, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355028843, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355028848, "dur": 124, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355028975, "dur": 48, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355029025, "dur": 292, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355029321, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355029338, "dur": 8, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355029346, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355029400, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355029402, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355029421, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355029434, "dur": 609, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030046, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030047, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030062, "dur": 15, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030078, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030103, "dur": 8, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030113, "dur": 69, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030185, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030211, "dur": 6, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030218, "dur": 9, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030228, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030254, "dur": 8, "ph": "X", "name": "ProcessMessages 10", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030264, "dur": 56, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030323, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030335, "dur": 6, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030341, "dur": 356, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030703, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030730, "dur": 8, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030740, "dur": 30, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030772, "dur": 5, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030778, "dur": 25, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030805, "dur": 8, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355030814, "dur": 669, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355031489, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355031524, "dur": 9, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355031535, "dur": 20, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355031558, "dur": 6, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355031565, "dur": 139, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355031706, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355031737, "dur": 8, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355031747, "dur": 212, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355031963, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355031989, "dur": 7, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355031997, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032020, "dur": 7, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032028, "dur": 13, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032043, "dur": 5, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032049, "dur": 64, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032117, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032133, "dur": 6, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032140, "dur": 22, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032165, "dur": 6, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032172, "dur": 21, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032195, "dur": 4, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032200, "dur": 437, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032641, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032667, "dur": 6, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032674, "dur": 58, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032736, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032755, "dur": 6, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032762, "dur": 82, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032846, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032858, "dur": 3, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355032862, "dur": 308, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355033176, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355033199, "dur": 7, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033355033208, "dur": 4303824, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033359337039, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033359337041, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033359337067, "dur": 4581, "ph": "X", "name": "ProcessMessages 9761", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033359341651, "dur": 23, "ph": "X", "name": "ReadAsync 9761", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033359341675, "dur": 130, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 82792, "tid": 12884901888, "ts": 1757033359341807, "dur": 2389, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 82792, "tid": 16, "ts": 1757033363707380, "dur": 255, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 82792, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 82792, "tid": 8589934592, "ts": 1757033354974719, "dur": 82844, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 82792, "tid": 8589934592, "ts": 1757033355057567, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 82792, "tid": 8589934592, "ts": 1757033355057570, "dur": 1332, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 82792, "tid": 16, "ts": 1757033363707636, "dur": 2, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 82792, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 82792, "tid": 4294967296, "ts": 1757033354867424, "dur": 4477805, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 82792, "tid": 4294967296, "ts": 1757033354871980, "dur": 96175, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 82792, "tid": 4294967296, "ts": 1757033359345325, "dur": 1717134, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 82792, "tid": 4294967296, "ts": 1757033361062569, "dur": 2634414, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 82792, "tid": 4294967296, "ts": 1757033361062639, "dur": 43827, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 82792, "tid": 4294967296, "ts": 1757033363696992, "dur": 5718, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 82792, "tid": 4294967296, "ts": 1757033363698729, "dur": 3198, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 82792, "tid": 4294967296, "ts": 1757033363702714, "dur": 7, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 82792, "tid": 16, "ts": 1757033363707640, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1757033361123715, "dur": 29339, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757033361153058, "dur": 112, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757033361153234, "dur": 584, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757033361153835, "dur": 5926, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757033361159768, "dur": 2534665, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757033363694434, "dur": 214, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757033363694819, "dur": 63, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757033363694891, "dur": 735, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1757033361153647, "dur": 6127, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361159778, "dur": 702, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361160658, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361160747, "dur": 88, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361160836, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361161167, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361161252, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361161507, "dur": 1555, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-unity\\Modules\\Timeline\\SpineSkeletonFlip\\SpineSkeletonFlipMixerBehaviour.cs"}}, {"pid": 12345, "tid": 1, "ts": 1757033361161393, "dur": 1669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361163063, "dur": 131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361163194, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361163510, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361163702, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361164090, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361164360, "dur": 2040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361166428, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361166605, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361166879, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361167143, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361167514, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033361167757, "dur": 1780863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033362948620, "dur": 746000, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361153647, "dur": 6132, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361159798, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1757033361159793, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_4C96AFA9785B3E8A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757033361160114, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361160372, "dur": 1138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361161512, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361161627, "dur": 114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361161741, "dur": 106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361161847, "dur": 113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361161960, "dur": 123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361162083, "dur": 114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361162197, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361162340, "dur": 124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361162464, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361162631, "dur": 588, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float2x4.gen.cs"}}, {"pid": 12345, "tid": 2, "ts": 1757033361162574, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361163271, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361163409, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361163672, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361164053, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361164343, "dur": 1827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361166170, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757033361166234, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1757033361166532, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361166614, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361166872, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361166941, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361167150, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361167403, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361167469, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033361167729, "dur": 1780836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033362948566, "dur": 745912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361153665, "dur": 6129, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361159805, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1757033361159799, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_63B80DE4FB9B8B85.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757033361160386, "dur": 1255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361161645, "dur": 117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361161762, "dur": 127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361161889, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361162011, "dur": 111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361162122, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361162285, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361162422, "dur": 112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361162534, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361162974, "dur": 109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361163089, "dur": 101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361163190, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361163372, "dur": 112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361163484, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361163672, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361164053, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361164339, "dur": 2060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361166399, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361166462, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361166605, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361166878, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361167186, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361167513, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033361167754, "dur": 1780796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033362948551, "dur": 745980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033361153651, "dur": 6137, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033361159933, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033361160249, "dur": 641, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033361160955, "dur": 2289, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\OrganBaseComponentWrap.cs"}}, {"pid": 12345, "tid": 4, "ts": 1757033361160891, "dur": 2373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033361163264, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033361163596, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033361163694, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033361164060, "dur": 2428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033361166488, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033361166621, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033361166873, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033361167160, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033361167429, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033361167500, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033361167775, "dur": 1780804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033362948579, "dur": 746015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033361153681, "dur": 6118, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033361159937, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1757033361159936, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_D8923AA791216057.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1757033361160136, "dur": 229, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1757033361160136, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_ABC31C6E784BF4F9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1757033361160422, "dur": 2838, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033361163264, "dur": 121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033361163385, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033361163685, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033361164073, "dur": 2414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033361166487, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033361166621, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033361166896, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033361167164, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033361167430, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033361167491, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033361167778, "dur": 1780797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033362948576, "dur": 746037, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033361153704, "dur": 6115, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033361160087, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033361160632, "dur": 2761, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033361163394, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033361163733, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033361164079, "dur": 2416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033361166495, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033361166620, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033361166900, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033361167159, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033361167455, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033361167747, "dur": 1780844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033362948591, "dur": 745993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361153721, "dur": 6104, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361160131, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361160640, "dur": 90, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361160731, "dur": 113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361160882, "dur": 1108, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\UnityEngine_Events_UnityEventWrap.cs"}}, {"pid": 12345, "tid": 7, "ts": 1757033361160844, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361162119, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361162335, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361162534, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361163001, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361163100, "dur": 121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361163221, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361163611, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361163692, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361164061, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361164365, "dur": 2116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361166481, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361166623, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361166893, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361167161, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361167430, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361167482, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033361167741, "dur": 1780862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033362948603, "dur": 745835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033361153735, "dur": 6173, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033361159936, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033361160130, "dur": 508, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033361160643, "dur": 90, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033361160734, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033361160823, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1757033361161008, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DemiLib\\Editor\\DemiEditor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1757033361161568, "dur": 1943, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 8, "ts": 1757033361161001, "dur": 2642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1757033361163675, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033361164072, "dur": 2408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033361166480, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033361166622, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033361166896, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033361167137, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033361167424, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033361167476, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033361167743, "dur": 1780851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033362948595, "dur": 745849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757033361153754, "dur": 6192, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757033361160135, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1757033361160126, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_925C9BD15D5833BC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1757033361160187, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757033361160792, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1757033361161005, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1757033361162437, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultDataWithTestData.cs"}}, {"pid": 12345, "tid": 9, "ts": 1757033361162641, "dur": 877, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\AssemblyWrapper.cs"}}, {"pid": 12345, "tid": 9, "ts": 1757033361160851, "dur": 2740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1757033361163733, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1757033361164119, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1757033361166404, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1757033361166598, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1757033361166515, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1757033361166946, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1757033361167209, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1757033361167476, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1757033361167540, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1757033361168083, "dur": 2790, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1757033361167735, "dur": 3138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1757033361171266, "dur": 89, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757033361171361, "dur": 1773742, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1757033362948759, "dur": 24399, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 9, "ts": 1757033362948546, "dur": 24693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1757033362973426, "dur": 59, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757033362973495, "dur": 498194, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1757033363473137, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1757033363473136, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1757033363473267, "dur": 388, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1757033363473657, "dur": 220958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361153778, "dur": 6364, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361160147, "dur": 256, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757033361160145, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_7BBCF5494DCBE09D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1757033361160657, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361160756, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361161215, "dur": 113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361161329, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361161461, "dur": 129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361161590, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361161698, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361161797, "dur": 117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361161914, "dur": 102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361162016, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361162126, "dur": 104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361162230, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361162329, "dur": 120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361162449, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361162638, "dur": 881, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\int2x2.gen.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757033361162567, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361163558, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361163719, "dur": 364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361164083, "dur": 2419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361166503, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361166612, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361166903, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361167168, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361167508, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033361167766, "dur": 1780840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033362948606, "dur": 746003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033361153791, "dur": 6363, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033361160159, "dur": 276, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1757033361160157, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_0A9DB5C36482F58D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1757033361160529, "dur": 2804, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033361163389, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033361163687, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033361164065, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033361164353, "dur": 2120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033361166473, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033361166626, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033361166886, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033361167157, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033361167502, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033361167772, "dur": 1780811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033362948583, "dur": 746021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033361153812, "dur": 6346, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033361160162, "dur": 381, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1757033361160161, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_2FD3DB5FA2EA5C12.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757033361161123, "dur": 197, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1757033361161344, "dur": 2135, "ph": "X", "name": "File", "args": {"detail": "Assets\\IronSource\\Scripts\\IronSourceInitializationAndroid.cs"}}, {"pid": 12345, "tid": 12, "ts": 1757033361161321, "dur": 2241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033361163562, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033361163712, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033361164083, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033361164338, "dur": 2135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033361166474, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033361166625, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033361166907, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033361167174, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033361167516, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033361167751, "dur": 1780862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033362948613, "dur": 745819, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033361153826, "dur": 6341, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033361160347, "dur": 1234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033361161583, "dur": 117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033361161700, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033361161799, "dur": 114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033361161913, "dur": 128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033361162041, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033361162151, "dur": 119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033361162316, "dur": 941, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMPro_UGUI_Private.cs"}}, {"pid": 12345, "tid": 13, "ts": 1757033361162270, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033361163397, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033361163686, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033361164072, "dur": 2429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033361166501, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033361166613, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033361166903, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033361167166, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033361167494, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033361167769, "dur": 1780818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033362948588, "dur": 746002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757033361153843, "dur": 6328, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757033361160355, "dur": 3046, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757033361163405, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757033361163687, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757033361164073, "dur": 2421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757033361166494, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757033361166621, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757033361166900, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757033361167165, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757033361167412, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757033361167482, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757033361167742, "dur": 1780802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757033362948546, "dur": 467932, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1757033362948545, "dur": 467934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1757033363416495, "dur": 1420, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1757033363417919, "dur": 276749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361153857, "dur": 6339, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361160662, "dur": 87, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361160750, "dur": 127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361160908, "dur": 1414, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\UnityEngine_BoxCollider2DWrap.cs"}}, {"pid": 12345, "tid": 15, "ts": 1757033361160877, "dur": 1513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361162391, "dur": 124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361162515, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361163000, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361163099, "dur": 112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361163211, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361163601, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361163688, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361164062, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361164361, "dur": 2101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361166462, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361166627, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361166880, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361167159, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361167454, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1757033361167526, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033361167749, "dur": 1780867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033362948617, "dur": 745798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033361153885, "dur": 6995, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033361160885, "dur": 96, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033361160981, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033361161230, "dur": 2283, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Framework\\WebSocket\\WebSocketSession.cs"}}, {"pid": 12345, "tid": 16, "ts": 1757033361161197, "dur": 2411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033361163609, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033361163679, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033361164040, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033361164438, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1757033361164702, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1757033361164770, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1757033361164932, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1757033361164996, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1757033361165072, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1757033361165327, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1757033361165576, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033361165949, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1757033361166426, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033361166537, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033361166605, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033361166879, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033361167141, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033361167462, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033361167728, "dur": 1780820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033362948548, "dur": 524591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033363473141, "dur": 220288, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1757033363473140, "dur": 220290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1757033363693450, "dur": 911, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 17, "ts": 1757033361153900, "dur": 7096, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361161000, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361161485, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361161603, "dur": 94, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361161697, "dur": 111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361161808, "dur": 104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361161912, "dur": 116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361162028, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361162150, "dur": 121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361162272, "dur": 124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361162396, "dur": 119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361162515, "dur": 116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361162631, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361163131, "dur": 119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361163250, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361163392, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361163672, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361164118, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361164387, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1757033361164451, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1757033361164713, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361164974, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1757033361165044, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1757033361165308, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1757033361165550, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361165608, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1757033361165873, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1757033361165950, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1757033361166433, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361166598, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361166697, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361166879, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361167136, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361167457, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033361167729, "dur": 1780820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033362948550, "dur": 745988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033361153925, "dur": 7092, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033361161020, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033361161483, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033361161632, "dur": 1860, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Splines\\Editor\\DSSplineDrawer.cs"}}, {"pid": 12345, "tid": 18, "ts": 1757033361161593, "dur": 1990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033361163584, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033361163698, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033361164040, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033361164394, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1757033361164452, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033361164611, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1757033361164941, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1757033361165027, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/AppleAuth.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1757033361165093, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1757033361165368, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1757033361165596, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033361165804, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1757033361165868, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1757033361165933, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1757033361166685, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1757033361166935, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033361167152, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033361167403, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033361167475, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033361167720, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1757033361167933, "dur": 1780639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033362948573, "dur": 745840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033361153941, "dur": 7085, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033361161029, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033361161471, "dur": 125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033361161596, "dur": 121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033361161718, "dur": 113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033361161913, "dur": 1055, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Utilities\\TrackModifier.cs"}}, {"pid": 12345, "tid": 19, "ts": 1757033361161831, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033361162989, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033361163092, "dur": 121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033361163214, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033361163532, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033361163705, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033361164092, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033361164444, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1757033361164739, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033361164967, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1757033361165033, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1757033361165344, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1757033361165593, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1757033361165883, "dur": 524, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 19, "ts": 1757033361165828, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1757033361166611, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 19, "ts": 1757033361166445, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1757033361166790, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033361166887, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033361167140, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033361167456, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033361167736, "dur": 1780831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033362948567, "dur": 745910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361153955, "dur": 7081, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361161038, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361161622, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361161755, "dur": 96, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361161851, "dur": 112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361161963, "dur": 127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361162090, "dur": 112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361162202, "dur": 113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361162315, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361162437, "dur": 543, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UI\\DrawStaticElement.cs"}}, {"pid": 12345, "tid": 20, "ts": 1757033361162437, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361163098, "dur": 106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361163204, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361163382, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361163680, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361164046, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361164346, "dur": 1689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361166035, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1757033361166441, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361166598, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361166794, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361166889, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361167153, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361167410, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361167462, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033361167735, "dur": 1780857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033362948593, "dur": 745843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033361153974, "dur": 7071, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033361161047, "dur": 878, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Scripts\\ScriptDLL\\InjectionGameObject.cs"}}, {"pid": 12345, "tid": 21, "ts": 1757033361161047, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033361162021, "dur": 113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033361162134, "dur": 98, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033361162232, "dur": 125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033361162357, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033361162467, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033361162583, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033361163435, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033361163665, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033361164054, "dur": 2348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033361166429, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033361166604, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033361166884, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033361167135, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033361167456, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033361167737, "dur": 1780815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033362948552, "dur": 745962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033361153988, "dur": 7061, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033361161051, "dur": 1399, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Scripts\\ScriptDLL\\GameLuaObjectDataComparer_3.cs"}}, {"pid": 12345, "tid": 22, "ts": 1757033361161051, "dur": 1499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033361162565, "dur": 648, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\random.cs"}}, {"pid": 12345, "tid": 22, "ts": 1757033361162551, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033361163385, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033361163679, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033361164046, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033361164345, "dur": 1698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033361166043, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1757033361166435, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033361166636, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033361166885, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033361167154, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033361167423, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033361167510, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033361167760, "dur": 1780849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033362948610, "dur": 745821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033361154004, "dur": 7050, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033361161056, "dur": 1531, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Scripts\\CommonTools\\CsGetLuaUtilst.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757033361161056, "dur": 2391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033361163447, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033361163676, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033361164052, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033361164347, "dur": 1538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033361165885, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1757033361165948, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1757033361166166, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033361166454, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033361166598, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033361166886, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033361167144, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033361167410, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033361167462, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033361167734, "dur": 1780826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033362948560, "dur": 745937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033361154021, "dur": 7043, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033361161067, "dur": 2339, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\CasualGame\\FlexyRing\\Script\\GameScript\\GramScripts\\RewardEntityManager.cs"}}, {"pid": 12345, "tid": 24, "ts": 1757033361161066, "dur": 2447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033361163513, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033361163715, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033361164086, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033361164365, "dur": 1216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033361165583, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1757033361165812, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033361166190, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1757033361166453, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033361166605, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033361166872, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033361167146, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033361167409, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033361167469, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033361167730, "dur": 1780816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033362948548, "dur": 509, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 24, "ts": 1757033362948547, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 24, "ts": 1757033362949076, "dur": 1308, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 24, "ts": 1757033362950386, "dur": 744049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033361154038, "dur": 7040, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033361161080, "dur": 2327, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\CasualGame\\DLYShuiGuan\\Script\\Other\\tunnel.cs"}}, {"pid": 12345, "tid": 25, "ts": 1757033361161080, "dur": 2423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033361163503, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033361163665, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033361164047, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033361164387, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1757033361164514, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1757033361164968, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1757033361165132, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033361165277, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1757033361165922, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033361166427, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033361166612, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Lofelt.NiceVibrations.Editor.dll"}}, {"pid": 12345, "tid": 25, "ts": 1757033361166612, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Lofelt.NiceVibrations.Editor.dll"}}, {"pid": 12345, "tid": 25, "ts": 1757033361166706, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033361166872, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033361167142, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033361167463, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033361167722, "dur": 1780834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033362948557, "dur": 745860, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033361154059, "dur": 7030, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033361161092, "dur": 2318, "ph": "X", "name": "File", "args": {"detail": "Assets\\ThirdParty\\LuaPerfect\\ObjectFormater.cs"}}, {"pid": 12345, "tid": 26, "ts": 1757033361161092, "dur": 2414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033361163506, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033361163677, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033361164050, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033361164338, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033361164434, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1757033361164518, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1757033361165061, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1757033361165332, "dur": 1029, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033361166431, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033361166598, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033361166699, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033361166922, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033361167148, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033361167409, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033361167469, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033361167722, "dur": 1780835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033362948558, "dur": 745953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033361154075, "dur": 7018, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033361161095, "dur": 2324, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\UIPlugins\\TableView\\CCTableViewController.cs"}}, {"pid": 12345, "tid": 27, "ts": 1757033361161095, "dur": 2424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033361163519, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033361163709, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033361164088, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033361164443, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1757033361164960, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757033361165028, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1757033361165287, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1757033361165547, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1757033361165782, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757033361165842, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757033361166039, "dur": 402, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 27, "ts": 1757033361165944, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1757033361166614, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Lofelt.NiceVibrations.Editor.pdb"}}, {"pid": 12345, "tid": 27, "ts": 1757033361166613, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Lofelt.NiceVibrations.Editor.pdb"}}, {"pid": 12345, "tid": 27, "ts": 1757033361166708, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033361166893, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033361167149, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033361167402, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033361167476, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033361167721, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033361167936, "dur": 1780627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033362948564, "dur": 745919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033361154091, "dur": 7005, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033361161096, "dur": 96, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033361161192, "dur": 83, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033361161275, "dur": 96, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033361161372, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033361161485, "dur": 1152, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-csharp\\TransformConstraintData.cs"}}, {"pid": 12345, "tid": 28, "ts": 1757033361161476, "dur": 1994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033361163470, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033361163679, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033361164048, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033361164340, "dur": 2056, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033361166434, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033361166612, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033361166878, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033361167147, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033361167410, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033361167468, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033361167728, "dur": 1780840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033362948568, "dur": 745906, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757033363696735, "dur": 1041, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1757033360679378, "dur": 367489, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1757033360680415, "dur": 172505, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1757033361021603, "dur": 3344, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1757033361024948, "dur": 21914, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1757033361025731, "dur": 18706, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1757033361051361, "dur": 843, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1757033361050933, "dur": 1406, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1757033354978483, "dur": 18596, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757033354997088, "dur": 5122, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757033355002274, "dur": 630, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757033355003330, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FEB44C0922F521E3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1757033355005806, "dur": 1331, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Updater.ref.dll_34B8D5B7DED6E76D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1757033355010845, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1757033355002919, "dur": 9538, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757033355012468, "dur": 49101, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757033355061570, "dur": 463, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757033355062218, "dur": 4279347, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757033359341610, "dur": 51, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757033359341671, "dur": 671, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1757033355002760, "dur": 9715, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033355012528, "dur": 414, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1757033355012487, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_4C96AFA9785B3E8A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1757033355013067, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1757033355013066, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_9BD6764F6FE0AEBB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1757033355013163, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_9BD6764F6FE0AEBB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1757033355013357, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1757033355013759, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1757033355014380, "dur": 936, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033355015318, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033355015538, "dur": 625, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Common\\CompressTookit.cs"}}, {"pid": 12345, "tid": 1, "ts": 1757033355015512, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033355016562, "dur": 1250, "ph": "X", "name": "File", "args": {"detail": "Assets\\WX-WASM-SDK-V2\\Runtime\\WXBase.cs"}}, {"pid": 12345, "tid": 1, "ts": 1757033355017856, "dur": 728, "ph": "X", "name": "File", "args": {"detail": "Assets\\WX-WASM-SDK-V2\\Runtime\\HideLoadingPage.cs"}}, {"pid": 12345, "tid": 1, "ts": 1757033355016516, "dur": 2162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033355018679, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033355018864, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033355019103, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1757033355019187, "dur": 975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1757033355020464, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 1, "ts": 1757033355020773, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll"}}, {"pid": 12345, "tid": 1, "ts": 1757033355020956, "dur": 245, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.SourceGenerators.dll"}}, {"pid": 12345, "tid": 1, "ts": 1757033355020189, "dur": 1031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1757033355021221, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033355021392, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1757033355021927, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757033355022012, "dur": 4152, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/WxEditor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1757033355026189, "dur": 4689, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Sprite.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1757033355030880, "dur": 30684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033355003084, "dur": 9547, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033355012655, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1757033355012634, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_38431C43644D6EBD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757033355012849, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033355012924, "dur": 405, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_38431C43644D6EBD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757033355013329, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1757033355013491, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1757033355013580, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1757033355013866, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1757033355014478, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033355014928, "dur": 800, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\UnityEngine_RendererWrap.cs"}}, {"pid": 12345, "tid": 2, "ts": 1757033355014838, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033355015728, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757033355015898, "dur": 4522, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 2, "ts": 1757033355020421, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.AI.Navigation.dll"}}, {"pid": 12345, "tid": 2, "ts": 1757033355020421, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.dll"}}, {"pid": 12345, "tid": 2, "ts": 1757033355020485, "dur": 1457, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.AI.Navigation.dll"}}, {"pid": 12345, "tid": 2, "ts": 1757033355021949, "dur": 3453, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UIEffect.dll"}}, {"pid": 12345, "tid": 2, "ts": 1757033355025412, "dur": 3913, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1757033355029337, "dur": 4225, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.dll"}}, {"pid": 12345, "tid": 2, "ts": 1757033355033565, "dur": 28043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033355002764, "dur": 9720, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033355012527, "dur": 244, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1757033355012488, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_68485FEE8B3A7A7E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757033355012830, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1757033355012803, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_7D6B8E0347C20661.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757033355012906, "dur": 387, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_7D6B8E0347C20661.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757033355013310, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1757033355014109, "dur": 281, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 3, "ts": 1757033355014599, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableApplyChangesToContextCommand.cs"}}, {"pid": 12345, "tid": 3, "ts": 1757033355013689, "dur": 2147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1757033355016469, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1757033355017367, "dur": 442, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\PlatformSetup\\AndroidPlatformSetup.cs"}}, {"pid": 12345, "tid": 3, "ts": 1757033355015944, "dur": 2493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1757033355018438, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033355018531, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757033355018611, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1757033355019079, "dur": 951, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 3, "ts": 1757033355018932, "dur": 1210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1757033355020142, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033355020413, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757033355020669, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1757033355021084, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033355021205, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757033355021306, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033355021364, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1757033355021691, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757033355021810, "dur": 3840, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/CasualGame.Dreamteck.Splines.dll"}}, {"pid": 12345, "tid": 3, "ts": 1757033355025660, "dur": 4336, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.AI.Navigation.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1757033355029997, "dur": 31623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033355002786, "dur": 9703, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033355012501, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1757033355012703, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1757033355012494, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_63B80DE4FB9B8B85.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1757033355012904, "dur": 372, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_63B80DE4FB9B8B85.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1757033355013277, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1757033355013507, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1757033355014098, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UIEffect.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1757033355014546, "dur": 819, "ph": "X", "name": "File", "args": {"detail": "Assets\\Editor\\XLua\\CustomConfig.cs"}}, {"pid": 12345, "tid": 4, "ts": 1757033355015438, "dur": 746, "ph": "X", "name": "File", "args": {"detail": "Assets\\Editor\\CustomTools\\LogManConsole.cs"}}, {"pid": 12345, "tid": 4, "ts": 1757033355014451, "dur": 2031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033355016573, "dur": 1250, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Splines\\Core\\SplineUtility.cs"}}, {"pid": 12345, "tid": 4, "ts": 1757033355016482, "dur": 1544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033355018026, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033355018212, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033355018462, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033355018547, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033355019046, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1757033355019520, "dur": 221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1757033355019336, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1757033355020068, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757033355020175, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1757033355020326, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1757033355020546, "dur": 1629, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Sirenix.OdinInspector.CompatibilityLayer.dll"}}, {"pid": 12345, "tid": 4, "ts": 1757033355022181, "dur": 4011, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/WxEditor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1757033355026204, "dur": 4868, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.2D.Sprite.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1757033355031074, "dur": 30518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033355002803, "dur": 9692, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033355012501, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1757033355012683, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1757033355012500, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_E2C9DE69A35782B8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1757033355012873, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033355013065, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033355013131, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_B0882F34B7D5F84E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1757033355013244, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_5A47CDEF9E96A3A3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1757033355013389, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1757033355013826, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033355014267, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1757033355014660, "dur": 473, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033355015134, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033355015787, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033355016030, "dur": 3419, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1757033355019450, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1757033355020075, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033355020148, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1757033355020391, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1757033355021170, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757033355021301, "dur": 2630, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/AppleAuth.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1757033355023942, "dur": 4477, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1757033355028430, "dur": 3978, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1757033355032410, "dur": 29192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033355002835, "dur": 9712, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033355012553, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1757033355012697, "dur": 203, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1757033355012550, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_EEA89CD5119B3433.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1757033355012905, "dur": 375, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_EEA89CD5119B3433.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1757033355013311, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1757033355013543, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1757033355013943, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1757033355014011, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1757033355014594, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033355014750, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033355014982, "dur": 1297, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\UnityEngine_Collider2DWrap.cs"}}, {"pid": 12345, "tid": 6, "ts": 1757033355014982, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033355016365, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033355016568, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033355016937, "dur": 869, "ph": "X", "name": "File", "args": {"detail": "Assets\\UnityWebSocket\\Scripts\\Editor\\SettingsWindow.cs"}}, {"pid": 12345, "tid": 6, "ts": 1757033355017807, "dur": 670, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\VisualStudioIntegration.cs"}}, {"pid": 12345, "tid": 6, "ts": 1757033355016770, "dur": 1715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757033355018536, "dur": 2467, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1757033355021014, "dur": 2801, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 6, "ts": 1757033355023843, "dur": 3902, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UIEffect-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1757033355027757, "dur": 3872, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityWebSocket.Runtime.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1757033355031630, "dur": 29949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033355002850, "dur": 9702, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033355012557, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1757033355012696, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1757033355012556, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_F175CC3EE4DF39AF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1757033355012923, "dur": 397, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_F175CC3EE4DF39AF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1757033355013321, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1757033355013627, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1757033355014008, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1757033355014266, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UIEffect.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1757033355014668, "dur": 1056, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033355015725, "dur": 809, "ph": "X", "name": "File", "args": {"detail": "Assets\\IronSource\\Scripts\\IronSourceJSON.cs"}}, {"pid": 12345, "tid": 7, "ts": 1757033355016572, "dur": 1244, "ph": "X", "name": "File", "args": {"detail": "Assets\\IronSource\\Scripts\\IronSourceImpressionDataAndroid.cs"}}, {"pid": 12345, "tid": 7, "ts": 1757033355015725, "dur": 2351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033355018076, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033355018324, "dur": 79, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033355018453, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033355018550, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033355018972, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1757033355019330, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1757033355019163, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1757033355019792, "dur": 568, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757033355020378, "dur": 2567, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/CasualGame.Dreamteck.Utilities.dll"}}, {"pid": 12345, "tid": 7, "ts": 1757033355022957, "dur": 4410, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor-firstpass.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1757033355027380, "dur": 4210, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.AI.Navigation.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1757033355031591, "dur": 29985, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033355002868, "dur": 9695, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033355012566, "dur": 208, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1757033355012565, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_21675FD99AD58191.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1757033355013087, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1757033355012813, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_59D59D2570B707D8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1757033355013189, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_59D59D2570B707D8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1757033355013381, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1757033355013799, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1757033355014369, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1757033355014689, "dur": 1384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033355016427, "dur": 898, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-unity\\Modules\\SkeletonRenderSeparator\\SkeletonRenderSeparator.cs"}}, {"pid": 12345, "tid": 8, "ts": 1757033355016074, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033355017780, "dur": 689, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\Welcome\\DownloadAndInstallOperation.cs"}}, {"pid": 12345, "tid": 8, "ts": 1757033355017416, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033355018542, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033355019086, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1757033355019627, "dur": 738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1757033355020366, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757033355020539, "dur": 2434, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 8, "ts": 1757033355022978, "dur": 4402, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-firstpass.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1757033355027390, "dur": 3766, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/CasualGame.Dreamteck.Utilities.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1757033355031157, "dur": 30442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757033355002887, "dur": 9689, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757033355012581, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll"}}, {"pid": 12345, "tid": 9, "ts": 1757033355012695, "dur": 223, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1757033355012579, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_52C8AB7AD3D8A783.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1757033355012923, "dur": 401, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_52C8AB7AD3D8A783.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1757033355013341, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1757033355013529, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1757033355014067, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1757033355014642, "dur": 469, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757033355015112, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757033355015369, "dur": 676, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\WX\\NativeInputField\\InputField\\WXInputFieldTmpAdapter.cs"}}, {"pid": 12345, "tid": 9, "ts": 1757033355016058, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\WX\\NativeInputField\\InputField\\TmpTextInit.cs"}}, {"pid": 12345, "tid": 9, "ts": 1757033355016643, "dur": 1831, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\UIPlugins\\TableViewPlug\\Common\\RichText.cs"}}, {"pid": 12345, "tid": 9, "ts": 1757033355015303, "dur": 3216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757033355018540, "dur": 2793, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 9, "ts": 1757033355021345, "dur": 2668, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/AppleAuth.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1757033355024023, "dur": 4319, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1757033355028355, "dur": 4500, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VSCode.Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1757033355032857, "dur": 28748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033355002900, "dur": 9679, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033355012597, "dur": 213, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1757033355012582, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_78AB6F32676F7162.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1757033355012811, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033355013191, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_78AB6F32676F7162.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1757033355013385, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1757033355013522, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1757033355013654, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1757033355013899, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1757033355014733, "dur": 1530, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Editor\\spine-unity\\Modules\\Timeline\\Editor\\SpineSkeletonFlipDrawer.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757033355014453, "dur": 1848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033355016301, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033355016575, "dur": 2071, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Splines\\Components\\ObjectController.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757033355016494, "dur": 2269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033355018764, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033355018889, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1757033355019079, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757033355019263, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757033355019016, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1757033355019642, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033355019708, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1757033355020024, "dur": 392, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757033355020417, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757033355020555, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757033355019806, "dur": 1164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1757033355020970, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757033355021069, "dur": 3475, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757033355024555, "dur": 3931, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Sirenix.OdinInspector.Modules.UnityMathematics.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1757033355028499, "dur": 4038, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1757033355032538, "dur": 29076, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033355002918, "dur": 9670, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033355012614, "dur": 243, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1757033355012593, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_A6E727B0B6898769.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1757033355012865, "dur": 240, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_A6E727B0B6898769.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1757033355013107, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1757033355013106, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_C2B00A29F8F2F428.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1757033355013241, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1757033355013498, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1757033355013945, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1757033355014214, "dur": 1182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033355015494, "dur": 700, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\MonoWidgets\\HttpMono.cs"}}, {"pid": 12345, "tid": 11, "ts": 1757033355015397, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033355016801, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033355017006, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033355017224, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033355017787, "dur": 868, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\PendingChanges\\Changelists\\ChangelistMenu.cs"}}, {"pid": 12345, "tid": 11, "ts": 1757033355017432, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033355018729, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033355019085, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1757033355019262, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1757033355019520, "dur": 367, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 11, "ts": 1757033355019242, "dur": 836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1757033355020079, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033355020219, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1757033355020411, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033355020773, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1757033355020602, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1757033355021396, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757033355021534, "dur": 3506, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Sirenix.OdinInspector.Modules.UnityMathematics.dll"}}, {"pid": 12345, "tid": 11, "ts": 1757033355025050, "dur": 3973, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Wx.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1757033355029037, "dur": 4649, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1757033355033688, "dur": 27922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033355003197, "dur": 9471, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033355012683, "dur": 254, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1757033355012668, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BD6B6DA2CAED0C51.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757033355013069, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1757033355013069, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_9EFF5EAEE149B463.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757033355013145, "dur": 205, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_7BBCF5494DCBE09D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757033355013350, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1757033355013733, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1757033355014334, "dur": 1174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033355015696, "dur": 523, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Common\\Encrypt\\AesRijndael.cs"}}, {"pid": 12345, "tid": 12, "ts": 1757033355015509, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033355016235, "dur": 1120, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Hierarchy 2\\Runtime\\UIElements.cs"}}, {"pid": 12345, "tid": 12, "ts": 1757033355017438, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\AssetBundleManager\\Utility.cs"}}, {"pid": 12345, "tid": 12, "ts": 1757033355016219, "dur": 1944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033355018163, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033355018452, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033355018680, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033355019091, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757033355019295, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1757033355019892, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033355020134, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757033355020521, "dur": 711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1757033355021233, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757033355021466, "dur": 3502, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Wx.dll"}}, {"pid": 12345, "tid": 12, "ts": 1757033355024982, "dur": 3927, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1757033355029025, "dur": 4971, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1757033355033997, "dur": 27575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033355002947, "dur": 9647, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033355012610, "dur": 567, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1757033355012597, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_2D9AC79B2BBA2E00.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1757033355013184, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_2D9AC79B2BBA2E00.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1757033355013377, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1757033355013455, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1757033355013664, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1757033355014431, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1757033355014702, "dur": 639, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033355015342, "dur": 599, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\UI\\UIBGScaler.cs"}}, {"pid": 12345, "tid": 13, "ts": 1757033355015342, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033355016701, "dur": 1856, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-unity\\Asset Types\\RegionlessAttachmentLoader.cs"}}, {"pid": 12345, "tid": 13, "ts": 1757033355016107, "dur": 2497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033355018605, "dur": 609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033355019214, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/AppleAuth.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1757033355019461, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1757033355020060, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033355020122, "dur": 562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1757033355020711, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1757033355021320, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757033355021472, "dur": 2725, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/FancyScrollView.Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1757033355024208, "dur": 4047, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityWebSocket.Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1757033355028269, "dur": 4047, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1757033355032317, "dur": 29274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757033355002970, "dur": 9629, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757033355012622, "dur": 268, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1757033355012602, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_58061DC3ABA3501B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1757033355012895, "dur": 377, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_58061DC3ABA3501B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1757033355013272, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1757033355013472, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1757033355013729, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1757033355014179, "dur": 904, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757033355015870, "dur": 4509, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1757033355020393, "dur": 1709, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityWebSocket.Runtime.dll"}}, {"pid": 12345, "tid": 14, "ts": 1757033355022114, "dur": 4188, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/CasualGame.Dreamteck.Splines.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1757033355026314, "dur": 4594, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1757033355030909, "dur": 30658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033355002991, "dur": 9612, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033355012607, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1757033355012695, "dur": 214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 15, "ts": 1757033355012607, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_875FEDF767AE9596.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1757033355012915, "dur": 382, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_875FEDF767AE9596.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1757033355013314, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1757033355013599, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033355013918, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 15, "ts": 1757033355014598, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Dropdown.cs"}}, {"pid": 12345, "tid": 15, "ts": 1757033355013655, "dur": 1382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1757033355015290, "dur": 5032, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 15, "ts": 1757033355020323, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1757033355020640, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1757033355021249, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757033355021471, "dur": 3114, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1757033355024612, "dur": 4085, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/FancyScrollView.Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1757033355028709, "dur": 4237, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/CasualGame.Dreamteck.Utilities.Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1757033355032947, "dur": 28657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033355003012, "dur": 9596, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033355012623, "dur": 536, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1757033355012611, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_AD339B360B767264.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1757033355013164, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_AD339B360B767264.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1757033355013362, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1757033355013946, "dur": 448, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1757033355014422, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1757033355014678, "dur": 531, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033355015210, "dur": 616, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\CasualGame\\FlexyRing\\Script\\GameScript\\GramScripts\\RewardEntityManager.cs"}}, {"pid": 12345, "tid": 16, "ts": 1757033355015921, "dur": 1058, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\CasualGame\\FlexyRing\\Script\\FlexyRingLevelEnter.cs"}}, {"pid": 12345, "tid": 16, "ts": 1757033355015210, "dur": 1862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033355017072, "dur": 107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033355017180, "dur": 111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033355017291, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033355017399, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033355017840, "dur": 113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033355017953, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033355018466, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033355018542, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033355018992, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033355019330, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1757033355019514, "dur": 1071, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1757033355020616, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 16, "ts": 1757033355020780, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 16, "ts": 1757033355020831, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 16, "ts": 1757033355019108, "dur": 2074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1757033355021183, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757033355021435, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1757033355021648, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1757033355022208, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1757033355022273, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1757033355022510, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1757033355022799, "dur": 4009, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-firstpass.dll"}}, {"pid": 12345, "tid": 16, "ts": 1757033355026818, "dur": 4063, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1757033355030882, "dur": 30688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033355003028, "dur": 9587, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033355012620, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1757033355012694, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 17, "ts": 1757033355012617, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_D57CD8953DD5F64D.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1757033355012885, "dur": 339, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_D57CD8953DD5F64D.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1757033355013252, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_643B55DA42DFE635.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1757033355013397, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1757033355013518, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1757033355014018, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1757033355014626, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033355015076, "dur": 1585, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\System_Collections_Generic_Dictionary_2_System_String_UnityEngine_GameObject_Wrap.cs"}}, {"pid": 12345, "tid": 17, "ts": 1757033355015004, "dur": 1688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033355016692, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033355017015, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033355017301, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033355017801, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033355018086, "dur": 112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033355018198, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033355018534, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033355019086, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1757033355019641, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1757033355020010, "dur": 475, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757033355020508, "dur": 1322, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/IngameDebugConsole.Runtime.dll"}}, {"pid": 12345, "tid": 17, "ts": 1757033355021840, "dur": 3609, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.AI.Navigation.Updater.dll"}}, {"pid": 12345, "tid": 17, "ts": 1757033355025458, "dur": 3873, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.AI.Navigation.Editor.dll"}}, {"pid": 12345, "tid": 17, "ts": 1757033355029353, "dur": 4112, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1757033355033466, "dur": 28132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033355003042, "dur": 9576, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033355012630, "dur": 547, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 18, "ts": 1757033355012621, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_0EED61BE115AECA6.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1757033355013183, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_0EED61BE115AECA6.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1757033355013373, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1757033355013888, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1757033355014279, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/WxEditor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1757033355014681, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033355015160, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033355015386, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033355016140, "dur": 600, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-unity\\SkeletonUtility\\SkeletonUtility.cs"}}, {"pid": 12345, "tid": 18, "ts": 1757033355016039, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033355016834, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033355016933, "dur": 121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033355017054, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033355017172, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033355017805, "dur": 676, "ph": "X", "name": "File", "args": {"detail": "Assets\\UnityWebSocket\\Scripts\\Runtime\\Core\\CloseEventArgs.cs"}}, {"pid": 12345, "tid": 18, "ts": 1757033355017275, "dur": 1287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033355018562, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757033355018875, "dur": 2932, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 18, "ts": 1757033355021815, "dur": 3329, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.AI.Navigation.Updater.pdb"}}, {"pid": 12345, "tid": 18, "ts": 1757033355025155, "dur": 3635, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1757033355028800, "dur": 4172, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/CasualGame.Dreamteck.Utilities.Editor.pdb"}}, {"pid": 12345, "tid": 18, "ts": 1757033355032974, "dur": 28623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033355003056, "dur": 9567, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033355012644, "dur": 275, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 19, "ts": 1757033355012627, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_AEE611DA6D6531BB.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1757033355012924, "dur": 409, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_AEE611DA6D6531BB.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1757033355013334, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1757033355013633, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1757033355014042, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1757033355014639, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033355014906, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\UnityEngine_UI_But<PERSON>_ButtonClickedEventWrap.cs"}}, {"pid": 12345, "tid": 19, "ts": 1757033355014817, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033355015478, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Game\\LanguageManager.cs"}}, {"pid": 12345, "tid": 19, "ts": 1757033355015454, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033355016205, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033355016546, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033355016792, "dur": 109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033355016901, "dur": 112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033355017013, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033355017123, "dur": 112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033355017291, "dur": 1209, "ph": "X", "name": "File", "args": {"detail": "Assets\\UnityWebSocket\\Scripts\\Runtime\\Implementation\\WebGL\\WebSocketManager.cs"}}, {"pid": 12345, "tid": 19, "ts": 1757033355018777, "dur": 765, "ph": "X", "name": "File", "args": {"detail": "Assets\\UnityWebSocket\\Scripts\\Runtime\\Core\\Settings.cs"}}, {"pid": 12345, "tid": 19, "ts": 1757033355017235, "dur": 2349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033355019585, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1757033355019677, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033355019925, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1757033355020067, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1757033355020142, "dur": 658, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757033355020810, "dur": 2362, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Lofelt.NiceVibrations.dll"}}, {"pid": 12345, "tid": 19, "ts": 1757033355023178, "dur": 4397, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UIEffect-Editor.dll"}}, {"pid": 12345, "tid": 19, "ts": 1757033355027591, "dur": 5198, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/FancyScrollView.pdb"}}, {"pid": 12345, "tid": 19, "ts": 1757033355032791, "dur": 28809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033355003099, "dur": 9540, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033355012656, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 20, "ts": 1757033355012642, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_ED95F4E26287FEC9.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1757033355012865, "dur": 245, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_ED95F4E26287FEC9.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1757033355013142, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_ABC31C6E784BF4F9.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1757033355013346, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1757033355013690, "dur": 917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1757033355014607, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033355014756, "dur": 665, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Src\\Utils.cs"}}, {"pid": 12345, "tid": 20, "ts": 1757033355016731, "dur": 1829, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Src\\ObjectTranslator.cs"}}, {"pid": 12345, "tid": 20, "ts": 1757033355018765, "dur": 655, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Src\\LuaDLL.cs"}}, {"pid": 12345, "tid": 20, "ts": 1757033355019420, "dur": 740, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Src\\LuaDebugTool.cs"}}, {"pid": 12345, "tid": 20, "ts": 1757033355020171, "dur": 620, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Src\\InternalGlobals.cs"}}, {"pid": 12345, "tid": 20, "ts": 1757033355014756, "dur": 6049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757033355020865, "dur": 2082, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/AppleAuth.dll"}}, {"pid": 12345, "tid": 20, "ts": 1757033355022955, "dur": 4162, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor-firstpass.dll"}}, {"pid": 12345, "tid": 20, "ts": 1757033355027128, "dur": 3890, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/IngameDebugConsole.Runtime.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1757033355031020, "dur": 30542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033355003070, "dur": 9557, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033355012651, "dur": 231, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 21, "ts": 1757033355012630, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_6A4C4ABC6FBD89C0.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1757033355012888, "dur": 504, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_6A4C4ABC6FBD89C0.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1757033355013496, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1757033355013776, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1757033355014448, "dur": 550, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Src\\Editor\\TemplateRef.cs"}}, {"pid": 12345, "tid": 21, "ts": 1757033355014998, "dur": 559, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Src\\Editor\\LinkXmlGen\\LinkXmlGen.cs"}}, {"pid": 12345, "tid": 21, "ts": 1757033355015599, "dur": 879, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Scripts\\Sirenix\\Demos\\Editor Windows\\Scripts\\Editor\\ToolUti.cs"}}, {"pid": 12345, "tid": 21, "ts": 1757033355016576, "dur": 1986, "ph": "X", "name": "File", "args": {"detail": "Assets\\IronSource\\Editor\\IronSourceManifestProcessor.cs"}}, {"pid": 12345, "tid": 21, "ts": 1757033355014448, "dur": 4125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033355018573, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033355019097, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1757033355019193, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1757033355019520, "dur": 636, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 21, "ts": 1757033355019277, "dur": 1025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1757033355020302, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757033355020425, "dur": 1520, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/FancyScrollView.dll"}}, {"pid": 12345, "tid": 21, "ts": 1757033355021955, "dur": 3702, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UIEffect.pdb"}}, {"pid": 12345, "tid": 21, "ts": 1757033355025667, "dur": 4332, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 21, "ts": 1757033355030001, "dur": 31565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033355002820, "dur": 9722, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033355012548, "dur": 223, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1757033355012545, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F16AF7E23D838A64.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1757033355012814, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1757033355012814, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7C2C4F34C6B423AF.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1757033355012886, "dur": 343, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7C2C4F34C6B423AF.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1757033355013268, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1757033355013579, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1757033355013955, "dur": 688, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1757033355014643, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033355015109, "dur": 76, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033355015304, "dur": 1044, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\CasualGame\\FlexyRing\\Script\\GameScript\\Tools\\EffectManager.cs"}}, {"pid": 12345, "tid": 22, "ts": 1757033355015185, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033355016396, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033355016763, "dur": 1742, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Utilities\\Editor\\ResourceUtility.cs"}}, {"pid": 12345, "tid": 22, "ts": 1757033355016724, "dur": 1913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033355018637, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033355019094, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1757033355019508, "dur": 911, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033355020617, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1757033355020774, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 22, "ts": 1757033355020422, "dur": 904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1757033355021326, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033355021774, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 22, "ts": 1757033355021520, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1757033355022003, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757033355022104, "dur": 4058, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/CasualGame.Dreamteck.Splines.Editor.pdb"}}, {"pid": 12345, "tid": 22, "ts": 1757033355026173, "dur": 4053, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Lofelt.NiceVibrations.pdb"}}, {"pid": 12345, "tid": 22, "ts": 1757033355030227, "dur": 31362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033355003088, "dur": 9547, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033355012648, "dur": 252, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 23, "ts": 1757033355012638, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_328CD27C104796DE.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1757033355012906, "dur": 381, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_328CD27C104796DE.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1757033355013320, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1757033355013605, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033355013661, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1757033355014163, "dur": 762, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033355014992, "dur": 1475, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\UnityEngine_Physics2DWrap.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757033355014927, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033355016499, "dur": 885, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Splines\\Core\\Primitives\\Capsule.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757033355017797, "dur": 793, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Splines\\Components\\SplineTracer.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757033355016484, "dur": 2187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033355018671, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033355019087, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1757033355019640, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1757033355020016, "dur": 405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757033355020464, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Sirenix\\Assemblies\\Sirenix.Utilities.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757033355020556, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Sirenix\\Assemblies\\Sirenix.Utilities.Editor.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757033355020911, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757033355020425, "dur": 964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1757033355021487, "dur": 3239, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/IngameDebugConsole.Editor.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1757033355024735, "dur": 4140, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1757033355028895, "dur": 4098, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1757033355032994, "dur": 28623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033355003115, "dur": 9534, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033355012663, "dur": 509, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 24, "ts": 1757033355012651, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_90AA0436019DA739.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1757033355013178, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_90AA0436019DA739.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1757033355013369, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1757033355013650, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1757033355014115, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1757033355014693, "dur": 589, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033355015309, "dur": 733, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\CasualGame\\DLYShuiGuan\\Script\\Other\\rotater.cs"}}, {"pid": 12345, "tid": 24, "ts": 1757033355015283, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033355016315, "dur": 1061, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-csharp\\Json.cs"}}, {"pid": 12345, "tid": 24, "ts": 1757033355016196, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033355017420, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033355017936, "dur": 107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033355018044, "dur": 123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033355018167, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033355018490, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033355018541, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033355019004, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1757033355019398, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Configuration\\CloudEdition\\Welcome\\SignInWithEmailPanel.cs"}}, {"pid": 12345, "tid": 24, "ts": 1757033355019103, "dur": 827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1757033355019931, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033355020115, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1757033355020411, "dur": 789, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 24, "ts": 1757033355021209, "dur": 464, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 24, "ts": 1757033355020234, "dur": 1649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1757033355021883, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033355021942, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1757033355022424, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1757033355022488, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1757033355023624, "dur": 228, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757033355024305, "dur": 4313726, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1757033355003139, "dur": 9514, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355012665, "dur": 486, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 25, "ts": 1757033355012656, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_D8923AA791216057.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1757033355013156, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_D8923AA791216057.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1757033355013354, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 25, "ts": 1757033355013483, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 25, "ts": 1757033355013752, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355013860, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1757033355014150, "dur": 783, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355014936, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355015025, "dur": 81, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355015114, "dur": 1601, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\DG_Tweening_TweenWrap.cs"}}, {"pid": 12345, "tid": 25, "ts": 1757033355015106, "dur": 1693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355016800, "dur": 105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355016905, "dur": 102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355017008, "dur": 114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355017122, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355017232, "dur": 112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355017344, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355017460, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355017960, "dur": 107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355018067, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355018175, "dur": 112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355018470, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355018553, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355018874, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1757033355018947, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1757033355019520, "dur": 930, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 25, "ts": 1757033355019308, "dur": 1286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1757033355020642, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1757033355021029, "dur": 448, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757033355021488, "dur": 3243, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Lofelt.NiceVibrations.Editor.pdb"}}, {"pid": 12345, "tid": 25, "ts": 1757033355024741, "dur": 4099, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/IngameDebugConsole.Editor.dll"}}, {"pid": 12345, "tid": 25, "ts": 1757033355028865, "dur": 3927, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Sirenix.OdinInspector.CompatibilityLayer.pdb"}}, {"pid": 12345, "tid": 25, "ts": 1757033355032793, "dur": 28795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355003157, "dur": 9500, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355012683, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 26, "ts": 1757033355012660, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_036EBEF6E01DAAEF.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1757033355012876, "dur": 461, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_036EBEF6E01DAAEF.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1757033355013338, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1757033355013889, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1757033355014324, "dur": 1132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355015576, "dur": 755, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Framework\\ResourceManager\\AssetManager.cs"}}, {"pid": 12345, "tid": 26, "ts": 1757033355015457, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355016405, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355016839, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355016972, "dur": 113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355017085, "dur": 97, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355017182, "dur": 117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355017300, "dur": 104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355017404, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355017954, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355018069, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355018168, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355018271, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355018495, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355018549, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355018928, "dur": 610, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355019539, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1757033355020190, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1757033355020617, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1757033355020304, "dur": 905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1757033355021209, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757033355021463, "dur": 2792, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 26, "ts": 1757033355024270, "dur": 3900, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/CasualGame.Dreamteck.Splines.pdb"}}, {"pid": 12345, "tid": 26, "ts": 1757033355028178, "dur": 3349, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 26, "ts": 1757033355031528, "dur": 30045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033355003173, "dur": 9487, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033355012691, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 27, "ts": 1757033355012663, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_5733EFC1F4B68777.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757033355012862, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_5733EFC1F4B68777.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757033355013068, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1757033355013067, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_389506176DEC1286.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757033355013165, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_389506176DEC1286.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757033355013365, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 27, "ts": 1757033355013449, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 27, "ts": 1757033355013505, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1757033355013710, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 27, "ts": 1757033355014240, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1757033355014665, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033355015032, "dur": 91, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033355015712, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Scripts\\ScriptDLL\\InjectionMaterialArray.cs"}}, {"pid": 12345, "tid": 27, "ts": 1757033355015123, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033355016685, "dur": 1125, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Splines\\Editor\\SplineEditor\\Point Modules\\PointScaleModule.cs"}}, {"pid": 12345, "tid": 27, "ts": 1757033355016325, "dur": 1954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033355018279, "dur": 80, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033355018454, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033355018547, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033355019079, "dur": 452, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 27, "ts": 1757033355019535, "dur": 275, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 27, "ts": 1757033355018948, "dur": 1240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1757033355020188, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033355020317, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033355020477, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757033355020732, "dur": 779, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033355021514, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1757033355021944, "dur": 3428, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityWebSocket.Editor.dll"}}, {"pid": 12345, "tid": 27, "ts": 1757033355025383, "dur": 4167, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 27, "ts": 1757033355029552, "dur": 30082, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757033355059635, "dur": 1862, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033355003186, "dur": 9478, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033355012669, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 28, "ts": 1757033355012667, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_D699E5DF035CDE66.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1757033355012844, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_A062D8221399A90F.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1757033355013065, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_57E25D09F6FAA0C2.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1757033355013142, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_925C9BD15D5833BC.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1757033355013342, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 28, "ts": 1757033355013471, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 28, "ts": 1757033355013778, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033355014079, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1757033355014635, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033355014791, "dur": 704, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Src\\GenAttributes.cs"}}, {"pid": 12345, "tid": 28, "ts": 1757033355014791, "dur": 1653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033355016450, "dur": 2120, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Scripts\\UIEffect\\Editor\\UITransitionEffectEditor.cs"}}, {"pid": 12345, "tid": 28, "ts": 1757033355016445, "dur": 2399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757033355018873, "dur": 2609, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 28, "ts": 1757033355021489, "dur": 3425, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Lofelt.NiceVibrations.Editor.dll"}}, {"pid": 12345, "tid": 28, "ts": 1757033355024926, "dur": 3917, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Sirenix.OdinInspector.CompatibilityLayer.Editor.pdb"}}, {"pid": 12345, "tid": 28, "ts": 1757033355028870, "dur": 3959, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/AppleAuth.pdb"}}, {"pid": 12345, "tid": 28, "ts": 1757033355032831, "dur": 28759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757033359343457, "dur": 1234, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 82792, "tid": 16, "ts": 1757033363707904, "dur": 1378, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 82792, "tid": 16, "ts": 1757033363710405, "dur": 22, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 82792, "tid": 16, "ts": 1757033363710497, "dur": 8, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 82792, "tid": 16, "ts": 1757033363709311, "dur": 1092, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 82792, "tid": 16, "ts": 1757033363710445, "dur": 52, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 82792, "tid": 16, "ts": 1757033363710512, "dur": 263, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 82792, "tid": 16, "ts": 1757033363705479, "dur": 5910, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}